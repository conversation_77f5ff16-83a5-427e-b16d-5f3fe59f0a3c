/// 图片删除事件
///
/// 当用户在图片预览页面删除图片时触发此事件
/// 用于通知相关的控制器或服务处理删除的图片URL
///
/// 遵循Clean Architecture原则：
/// - 事件作为领域层的概念，不依赖具体的实现
/// - 发布者和订阅者之间解耦
/// - 支持多个订阅者处理同一事件
class ImageDeletedEvent {
  /// 被删除的图片URL列表
  ///
  /// 包含所有在当前操作中被用户删除的图片原始URL
  /// 这些URL将用于后续的S3文件清理或其他处理
  ///
  /// 特点：
  /// - 只包含原始URL，不包含临时URL
  /// - 已经过去重处理
  /// - 按删除顺序排列
  final List<String> deletedImageUrls;

  /// 删除操作的上下文信息（可选）
  ///
  /// 提供额外的上下文信息，帮助订阅者更好地处理事件
  /// 例如：删除来源、用户ID、操作时间等
  final Map<String, dynamic>? context;

  /// 构造函数
  ///
  /// [deletedImageUrls] 被删除的图片URL列表，必需参数
  /// [context] 删除操作的上下文信息，可选参数
  ///
  /// 示例：
  /// ```dart
  /// final event = ImageDeletedEvent(
  ///   ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
  ///   context: {
  ///     'source': 'asset_history_editor',
  ///     'userId': '12345',
  ///     'timestamp': DateTime.now().millisecondsSinceEpoch,
  ///   },
  /// );
  /// ```
  ImageDeletedEvent(this.deletedImageUrls, {this.context}) : assert(deletedImageUrls.isNotEmpty, '删除的图片URL列表不能为空');

  /// 获取删除的图片数量
  int get deletedCount => deletedImageUrls.length;

  /// 检查是否包含指定的URL
  bool containsUrl(String url) => deletedImageUrls.contains(url);

  /// 获取上下文中的特定值
  T? getContextValue<T>(String key) {
    if (context == null) return null;
    final value = context![key];
    return value is T ? value : null;
  }

  /// 转换为字符串表示
  @override
  String toString() {
    return 'ImageDeletedEvent(deletedCount: $deletedCount, urls: ${deletedImageUrls.join(', ')})';
  }

  /// 相等性比较
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ImageDeletedEvent) return false;

    return _listEquals(deletedImageUrls, other.deletedImageUrls) && _mapEquals(context, other.context);
  }

  @override
  int get hashCode => Object.hash(Object.hashAll(deletedImageUrls), context?.hashCode ?? 0);

  /// 列表相等性比较辅助方法
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }

  /// Map相等性比较辅助方法
  bool _mapEquals<T, U>(Map<T, U>? a, Map<T, U>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final T key in a.keys) {
      if (!b.containsKey(key) || b[key] != a[key]) return false;
    }
    return true;
  }
}
