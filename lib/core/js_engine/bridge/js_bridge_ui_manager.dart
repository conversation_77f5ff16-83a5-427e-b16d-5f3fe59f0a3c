import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// JavaScript 桥接UI管理器
///
/// 负责处理UI相关操作
class JsBridgeUiManager extends GetxService {
  /// 是否正在显示对话框
  bool _isShowAlert = false;

  /// 显示警告对话框
  ///
  /// 参数格式: [message]
  Future<dynamic> showAlert(List<dynamic> params) async {
    if (params.isEmpty) {
      return {'error': '缺少必要参数: message'};
    }

    if (_isShowAlert) {
      return {'error': '已有警告对话框正在显示'};
    }

    final message = params[0].toString();

    // 设置对话框显示状态
    _isShowAlert = true;

    try {
      return await Get.dialog(
        AlertDialog(
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: const Text('キャンセル'),
              onPressed: () {
                // 先重置状态再关闭对话框，确保状态管理的可靠性
                _isShowAlert = false;
                Get.back();
              },
            ),
          ],
        ),
      ).whenComplete(() {
        // 当对话框关闭时重置状态（双重保险）
        _isShowAlert = false;
      });
    } catch (e) {
      // 异常情况下也要重置状态
      _isShowAlert = false;
      rethrow;
    }
  }
}
