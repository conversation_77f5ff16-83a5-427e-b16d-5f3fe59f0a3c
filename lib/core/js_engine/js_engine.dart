/// JavaScript 执行引擎
///
/// 提供统一的 JavaScript 执行能力，包括：
/// - JavaScript 代码执行
/// - 数据管理和同步
/// - 批量更新机制
/// - 异步函数转换
/// - Flutter-JavaScript 桥接

// 核心执行器
export 'executor/js_executor.dart';
export 'executor/js_async_transformer.dart';

// 桥接服务
export 'bridge/js_flutter_bridge_service.dart';
export 'bridge/js_bridge_function_registry.dart';
export 'bridge/js_bridge_state_manager.dart';
export 'bridge/js_bridge_ui_manager.dart';
export 'bridge/js_bridge_data_manager.dart';

// 数据管理
export 'data/cow_data_manager.dart';
export 'data/batch_update_manager.dart';
export 'data/js_executor_context.dart';

// 服务
export 'services/js_executor_binding_service.dart';
