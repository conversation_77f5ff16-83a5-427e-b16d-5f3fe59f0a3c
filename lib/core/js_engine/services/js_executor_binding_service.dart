import 'package:asset_force_mobile_v2/core/js_engine/bridge/js_bridge_data_manager.dart';
import 'package:asset_force_mobile_v2/core/js_engine/bridge/js_bridge_function_registry.dart';
import 'package:asset_force_mobile_v2/core/js_engine/bridge/js_bridge_state_manager.dart';
import 'package:asset_force_mobile_v2/core/js_engine/bridge/js_bridge_ui_manager.dart';
import 'package:asset_force_mobile_v2/core/js_engine/bridge/js_flutter_bridge_service.dart';
import 'package:asset_force_mobile_v2/core/js_engine/data/js_executor_context.dart';
import 'package:asset_force_mobile_v2/core/js_engine/executor/js_executor.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:get/get.dart';

/// JsExecutor 依赖注册服务
///
/// 提供统一的 JsExecutor 相关依赖注册功能，避免在多个 binding 文件中重复相同的注册代码
/// 支持带标签和无标签两种注册方式，满足不同场景的需求
class JsExecutorBindingService {
  /// 私有构造函数，防止实例化
  JsExecutorBindingService._();

  /// 注册带标签的 JsExecutor 依赖
  ///
  /// 用于需要独立上下文的场景，每个标签对应一个独立的 JsExecutorContext 实例
  ///
  /// 参数:
  /// * [tag] - 用于区分不同实例的标签，通常使用页面ID或其他唯一标识
  ///
  /// 返回:
  /// * [JsExecutorContext] - 注册的 JsExecutorContext 实例
  ///
  /// 使用场景:
  /// * 需要为每个页面或组件创建独立的 JavaScript 执行上下文
  /// * 避免不同页面间的数据污染
  static JsExecutorContext registerWithTag(String tag) {
    // 注册 JsExecutorContext（带标签）
    Get.lazyPut(() => JsExecutorContext(), tag: tag);
    final jsExecutorContext = Get.find<JsExecutorContext>(tag: tag);

    // 注册 JsExecutor 相关依赖（全局共享，使用 fenix 确保不被意外销毁）
    _registerJsExecutorDependencies();

    // 注册 JsFlutterBridgeService（带标签，使用特定的 JsExecutorContext）
    Get.lazyPut(
      () => JsFlutterBridgeService(
        functionRegistry: Get.find<JsBridgeFunctionRegistry>(),
        dataManager: Get.find<JsBridgeDataManager>(),
        stateManager: Get.find<JsBridgeStateManager>(),
        uiManager: Get.find<JsBridgeUiManager>(),
        assetRepository: Get.find<AssetRepository>(),
        jsExecutorContext: jsExecutorContext,
      ),
      tag: tag,
      fenix: true,
    );

    // 注册 JsExecutor（带标签）
    Get.lazyPut(() => JsExecutor(Get.find<JsFlutterBridgeService>(tag: tag)), tag: tag);

    return jsExecutorContext;
  }

  /// 注册无标签的 JsExecutor 依赖
  ///
  /// 用于共享上下文的场景，所有使用者共享同一个 JsExecutorContext 实例
  ///
  /// 返回:
  /// * [JsExecutorContext] - 注册的 JsExecutorContext 实例
  ///
  /// 使用场景:
  /// * 多个组件需要共享同一个 JavaScript 执行上下文
  /// * 简化依赖管理，减少内存占用
  static JsExecutorContext registerWithoutTag() {
    // 注册 JsExecutorContext（无标签）
    Get.lazyPut(() => JsExecutorContext());
    final jsExecutorContext = Get.find<JsExecutorContext>();

    // 注册 JsExecutor 相关依赖（全局共享）
    _registerJsExecutorDependencies();

    // 注册 JsFlutterBridgeService（无标签）
    Get.lazyPut(
      () => JsFlutterBridgeService(
        functionRegistry: Get.find<JsBridgeFunctionRegistry>(),
        dataManager: Get.find<JsBridgeDataManager>(),
        stateManager: Get.find<JsBridgeStateManager>(),
        uiManager: Get.find<JsBridgeUiManager>(),
        assetRepository: Get.find<AssetRepository>(),
        jsExecutorContext: jsExecutorContext,
      ),
      fenix: true,
    );

    // 注册 JsExecutor（无标签）
    Get.lazyPut(() => JsExecutor(Get.find<JsFlutterBridgeService>()));

    return jsExecutorContext;
  }

  /// 注册 JsExecutor 核心依赖
  ///
  /// 注册 JsExecutor 运行所需的核心组件，这些组件通常是全局共享的
  /// 使用 fenix: true 确保这些依赖不会被意外销毁
  ///
  /// 注册的依赖包括:
  /// * JsBridgeFunctionRegistry - JavaScript 函数注册表
  /// * JsBridgeDataManager - 数据管理器
  /// * JsBridgeStateManager - 状态管理器
  /// * JsBridgeUiManager - UI 管理器
  static void _registerJsExecutorDependencies() {
    // 检查是否已经注册，避免重复注册
    if (!Get.isRegistered<JsBridgeFunctionRegistry>()) {
      Get.lazyPut(() => JsBridgeFunctionRegistry(), fenix: true);
    }

    if (!Get.isRegistered<JsBridgeDataManager>()) {
      Get.lazyPut(() => JsBridgeDataManager(), fenix: true);
    }

    if (!Get.isRegistered<JsBridgeStateManager>()) {
      Get.lazyPut(() => JsBridgeStateManager(), fenix: true);
    }

    if (!Get.isRegistered<JsBridgeUiManager>()) {
      Get.lazyPut(() => JsBridgeUiManager(), fenix: true);
    }
  }

  /// 检查 JsExecutor 依赖是否已注册
  ///
  /// 参数:
  /// * [tag] - 可选的标签，如果提供则检查带标签的依赖
  ///
  /// 返回:
  /// * [bool] - 如果所有必需的依赖都已注册则返回 true
  static bool isRegistered({String? tag}) {
    if (tag != null) {
      return Get.isRegistered<JsExecutorContext>(tag: tag) &&
          Get.isRegistered<JsFlutterBridgeService>(tag: tag) &&
          Get.isRegistered<JsExecutor>(tag: tag);
    } else {
      return Get.isRegistered<JsExecutorContext>() &&
          Get.isRegistered<JsFlutterBridgeService>() &&
          Get.isRegistered<JsExecutor>();
    }
  }

  /// 只注册 JsExecutorContext（带标签）
  ///
  /// 用于只需要上下文数据存储功能，而不需要完整 JavaScript 执行功能的场景
  ///
  /// 参数:
  /// * [tag] - 用于区分不同实例的标签
  ///
  /// 返回:
  /// * [JsExecutorContext] - 注册的 JsExecutorContext 实例
  ///
  /// 使用场景:
  /// * 只需要临时存储页面数据，不需要执行 JavaScript 代码
  /// * 轻量级的数据上下文管理
  static JsExecutorContext registerContextOnly(String tag) {
    Get.lazyPut(() => JsExecutorContext(), tag: tag);
    return Get.find<JsExecutorContext>(tag: tag);
  }

  /// 清理 JsExecutor 依赖
  ///
  /// 参数:
  /// * [tag] - 可选的标签，如果提供则清理带标签的依赖
  ///
  /// 注意:
  /// * 核心依赖（JsBridgeFunctionRegistry 等）不会被清理，因为它们可能被其他实例使用
  /// * 只清理 JsExecutorContext、JsFlutterBridgeService 和 JsExecutor
  static void cleanup({String? tag}) {
    if (tag != null) {
      Get.delete<JsExecutor>(tag: tag);
      Get.delete<JsFlutterBridgeService>(tag: tag);
      Get.delete<JsExecutorContext>(tag: tag);
    } else {
      Get.delete<JsExecutor>();
      Get.delete<JsFlutterBridgeService>();
      Get.delete<JsExecutorContext>();
    }
  }
}
