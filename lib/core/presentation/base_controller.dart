import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_button_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_reset_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event_bus.dart';
import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/global_loading.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:get/get.dart';

// 新增一个枚举，定义错误处理的方式
enum ErrorHandlingMode {
  dialog, // 显示对话框
  toast, // 显示底部短暂提示
}

abstract class BaseController extends GetxController {
  late dynamic _argumnets;
  late Map<String, String?> _parameter;

  /// 获取 Get.arguments 参数. (该参数自动在 onInit 之后进行缓存)
  get argumnets => _argumnets;

  /// 获取 Get.parameters 参数. (该参数自动在 onInit 之后进行缓存)
  get parameter => _parameter;

  @override
  onInit() {
    super.onInit();

    _argumnets = Get.arguments;
    _parameter = Get.parameters;
  }

  NavigationService get navigationService => Get.find<NavigationService>();

  // 修改后的异常处理方法，添加处理模式参数
  Future<void> handleException(
    dynamic exception, [
    StackTrace? stackTrace,
    ErrorHandlingMode mode = ErrorHandlingMode.dialog,
  ]) async {
    String message;
    hideLoading();

    if (exception is BusinessException) {
      // 业务异常：显示用户可读的消息
      message = exception.message;
    } else if (exception is SystemException) {
      // 系统异常：记录技术日志，向用户展示友好的消息
      message = exception.message;
    } else {
      // 未知异常：记录并显示通用错误消息
      message = 'システムエラーが発生しました。管理者にご連絡ください。';
    }

    // 根据模式选择不同的展示方式
    switch (mode) {
      case ErrorHandlingMode.dialog:
        // 弹出错误提示框
        await CommonDialog.show(content: message, title: 'エラー', type: DialogType.error);
        break;

      case ErrorHandlingMode.toast:
        // 使用CommonDialog.showToast显示短暂提示
        CommonDialog.showToast(message);
        break;
    }
  }

  Future<void> showLoading() async {
    await GlobalLoading.show();
  }

  void hideLoading() {
    GlobalLoading.dismiss();
  }

  /// 获取当前是否需要显示导航栏
  /// 子类可以重写此方法来决定是否显示导航栏
  bool shouldShowNavigationBar() {
    return true;
  }

  /// 配置导航栏的状态
  /// 根据shouldShowNavigationBar()的返回值调用TabsController中的showBar或hideBar方法
  void configureNavigationBarVisibility() {
    try {
      // 尝试获取TabsController的实例
      final tabsController = Get.find<TabsController>();

      // 根据shouldShowNavigationBar的返回值来决定显示或隐藏导航栏
      if (shouldShowNavigationBar()) {
        tabsController.showBar();
      } else {
        tabsController.hideBar();
      }
    } catch (e) {
      // TabsController不存在或其他错误时，记录但不影响主流程
      LogUtil.d('无法配置导航栏状态: $e');
    }
  }

  /// 重置 Overlay 状态, 会彻底关闭掉 overlay 按钮及悬浮的webview.
  void resetOverlay() {
    EventBus.fire(OverlayResetEvent());
    EventBus.fire(OverlayButtonEvent(OverlayButtonEventType.logout));
  }

  /// 执行带Loading状态的异步操作
  ///
  /// 这是一个通用的异步操作执行器，提供统一的loading状态管理和错误处理
  ///
  /// [operation] - 要执行的异步操作
  /// [showLoadingDialog] - 是否显示全局Loading对话框，默认为true
  /// [errorMessage] - 自定义错误消息前缀，用于更具体的错误描述
  /// [errorHandlingMode] - 错误处理模式，默认为对话框模式
  /// [localLoadingState] - 局部loading状态的RxBool对象，当showLoadingDialog为false时使用
  /// [onError] - 额外的错误处理回调，在标准错误处理之前执行
  ///
  /// 使用示例：
  /// ```dart
  /// // 使用全局loading对话框
  /// await executeWithLoading(() async {
  ///   await someAsyncOperation();
  /// });
  ///
  /// // 使用局部loading状态和自定义错误处理
  /// await executeWithLoading(
  ///   () async { await someAsyncOperation(); },
  ///   showLoadingDialog: false,
  ///   localLoadingState: uiState.isLoading,
  ///   onError: (error, message) {
  ///     // 自定义错误处理逻辑，如更新UI状态
  ///     uiState.isError.value = true;
  ///     uiState.errorMessage.value = message;
  ///   },
  /// );
  /// ```
  Future<void> executeWithLoading(
    Future<void> Function() operation, {
    bool showLoadingDialog = true,
    String? errorMessage,
    ErrorHandlingMode errorHandlingMode = ErrorHandlingMode.dialog,
    RxBool? localLoadingState,
    void Function(dynamic error, String message)? onError,
  }) async {
    try {
      // 开始loading状态
      if (showLoadingDialog) {
        await showLoading();
      } else if (localLoadingState != null) {
        localLoadingState.value = true;
      }

      // 执行异步操作
      await operation();
    } catch (e, stackTrace) {
      // 构建错误消息
      final message = errorMessage != null ? '$errorMessage: $e' : e.toString();

      // 记录错误日志
      LogUtil.e(message, stackTrace: stackTrace);

      // 执行自定义错误处理回调（如果提供）
      if (onError != null) {
        onError(e, message);
      }

      // 处理异常
      await handleException(e, stackTrace, errorHandlingMode);
    } finally {
      // 结束loading状态
      if (showLoadingDialog) {
        hideLoading();
      } else if (localLoadingState != null) {
        localLoadingState.value = false;
      }
    }
  }
}
