import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:flutter/material.dart';

/// 通用按钮组件
///
/// 提供统一的按钮样式和行为，支持多种按钮类型和自定义配置
/// 基于项目现有的按钮实现模式，整合了各种常用的按钮样式
///
/// 字体粗细使用指南：
/// - 使用 [CommonButton.fontWeightLight] 表示细体文字
/// - 使用 [CommonButton.fontWeightNormal] 表示正常文字
/// - 使用 [CommonButton.fontWeightMedium] 表示中等粗细文字
/// - 使用 [CommonButton.fontWeightSemiBold] 表示半粗体文字
/// - 使用 [CommonButton.fontWeightBold] 表示粗体文字
class CommonButton extends StatelessWidget {
  /// 字体粗细常量 - 细体 (300)
  static const FontWeight fontWeightLight = FontWeight.w300;

  /// 字体粗细常量 - 正常 (400)
  static const FontWeight fontWeightNormal = FontWeight.w400;

  /// 字体粗细常量 - 中等 (500)
  static const FontWeight fontWeightMedium = FontWeight.w500;

  /// 字体粗细常量 - 半粗体 (600)
  static const FontWeight fontWeightSemiBold = FontWeight.w600;

  /// 字体粗细常量 - 粗体 (700)
  static const FontWeight fontWeightBold = FontWeight.w700;

  /// 按钮文本
  final String text;

  /// 按钮点击回调
  final VoidCallback? onPressed;

  /// 按钮类型
  final CommonButtonType type;

  /// 按钮大小
  final CommonButtonSize size;

  /// 自定义背景色（优先级高于类型默认色）
  final Color? backgroundColor;

  /// 自定义文字颜色（优先级高于类型默认色）
  final Color? textColor;

  /// 自定义边框颜色（优先级高于类型默认色）
  final Color? borderColor;

  /// 边框宽度
  final double borderWidth;

  /// 圆角半径
  final double borderRadius;

  /// 字体大小（优先级高于尺寸默认值）
  final double? fontSize;

  /// 字体粗细
  final FontWeight? fontWeight;

  /// 按钮宽度（null 表示自适应）
  final double? width;

  /// 按钮高度（优先级高于尺寸默认值）
  final double? height;

  /// 内边距
  final EdgeInsets? padding;

  /// 是否禁用
  final bool disabled;

  /// 左侧图标
  final IconData? leftIcon;

  /// 右侧图标
  final IconData? rightIcon;

  /// 图标大小
  final double iconSize;

  /// 图标与文字间距
  final double iconSpacing;

  /// 阴影高度
  final double elevation;

  const CommonButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = CommonButtonType.primary,
    this.size = CommonButtonSize.medium,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.fontSize,
    this.fontWeight,
    this.width,
    this.height,
    this.padding,
    this.disabled = false,
    this.leftIcon,
    this.rightIcon,
    this.iconSize = 16.0,
    this.iconSpacing = 8.0,
    this.elevation = 0,
  });

  /// 主要按钮（蓝色背景，白色文字）
  factory CommonButton.primary({
    required String text,
    VoidCallback? onPressed,
    CommonButtonSize size = CommonButtonSize.medium,
    double? width,
    double? height,
    double? fontSize,
    FontWeight? fontWeight,
    bool disabled = false,
    IconData? leftIcon,
    IconData? rightIcon,
  }) {
    return CommonButton(
      text: text,
      onPressed: onPressed,
      type: CommonButtonType.primary,
      size: size,
      width: width,
      height: height,
      fontSize: fontSize,
      fontWeight: fontWeight,
      disabled: disabled,
      leftIcon: leftIcon,
      rightIcon: rightIcon,
    );
  }

  /// 次要按钮（白色背景，蓝色边框和文字）
  factory CommonButton.secondary({
    required String text,
    VoidCallback? onPressed,
    CommonButtonSize size = CommonButtonSize.medium,
    double? width,
    double? height,
    double? fontSize,
    FontWeight? fontWeight,
    bool disabled = false,
    IconData? leftIcon,
    IconData? rightIcon,
  }) {
    return CommonButton(
      text: text,
      onPressed: onPressed,
      type: CommonButtonType.secondary,
      size: size,
      width: width,
      height: height,
      fontSize: fontSize,
      fontWeight: fontWeight,
      disabled: disabled,
      leftIcon: leftIcon,
      rightIcon: rightIcon,
    );
  }

  /// 文字按钮（透明背景，蓝色文字）
  factory CommonButton.text({
    required String text,
    VoidCallback? onPressed,
    CommonButtonSize size = CommonButtonSize.medium,
    Color? textColor,
    double? fontSize,
    FontWeight? fontWeight,
    bool disabled = false,
    IconData? leftIcon,
    IconData? rightIcon,
  }) {
    return CommonButton(
      text: text,
      onPressed: onPressed,
      type: CommonButtonType.text,
      size: size,
      textColor: textColor,
      fontSize: fontSize,
      fontWeight: fontWeight,
      disabled: disabled,
      leftIcon: leftIcon,
      rightIcon: rightIcon,
    );
  }

  /// 危险按钮（红色背景，白色文字）
  factory CommonButton.danger({
    required String text,
    VoidCallback? onPressed,
    CommonButtonSize size = CommonButtonSize.medium,
    double? width,
    double? height,
    double? fontSize,
    FontWeight? fontWeight,
    bool disabled = false,
    IconData? leftIcon,
    IconData? rightIcon,
  }) {
    return CommonButton(
      text: text,
      onPressed: onPressed,
      type: CommonButtonType.danger,
      size: size,
      width: width,
      height: height,
      fontSize: fontSize,
      fontWeight: fontWeight,
      disabled: disabled,
      leftIcon: leftIcon,
      rightIcon: rightIcon,
    );
  }

  @override
  Widget build(BuildContext context) {
    final buttonStyle = _getButtonStyle();
    final buttonChild = _buildButtonChild();

    Widget button;
    switch (type) {
      case CommonButtonType.primary:
      case CommonButtonType.danger:
        button = ElevatedButton(onPressed: disabled ? null : onPressed, style: buttonStyle, child: buttonChild);
        break;
      case CommonButtonType.secondary:
        button = OutlinedButton(onPressed: disabled ? null : onPressed, style: buttonStyle, child: buttonChild);
        break;
      case CommonButtonType.text:
        // 对于文字按钮，如果设置了自定义背景色且不是透明色，则使用ElevatedButton
        // 这样可以确保背景色能够正确显示，解决TextButton不支持背景色的问题
        if (backgroundColor != null && backgroundColor != Colors.transparent) {
          button = ElevatedButton(onPressed: disabled ? null : onPressed, style: buttonStyle, child: buttonChild);
        } else {
          // 使用标准的TextButton，保持透明背景
          button = TextButton(onPressed: disabled ? null : onPressed, style: buttonStyle, child: buttonChild);
        }
        break;
    }

    return SizedBox(width: width, height: height ?? _getDefaultHeight(), child: button);
  }

  /// 构建按钮内容
  Widget _buildButtonChild() {
    final List<Widget> children = [];

    // 左侧图标
    if (leftIcon != null) {
      children.add(Icon(leftIcon, size: iconSize));
      if (text.isNotEmpty) {
        children.add(SizedBox(width: iconSpacing));
      }
    }

    // 文字
    if (text.isNotEmpty) {
      children.add(
        Text(
          text,
          style: TextStyle(
            fontSize: fontSize ?? _getDefaultFontSize(),
            fontWeight: fontWeight ?? _getDefaultFontWeight(),
            color: _getTextColor(),
          ),
        ),
      );
    }

    // 右侧图标
    if (rightIcon != null) {
      if (text.isNotEmpty) {
        children.add(SizedBox(width: iconSpacing));
      }
      children.add(Icon(rightIcon, size: iconSize));
    }

    return Row(mainAxisSize: MainAxisSize.min, mainAxisAlignment: MainAxisAlignment.center, children: children);
  }

  /// 获取按钮样式
  ///
  /// 根据按钮类型和自定义属性生成对应的ButtonStyle
  /// 特别处理文字按钮的背景色设置，确保自定义背景色能够正确显示
  ButtonStyle _getButtonStyle() {
    return ButtonStyle(
      backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (disabled || states.contains(WidgetState.disabled)) {
          return Colors.grey.shade300;
        }
        return _getBackgroundColor();
      }),
      foregroundColor: WidgetStateProperty.all(_getTextColor()),
      side: WidgetStateProperty.all(BorderSide(color: _getBorderColor(), width: borderWidth)),
      shape: WidgetStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(borderRadius))),
      padding: WidgetStateProperty.all(padding ?? _getDefaultPadding()),
      minimumSize: WidgetStateProperty.all(Size.zero),
      // 对于文字按钮，如果设置了自定义背景色且不是透明色，则显示阴影
      // 否则保持无阴影效果，确保视觉效果的一致性
      elevation: WidgetStateProperty.all(_getElevation()),
    );
  }

  /// 获取背景色
  Color _getBackgroundColor() {
    if (backgroundColor != null) return backgroundColor!;

    switch (type) {
      case CommonButtonType.primary:
        return AppTheme.darkBlueColor;
      case CommonButtonType.secondary:
        return Colors.white;
      case CommonButtonType.text:
        return Colors.transparent;
      case CommonButtonType.danger:
        return Colors.redAccent;
    }
  }

  /// 获取文字颜色
  Color _getTextColor() {
    if (textColor != null) return textColor!;

    switch (type) {
      case CommonButtonType.primary:
      case CommonButtonType.danger:
        return Colors.white;
      case CommonButtonType.secondary:
      case CommonButtonType.text:
        return AppTheme.darkBlueColor;
    }
  }

  /// 获取边框颜色
  Color _getBorderColor() {
    if (borderColor != null) return borderColor!;

    switch (type) {
      case CommonButtonType.primary:
      case CommonButtonType.danger:
        return Colors.transparent;
      case CommonButtonType.secondary:
        return AppTheme.darkBlueColor;
      case CommonButtonType.text:
        return Colors.transparent;
    }
  }

  /// 获取按钮阴影高度
  ///
  /// 根据按钮类型和背景色设置决定阴影效果：
  /// - 文字按钮：如果设置了自定义背景色且不是透明色，则显示阴影；否则无阴影
  /// - 其他按钮类型：显示标准阴影效果
  double _getElevation() {
    return elevation;
  }

  /// 获取默认高度
  double _getDefaultHeight() {
    switch (size) {
      case CommonButtonSize.small:
        return 32.0;
      case CommonButtonSize.medium:
        return 40.0;
      case CommonButtonSize.large:
        return 48.0;
    }
  }

  /// 获取默认字体大小
  double _getDefaultFontSize() {
    switch (size) {
      case CommonButtonSize.small:
        return 14.0;
      case CommonButtonSize.medium:
        return 16.0;
      case CommonButtonSize.large:
        return 18.0;
    }
  }

  /// 获取默认字体粗细
  ///
  /// 根据按钮类型返回合适的默认字体粗细：
  /// - 主要按钮和危险按钮：使用中等粗细(w500)，突出重要性
  /// - 次要按钮和文字按钮：使用正常粗细(w400)，保持简洁
  FontWeight _getDefaultFontWeight() {
    switch (type) {
      case CommonButtonType.primary:
      case CommonButtonType.danger:
        return FontWeight.w500; // 中等粗细，突出重要操作
      case CommonButtonType.secondary:
      case CommonButtonType.text:
        return FontWeight.w400; // 正常粗细，保持简洁
    }
  }

  /// 获取默认内边距
  EdgeInsets _getDefaultPadding() {
    switch (size) {
      case CommonButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0);
      case CommonButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0);
      case CommonButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0);
    }
  }
}

/// 按钮类型枚举
enum CommonButtonType {
  /// 主要按钮（蓝色背景）
  primary,

  /// 次要按钮（白色背景，蓝色边框）
  secondary,

  /// 文字按钮（透明背景）
  text,

  /// 危险按钮（红色背景）
  danger,
}

/// 按钮尺寸枚举
enum CommonButtonSize {
  /// 小尺寸
  small,

  /// 中等尺寸
  medium,

  /// 大尺寸
  large,
}
