// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:get/get.dart';

import 'package:asset_force_mobile_v2/features/action/action_tabs/presentation/bindings/update_binding.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/presentation/pages/update_page.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/bindings/complete_binding.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/bindings/progress_binding.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/pages/complete_list_page.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/pages/progress_list_page.dart';
import 'package:asset_force_mobile_v2/features/action/new/presentation/bindings/list_new_binding.dart';
import 'package:asset_force_mobile_v2/features/action/new/presentation/pages/list_new_page.dart';
import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/bindings/temporarily_binding.dart';
import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/pages/temporarily_page.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/bindings/af_customize_view_bindings.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/bindings/scan_barcode_list_binding.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/bindings/tab_binding.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/pages/scan_barcode_list_page.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/pages/tabs_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_appurtenances_Information/presentation/bindings/asset_appurtenances_information_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_appurtenances_Information/presentation/pages/asset_appurtenances_information_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/bindings/asset_category_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/pages/category_search_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/bindings/asset_detail_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/pages/asset_detail_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/presentation/bindings/asset_history_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/presentation/bindings/asset_history_editor_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/presentation/pages/asset_history_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/presentation/pages/asset_hostory_editor_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/bindings/asset_list_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/pages/asset_list_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/presentations/bindings/asset_new_create_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/presentations/pages/asset_new_create_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_relation_add/presentation/bindings/asset_relation_add_binging.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_relation_add/presentation/pages/asset_relation_add_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_relation_list/presentation/bindings/relation_asset_list_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_relation_list/presentation/pages/relation_asset_list_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/presentation/bindings/asset_schedule_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/presentation/pages/asset_schedule_page.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_type/presentation/bindings/asset_type_list_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_type/presentation/pages/asset_type_list_page.dart';
import 'package:asset_force_mobile_v2/features/asset_alert/presentation/bindings/asset_alert_binding.dart';
import 'package:asset_force_mobile_v2/features/asset_alert/presentation/pages/asset_alert_page.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/bindings/home_binding.dart';
import 'package:asset_force_mobile_v2/features/image_preview/presentation/bindings/image_preview_binding.dart';
import 'package:asset_force_mobile_v2/features/image_preview/presentation/pages/image_preview_page.dart';
import 'package:asset_force_mobile_v2/features/list_selector/presentation/bindings/list_selector_bindings.dart';
import 'package:asset_force_mobile_v2/features/list_selector/presentation/pages/list_selector_page.dart';
import 'package:asset_force_mobile_v2/features/location_select/presentation/bindings/location_select_binding.dart';
import 'package:asset_force_mobile_v2/features/location_select/presentation/pages/location_select_page.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/bindings/login_add_new_sms_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/pages/login_add_new_sms_page.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/presentation/bindings/login_opt_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/presentation/pages/login_opt_page.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/bindings/login_reset_password_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/pages/login_reset_password_page.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/bindings/login_sso_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/bindings/login_sso_list_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/pages/login_sso_list_page.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/pages/login_sso_page.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/presentation/bindings/login_traditional_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/presentation/pages/login_traditional_page.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/presentation/bindings/tenant_binding.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/presentation/pages/tenant_page.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/presentation/bindings/map_picker_binding.dart';
import 'package:asset_force_mobile_v2/features/map_picker_view/presentation/pages/map_picker_page.dart';
import 'package:asset_force_mobile_v2/features/master_select/presentation/binding/master_select_binding.dart';
import 'package:asset_force_mobile_v2/features/master_select/presentation/pages/master_select_page.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/bindings/account_binding.dart';
import 'package:asset_force_mobile_v2/features/me/account/presentation/pages/account_page.dart';
import 'package:asset_force_mobile_v2/features/me/font_size_setting/presentation/bindings/font_size_setting_binding.dart';
import 'package:asset_force_mobile_v2/features/me/font_size_setting/presentation/pages/font_size_setting_page.dart';
import 'package:asset_force_mobile_v2/features/me/message/presentation/bindings/message_binding.dart';
import 'package:asset_force_mobile_v2/features/me/message/presentation/pages/message_page.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/bindings/mypage_binding.dart';
import 'package:asset_force_mobile_v2/features/me/mypage/presentation/pages/my_page.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/bindings/scan_setting_binding.dart';
import 'package:asset_force_mobile_v2/features/me/scan_setting/presentation/pages/scan_setting_page.dart';
import 'package:asset_force_mobile_v2/features/ocr/presentation/bindings/ocr_binding.dart';
import 'package:asset_force_mobile_v2/features/ocr/presentation/pages/car_number_ocr_page.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/bindings/search_condition_binding.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/pages/search_condition_page.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/custom_icon_preview_widget.dart';
import 'package:asset_force_mobile_v2/features/splash/splash_page.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/presentation/bindings/user_group_selector_binding.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/presentation/pages/user_group_selector_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/presentation/bindings/workflow_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/presentation/pages/workflow_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/bindings/application_scan_list_bindings.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/bindings/application_view_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/bindings/workflow_application_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/pages/application_scan_list.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/pages/application_view_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/bindings/approval_no_claim_list_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/bindings/approval_scan_list_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/bindings/approval_view_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/bindings/unfinished_scanned_list_bindings.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/bindings/workflow_approval_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/pages/approval_no_claim_list_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/pages/approval_scan_list_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/pages/approval_view_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/pages/unfinished_scanned_list_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/choose_tantousha_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/new_application_form_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/new_application_submit_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/workflow_new_application_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/pages/choose_tantousha_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/pages/new_application_form_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/pages/new_application_submit_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/bindings/edit_form_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/bindings/workflow_scan_list_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/pages/edit_form_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/pages/workflow_scan_list.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_tabs/presentation/bindings/workflow_tabs_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_tabs/presentation/pages/workflow_tabs_page.dart';

class AutoRoutes {
  AutoRoutes._();

  static const String actionCompleteList = '/action/complete/list';
  static const String actionListNew = '/action/list/new';
  static const String actionProgressList = '/action/progress/list';
  static const String actionTemporarily = '/action/temporarily';
  static const String appTab = '/app_tab';
  static const String appTabScanViewBarcodeList = '/app_tab/scan_view_barcode_list';
  static const String appTabWorkflowTabs = '/app_tab/workflow/tabs';
  static const String appTabsAction = '/app_tabs/action';
  static const String applicationScanList = '/application/scan_list';
  static const String applicationView = '/application/view';
  static const String approvalNoClaimScanList = '/approval/no_claim_scan_list';
  static const String approvalScanList = '/approval/scan_list';
  static const String approvalUnFinishScanList = '/approval/un_finish_scan_list';
  static const String approvalView = '/approval/view';
  static const String assetCreate = '/asset/create';
  static const String assetAlert = '/asset_alert';
  static const String assetAppurtenancesInformation = '/asset_appurtenances_information';
  static const String assetCategory = '/asset_category';
  static const String assetDetailId = '/asset_detail/:id';
  static const String assetHistory = '/asset_history';
  static const String assetHistoryEditor = '/asset_history_editor';
  static const String assetList = '/asset_list';
  static const String assetRelationAdd = '/asset_relation_add';
  static const String assetSchedule = '/asset_schedule';
  static const String assetType = '/asset_type';
  static const String chooseTantousha = '/chooseTantousha';
  static const String devCustomIconPreview = '/dev/customIconPreview';
  static const String editPage = '/editPage';
  static const String imagePreview = '/image_preview';
  static const String listSelector = '/list_selector';
  static const String locationSelectPage = '/location_select_page';
  static const String login = '/login';
  static const String loginOpt = '/login/opt';
  static const String loginOptAddNewSms = '/login/opt/add_new_sms';
  static const String loginResetPassword = '/login/reset_password';
  static const String loginSso = '/login/sso';
  static const String loginTenant = '/login/tenant';
  static const String loginSsoList = '/login_sso/list';
  static const String mapPicker = '/map_picker';
  static const String masterSelect = '/master_select';
  static const String me = '/me';
  static const String meAccountSetting = '/me/account_setting';
  static const String meFontSizeSetting = '/me/font_size_setting';
  static const String meMessagePage = '/me/message_page';
  static const String meScanSetting = '/me/scan_setting';
  static const String newForm = '/newForm';
  static const String newFormSubmit = '/newForm/submit';
  static const String ocrCarNumberEditor = '/ocr/car_number_editor';
  static const String relationAssetListId = '/relation_asset_list/:id';
  static const String search = '/search';
  static const String splash = '/splash';
  static const String userGroupSelector = '/user_group_selector';
  static const String workflow = '/workflow';
  static const String workflowScanList = '/workflowScanList';

  static final List<GetPage> pages = [
    GetPage(
      name: actionCompleteList,
      page: () {
        return const CompleteListPage();
      },
      binding: CompleteBinding(),
      popGesture: false,
    ),
    GetPage(
      name: actionListNew,
      page: () {
        return const ActionListNewPage();
      },
      binding: ListNewBinding(),
      popGesture: false,
    ),
    GetPage(
      name: actionProgressList,
      page: () {
        return const ProgressListPage();
      },
      binding: ProgressBinding(),
      popGesture: false,
    ),
    GetPage(
      name: actionTemporarily,
      page: () {
        return const TemporarilyPage();
      },
      binding: TemporarilyBinding(),
      popGesture: false,
    ),
    GetPage(
      name: appTab,
      page: () {
        return const TabsPage();
      },
      binding: TabsBinding(),
      bindings: [AssetListBinding(), MyHomeBinding(), WorkflowBinding(), UpdateBinding()],
      popGesture: false,
    ),
    GetPage(
      name: appTabScanViewBarcodeList,
      page: () {
        return const ScanBarcodeListPage();
      },
      binding: ScanBarcodeListBinding(),
      popGesture: false,
    ),
    GetPage(
      name: appTabWorkflowTabs,
      page: () {
        return const WorkflowTabsPage();
      },
      bindings: [
        WorkflowTabsBinding(),
        WorkflowNewApplicationBinding(),
        WorkflowApplicationBinding(),
        WorkflowApprovalBinding(),
      ],
      popGesture: false,
    ),
    GetPage(
      name: appTabsAction,
      page: () {
        return const UpdatePage();
      },
      binding: UpdateBinding(),
      popGesture: false,
    ),
    GetPage(
      name: applicationScanList,
      page: () {
        return ApplicationScanList();
      },
      binding: ApplicationScanListBindings(),
      popGesture: false,
    ),
    GetPage(
      name: applicationView,
      page: () {
        return ApplicationViewPage();
      },
      bindings: [ApplicationViewBinding()],
      popGesture: false,
    ),
    GetPage(
      name: approvalNoClaimScanList,
      page: () {
        return ApprovalNoClaimListPage();
      },
      bindings: [ApprovalNoClaimListBinding()],
      popGesture: false,
    ),
    GetPage(
      name: approvalScanList,
      page: () {
        return ApprovalScanListPage();
      },
      bindings: [ApprovalScanListBinding()],
      popGesture: false,
    ),
    GetPage(
      name: approvalUnFinishScanList,
      page: () {
        return UnfinishedScannedListPage();
      },
      binding: UnfinishedScannedListBindings(),
      popGesture: false,
    ),
    GetPage(
      name: approvalView,
      page: () {
        return const ApprovalViewPage();
      },
      bindings: [ApprovalViewBinding()],
      popGesture: false,
    ),
    GetPage(
      name: assetCreate,
      page: () {
        return const AssetNewCreatePage();
      },
      bindings: [AssetNewCreateBinding(), AfCustomizeViewBindings()],
      popGesture: false,
    ),
    GetPage(
      name: assetAlert,
      page: () {
        return const AssetAlertPage();
      },
      binding: AssetAlertBinding(),
      popGesture: false,
    ),
    GetPage(
      name: assetAppurtenancesInformation,
      page: () {
        return const AssetAppurtenancesInformationPage();
      },
      binding: AssetAppurtenancesInformationBinding(),
      popGesture: false,
    ),
    GetPage(
      name: assetCategory,
      page: () {
        return const CategorySearchPage();
      },
      binding: AssetCategoryBinding(),
      popGesture: false,
    ),
    GetPage(
      name: assetDetailId,
      page: () {
        final id = Get.parameters['id'] ?? '';
        return AssetDetailPage(id: id);
      },
      bindings: [AssetDetailBinding(), AfCustomizeViewBindings()],
      popGesture: false,
    ),
    GetPage(
      name: assetHistory,
      page: () {
        return const AssetHistoryPage();
      },
      binding: AssetHistoryBinding(),
      popGesture: false,
    ),
    GetPage(
      name: assetHistoryEditor,
      page: () {
        return const AssetHistoryEditorPage();
      },
      bindings: [AfCustomizeViewBindings(), AssetHistoryEditorBinding()],
      popGesture: false,
    ),
    GetPage(
      name: assetList,
      page: () {
        return const AssetListPage();
      },
      binding: AssetListBinding(),
      popGesture: false,
    ),
    GetPage(
      name: assetRelationAdd,
      page: () {
        return const AssetRelationAddPage();
      },
      bindings: [AssetRelationAddBinding()],
      popGesture: false,
    ),
    GetPage(
      name: assetSchedule,
      page: () {
        return const AssetSchedulePage();
      },
      bindings: [AfCustomizeViewBindings(), AssetScheduleBinding()],
      popGesture: false,
    ),
    GetPage(
      name: assetType,
      page: () {
        return const AssetTypeListPage();
      },
      binding: AssetTypeListBinding(),
      popGesture: false,
    ),
    GetPage(
      name: chooseTantousha,
      page: () {
        return ChooseTantoushaPage();
      },
      binding: ChooseTantoushaBinding(),
      popGesture: false,
    ),
    GetPage(
      name: devCustomIconPreview,
      page: () {
        return const CustomIconPreviewWidget();
      },
      popGesture: false,
    ),
    GetPage(
      name: editPage,
      page: () {
        return EditFormPage();
      },
      bindings: [EditFormBinding(), AfCustomizeViewBindings()],
      popGesture: false,
    ),
    GetPage(
      name: imagePreview,
      page: () {
        return const ImagePreviewPage();
      },
      binding: ImagePreviewBinding(),
      popGesture: false,
    ),
    GetPage(
      name: listSelector,
      page: () {
        return const ListSelectorPage();
      },
      bindings: [ListSelectorBindings()],
      popGesture: false,
    ),
    GetPage(
      name: locationSelectPage,
      page: () {
        return const LocationSelectPage();
      },
      binding: LocationSelectBinding(),
      popGesture: false,
    ),
    GetPage(
      name: login,
      page: () {
        return const LoginTraditionalPage();
      },
      binding: LoginTraditionalBinding(),
      popGesture: false,
    ),
    GetPage(
      name: loginOpt,
      page: () {
        return const LoginOptPage();
      },
      binding: LoginOptBinding(),
      popGesture: false,
    ),
    GetPage(
      name: loginOptAddNewSms,
      page: () {
        return const LoginAddNewSMSPage();
      },
      binding: LoginAddNewSmsBinding(),
      popGesture: false,
    ),
    GetPage(
      name: loginResetPassword,
      page: () {
        return const LoginResetPasswordPage();
      },
      binding: LoginResetPasswordBinding(),
      popGesture: false,
    ),
    GetPage(
      name: loginSso,
      page: () {
        return const LoginSsoPage();
      },
      binding: LoginSsoBinding(),
      popGesture: false,
    ),
    GetPage(
      name: loginTenant,
      page: () {
        return const TenantPage();
      },
      binding: TenantBinding(),
      popGesture: false,
    ),
    GetPage(
      name: loginSsoList,
      page: () {
        return const LoginSsoListPage();
      },
      binding: LoginSsoListBinding(),
      popGesture: false,
    ),
    GetPage(
      name: mapPicker,
      page: () {
        return const MapPickerPage();
      },
      binding: MapPickerBinding(),
      popGesture: false,
    ),
    GetPage(
      name: masterSelect,
      page: () {
        return const MasterSelectPage();
      },
      bindings: [MasterSelectBinding()],
      popGesture: false,
    ),
    GetPage(
      name: me,
      page: () {
        return const MyPage();
      },
      binding: MyPageBinding(),
      popGesture: false,
    ),
    GetPage(
      name: meAccountSetting,
      page: () {
        return const AccountPage();
      },
      binding: AccountBinding(),
      popGesture: false,
    ),
    GetPage(
      name: meFontSizeSetting,
      page: () {
        return const FontSizeSettingsPage();
      },
      binding: FontSizeSettingBinding(),
      popGesture: false,
    ),
    GetPage(
      name: meMessagePage,
      page: () {
        return const MessagePage();
      },
      binding: MessageBinding(),
      popGesture: false,
    ),
    GetPage(
      name: meScanSetting,
      page: () {
        return const ScanSettingsPage();
      },
      binding: ScanSettingBinding(),
      popGesture: false,
    ),
    GetPage(
      name: newForm,
      page: () {
        return NewApplicationFormPage();
      },
      bindings: [NewApplicationFormBinding(), AfCustomizeViewBindings()],
      popGesture: false,
    ),
    GetPage(
      name: newFormSubmit,
      page: () {
        return NewApplicationSubmitPage();
      },
      bindings: [NewApplicationSubmitBinding()],
      popGesture: false,
    ),
    GetPage(
      name: ocrCarNumberEditor,
      page: () {
        return const CarNumberOcrPage();
      },
      binding: OcrBinding(),
      popGesture: false,
    ),
    GetPage(
      name: relationAssetListId,
      page: () {
        final id = Get.parameters['id'] ?? '';
        return RelationAssetListPage(id: id);
      },
      binding: RelationAssetListBinding(),
      popGesture: false,
    ),
    GetPage(
      name: search,
      page: () {
        return const SearchConditionPage();
      },
      binding: SearchConditionBinding(),
      popGesture: false,
    ),
    GetPage(
      name: splash,
      page: () {
        return const SplashPage();
      },
      popGesture: false,
    ),
    GetPage(
      name: userGroupSelector,
      page: () {
        return const UserGroupSelectorPage();
      },
      binding: UserGroupSelectorBinding(),
      popGesture: false,
    ),
    GetPage(
      name: workflow,
      page: () {
        return const WorkflowPage();
      },
      binding: WorkflowBinding(),
      popGesture: false,
    ),
    GetPage(
      name: workflowScanList,
      page: () {
        return WorkflowScanList();
      },
      binding: WorkflowScanListBinding(),
      popGesture: false,
    ),
  ];
}
