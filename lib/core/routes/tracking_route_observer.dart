import 'package:asset_force_mobile_v2/core/routes/route_tacker.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/overlay/floating_overlay_manager.dart';
import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';

class TrackingRouteObserver extends NavigatorObserver {
  final int id;

  TrackingRouteObserver(this.id);

  bool _isPopup(Route? route) {
    final result = route is PopupRoute || route is DialogRoute;
    LogUtil.d('isPopup  ${route?.settings.name}: $result');
    return result;
  }

  bool _isModalRoute(Route route) {
    // 1. 检查明确的modal类型
    if (route is DialogRoute || route is ModalBottomSheetRoute || route is PopupRoute) return true;

    // 系统picker类型
    final routeString = route.toString();
    return routeString.contains('DatePicker') ||
        routeString.contains('TimePicker') ||
        routeString.contains('CupertinoModalPopup');
  }

  bool _hasActiveModal() {
    if (navigator == null) return false;

    return navigator!.widget.pages.any((page) => page is MaterialPage && _isModalRoute(page as Route));
  }

  @override
  void didPush(Route route, Route? previousRoute) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final overlay = navigator?.overlay;
      if (overlay != null) {
        FloatingOverlayManager().init(overlay.context, overlay: overlay);

        LogUtil.d('FloatingOverlayManager didPush ${route.settings.name}: ${_isModalRoute(route)}');
        if (_isModalRoute(route)) {
          if (!_hasActiveModal()) {
            FloatingOverlayManager().hideButton();
          }
        }
      }
    });
    if (_isPopup(route)) return;
    RouteTracker.updateCurrentRoute(id, route.settings.name);
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    LogUtil.d('FloatingOverlayManager didPop ${route.settings.name}: ${_isModalRoute(route)}');
    if (_isModalRoute(route)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!_hasActiveModal()) {
          FloatingOverlayManager().showButton();
        }
      });
    }
    if (_isPopup(route)) return;
    RouteTracker.updateCurrentRoute(id, previousRoute?.settings.name);
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    if (newRoute != null && _isPopup(newRoute)) return;

    RouteTracker.updateCurrentRoute(id, newRoute?.settings.name);
  }
}
