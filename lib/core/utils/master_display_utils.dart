import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';

/// Master 显示工具类
///
/// 提供 Master 数据显示相关的工具方法，确保兼容性的同时
/// 解决显示名称为空的问题。
class MasterDisplayUtils {
  /// 获取安全的 Master 显示名称
  ///
  /// 按优先级返回：itemDisplayName -> itemName -> 默认值
  ///
  /// 参数：
  /// - [masterDisplay] Master 显示项模型
  /// - [defaultName] 默认名称，当其他名称都为空时使用
  ///
  /// 返回：
  /// - 非空的显示名称字符串
  static String getSafeMasterDisplayName(MasterDisplayItemModel? masterDisplay, {String defaultName = ''}) {
    if (masterDisplay == null) {
      LogUtil.w('MasterDisplayUtils - masterDisplay为空，使用默认名称: $defaultName');
      return defaultName;
    }

    // 优先使用 itemDisplayName
    if (masterDisplay.itemDisplayName?.isNotEmpty == true) {
      return masterDisplay.itemDisplayName!;
    }

    // 次选 itemName
    if (masterDisplay.itemName?.isNotEmpty == true) {
      LogUtil.d('MasterDisplayUtils - itemDisplayName为空，使用itemName: ${masterDisplay.itemName}');
      return masterDisplay.itemName!;
    }

    // 最后使用默认名称
    LogUtil.w('MasterDisplayUtils - itemDisplayName和itemName都为空，使用默认名称: $defaultName');
    return defaultName;
  }

  /// 确保 Master 显示项有有效的显示名称
  ///
  /// 如果 itemDisplayName 为空，会尝试用 itemName 填充
  /// 如果两者都为空，会设置一个默认名称
  ///
  /// 这个方法直接修改传入的对象，确保兼容性
  ///
  /// 参数：
  /// - [masterDisplay] Master 显示项模型
  /// - [index] 项的索引，用于生成默认名称
  /// - [defaultNamePrefix] 默认名称前缀
  ///
  /// 返回：
  /// - 确保后的显示名称
  static String ensureValidDisplayName(
    MasterDisplayItemModel masterDisplay,
    int index, {
    String defaultNamePrefix = 'Master項目',
  }) {
    // 如果 itemDisplayName 不为空，直接返回
    if (masterDisplay.itemDisplayName?.isNotEmpty == true) {
      return masterDisplay.itemDisplayName!;
    }

    // 如果 itemDisplayName 为空但 itemName 不为空，用 itemName 填充
    if (masterDisplay.itemName?.isNotEmpty == true) {
      masterDisplay.itemDisplayName = masterDisplay.itemName;
      LogUtil.d('MasterDisplayUtils - 索引 $index: 用itemName填充itemDisplayName: ${masterDisplay.itemName}');
      return masterDisplay.itemDisplayName!;
    }

    // 如果两者都为空，设置默认名称
    final defaultName = '$defaultNamePrefix${index + 1}';
    masterDisplay.itemDisplayName = defaultName;
    LogUtil.w('MasterDisplayUtils - 索引 $index: itemDisplayName和itemName都为空，设置默认名称: $defaultName');
    return defaultName;
  }

  /// 批量确保 Master 显示项列表的显示名称有效性
  ///
  /// 参数：
  /// - [masterDisplayList] Master 显示项列表
  /// - [defaultNamePrefix] 默认名称前缀
  ///
  /// 返回：
  /// - 处理的项目数量
  static int ensureValidDisplayNames(
    List<MasterDisplayItemModel> masterDisplayList, {
    String defaultNamePrefix = 'Master項目',
  }) {
    int processedCount = 0;

    for (int i = 0; i < masterDisplayList.length; i++) {
      final originalDisplayName = masterDisplayList[i].itemDisplayName;
      final newDisplayName = ensureValidDisplayName(masterDisplayList[i], i, defaultNamePrefix: defaultNamePrefix);

      if (originalDisplayName != newDisplayName) {
        processedCount++;
      }
    }

    if (processedCount > 0) {
      LogUtil.d('MasterDisplayUtils - 批量处理完成，处理了 $processedCount 个空名称项');
    }

    return processedCount;
  }

  /// 验证 Master 显示项是否有有效的名称
  ///
  /// 参数：
  /// - [masterDisplay] Master 显示项模型
  ///
  /// 返回：
  /// - true: 有有效名称
  /// - false: 没有有效名称
  static bool hasValidDisplayName(MasterDisplayItemModel? masterDisplay) {
    if (masterDisplay == null) {
      return false;
    }

    return (masterDisplay.itemDisplayName?.isNotEmpty == true) || (masterDisplay.itemName?.isNotEmpty == true);
  }
}
