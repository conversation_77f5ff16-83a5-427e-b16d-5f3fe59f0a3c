import 'package:json_annotation/json_annotation.dart';

part 'label_and_icon_model.g.dart'; // 自动生成的文件名

@JsonSerializable()
class LabelAndIconModel {
  @JsonKey(name: 'actionLabel')
  String? actionLabel;
  @Json<PERSON>ey(name: 'actionIcon')
  String? actionIcon;

  LabelAndIconModel({this.actionLabel, this.actionIcon});

  factory LabelAndIconModel.fromJson(Map<String, dynamic> json) => _$LabelAndIconModelFromJson(json);

  Map<String, dynamic> toJson() => _$LabelAndIconModelToJson(this);
}
