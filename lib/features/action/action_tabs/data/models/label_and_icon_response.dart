import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/data/models/label_and_icon_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'label_and_icon_response.g.dart'; // 自动生成的文件名

@JsonSerializable()
class LabelAndIconResponse extends BaseResponse {
  @JsonKey(name: 'labelAndIcon')
  List<LabelAndIconModel>? labelAndIcon;

  LabelAndIconResponse({required super.code, required super.msg, this.labelAndIcon});

  factory LabelAndIconResponse.fromJson(Map<String, dynamic> json) => _$LabelAndIconResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LabelAndIconResponseToJson(this);
}
