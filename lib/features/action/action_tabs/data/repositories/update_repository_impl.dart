import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/data/models/label_and_icon_model.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/data/models/label_and_icon_response.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/domain/repositories/update_repository.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';

class UpdateRepositoryImpl with RepositoryErrorHandler implements UpdateRepository {
  final DioUtil dioUtil;

  UpdateRepositoryImpl({required this.dioUtil});

  @override
  Future<List<LabelAndIconModel>> loadActionLabelAndIcon() async {
    return executeRepositoryTask<List<LabelAndIconModel>>(() async {
      final response = await dioUtil.get(GlobalVariable.getActionLabelAndIcon);
      if (response.isSuccess()) {
        final dataResp = LabelAndIconResponse.fromJson(response.data);
        if (dataResp.isSuccess()) {
          return dataResp.labelAndIcon ?? [];
        }
      }
      LogUtil.e('loadActionLabelAndIcon Failed: ${response.statusCode}');
      throw BusinessException(
        'Failed to load action label and icon due to API response status: ${response.statusCode}',
      );
    }, 'Failed to load action label and icon');
  }

  @override
  Future<int> loadActionListCount(int processType, {int skip = 0, int rows = 0}) async {
    return executeRepositoryTask<int>(() async {
      final queryObj = {'processType': processType, 'skip': skip, 'rows': rows};
      final response = await dioUtil.get(GlobalVariable.getAssetActionData, queryParams: queryObj);
      if (response.isSuccess()) {
        final dataResp = BaseResponse.fromJson(response.data);
        if (dataResp.isSuccess()) {
          final tmp = response.data as Map<String, dynamic>?;
          if (tmp != null) {
            return (tmp['count'] as int?) ?? 0;
          }
        }
      }
      LogUtil.e('loadActionListCount Failed: ${response.statusCode}');
      throw BusinessException('Failed to load action list count due to API response status: ${response.statusCode}');
    }, 'Failed to load action list count');
  }

  @override
  Future<LabelAndIconModel> getMobileGetAssetListByProcessId() {
    // TODO: implement getMobileGetAssetListByProcessId
    throw UnimplementedError();
  }
}
