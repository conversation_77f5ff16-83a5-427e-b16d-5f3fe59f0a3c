import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/action_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/get_authority_repository.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/data/models/label_and_icon_model.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/domain/repositories/update_repository.dart';

class UpdateUsecase implements UseCase<LoadDataResult, NoParams> {
  final UpdateRepository updateRepository;
  final GetAuthorityRepository authorityRepository;
  final ActionRepository actionRepository;

  UpdateUsecase({required this.updateRepository, required this.authorityRepository, required this.actionRepository});

  @override
  Future<LoadDataResult> call(NoParams params) async {
    try {
      final menu = {'処理一覧': '38', '一時保存': '127', '実行完了': '128'};
      final Map<String, bool> permissionObj = {};
      final authorityInfo = await authorityRepository.getAuthorityInfo();

      final labelAndIconList = await updateRepository.loadActionLabelAndIcon();

      menu.forEach((key, value) {
        permissionObj[key] = authorityInfo.any((permission) => permission.functionId == value.toString());
      });

      final isShowActionList = (permissionObj['処理一覧'] == true) ? await _isShowActionList(permissionObj) : false;

      var processingListCount = 0;
      if (permissionObj['一時保存'] == true) {
        try {
          processingListCount = await updateRepository.loadActionListCount(0);
        } catch (e) {
          processingListCount = 0;
        }
      }

      return LoadDataResult(
        authorityInfo: permissionObj,
        isShowActionList: isShowActionList,
        labelAndIconList: labelAndIconList,
        processingListCount: processingListCount,
      );
    } catch (e) {
      throw BusinessException('データの読み込みに失敗しました');
    }
  }

  Future<bool> _isShowActionList(Map<String, bool> permissionObj) async {
    bool isShowActionList = false;

    final actionResp = await actionRepository.getActionListBySearchKey(ActionListQuery(skip: 0, row: 1, searchKey: ''));

    if (actionResp.isSuccess() && (actionResp.assetActionList?.isNotEmpty ?? false)) {
      isShowActionList = true;
    }

    return isShowActionList;
  }
}

class LoadDataResult {
  Map<String, bool> authorityInfo;
  bool isShowActionList;
  List<LabelAndIconModel> labelAndIconList;
  int processingListCount;

  bool success;
  String msg;

  LoadDataResult({
    this.authorityInfo = const {},
    this.isShowActionList = false,
    this.labelAndIconList = const [],
    this.processingListCount = 0,
    this.success = true,
    this.msg = '',
  });

  Map<String, dynamic> toMap() {
    return {
      'authorityInfo': authorityInfo,
      'isShowActionList': isShowActionList,
      'labelAndIconList': labelAndIconList.map((e) => e.toJson()).toList(),
      'processingListCount': processingListCount,
      'success': success,
      'msg': msg,
    };
  }
}
