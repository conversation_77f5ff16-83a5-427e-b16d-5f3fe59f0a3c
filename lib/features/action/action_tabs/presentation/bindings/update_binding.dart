import 'package:asset_force_mobile_v2/core/extensions/getx_extension.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/data/repositories/update_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/domain/repositories/update_repository.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/domain/usecases/update_usecase.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/presentation/controllers/update_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/action_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/get_authority_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/action_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/get_authority_repository.dart';
import 'package:get/get.dart';

class UpdateBinding extends Bindings {
  @override
  void dependencies() {
    // 1. 首先注册所有repositories（使用lazyPutFenix确保不会被意外销毁）
    Get.lazyPutFenix<GetAuthorityRepository>(() => GetAuthorityRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix<ActionRepository>(() => ActionRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix<UpdateRepository>(() => UpdateRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    // 2. 然后注册usecase（依赖于repositories）
    Get.lazyPutFenix<UpdateUsecase>(
      () => UpdateUsecase(
        updateRepository: Get.find<UpdateRepository>(),
        authorityRepository: Get.find<GetAuthorityRepository>(),
        actionRepository: Get.find<ActionRepository>(),
      ),
    );

    // 3. 最后注册controller（依赖于usecase）
    Get.lazyPutFenix<UpdateController>(() => UpdateController(updateUsecase: Get.find<UpdateUsecase>()));
  }
}
