import 'package:asset_force_mobile_v2/core/presentation/loading_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/domain/usecases/update_usecase.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/presentation/models/update_ui_state.dart';
import 'package:asset_force_mobile_v2/features/action/new/presentation/controllers/list_new_page_param.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:get/get.dart';

class UpdateController extends LoadingController {
  UpdateUsecase updateUsecase;

  UpdateController({required this.updateUsecase});

  var state = UpdateUIState().obs;

  Future<void> refreshData() async {
    await fetchData(null);
  }

  @override
  Future<void> fetchData(dynamic params) async {
    final loadDatResult = await updateUsecase.call(const NoParams());
    state.value = UpdateUIState.fromMap(loadDatResult.toMap());

    state.value.labelAndIconList.sort((a, b) {
      int retVal = 0;
      if (a.actionLabel.compareTo(b.actionLabel) > 0) {
        retVal = 1;
      } else if (a.actionLabel.compareTo(b.actionLabel) < 0) {
        retVal = -1;
      }
      return retVal;
    });
    state.refresh();
  }

  void toActionList(String actionListType, String actionLabel) {
    Get.toNamed(
      AutoRoutes.actionListNew,
      id: SharedNavBarEnum.actionList.navigatorId,
      arguments: ListNewPageParam(actionLabel: actionLabel, actionListType: actionListType),
    );
  }

  void toTemporarilyPage() {
    Get.toNamed(AutoRoutes.actionProgressList, id: SharedNavBarEnum.actionList.navigatorId);
  }

  void toCompletePage() {
    Get.toNamed(AutoRoutes.actionCompleteList, id: SharedNavBarEnum.actionList.navigatorId);
  }
}
