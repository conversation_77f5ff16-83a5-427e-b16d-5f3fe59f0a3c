class ActionLabelIcon {
  final String actionLabel;
  final String actionIcon;

  ActionLabelIcon({required this.actionLabel, required this.actionIcon});

  factory ActionLabelIcon.fromMap(Map<String, dynamic> map) {
    return ActionLabelIcon(actionLabel: map['actionLabel'] ?? '', actionIcon: map['actionIcon'] ?? '');
  }
}

class UpdateUIState {
  Map<String, bool> authorityInfo;
  bool isShowActionList;
  List<ActionLabelIcon> labelAndIconList;
  int processingListCount;

  UpdateUIState({
    this.authorityInfo = const {},
    this.isShowActionList = false,
    this.labelAndIconList = const [],
    this.processingListCount = 0,
  });

  factory UpdateUIState.fromMap(Map<String, dynamic> map) {
    return UpdateUIState(
      authorityInfo: Map<String, bool>.from(map['authorityInfo'] ?? {}),
      isShowActionList: map['isShowActionList'] ?? false,
      labelAndIconList: List<ActionLabelIcon>.from(
        map['labelAndIconList']?.map((x) => ActionLabelIcon.fromMap(x)) ?? [],
      ),
      processingListCount: map['processingListCount'] ?? 0,
    );
  }

  @override
  String toString() {
    return 'UpdateUIState{authorityInfo: $authorityInfo, isShowActionList: $isShowActionList, labelAndIconList: $labelAndIconList, processingListCount: $processingListCount}';
  }
}
