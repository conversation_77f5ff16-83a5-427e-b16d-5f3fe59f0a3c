import 'package:asset_force_mobile_v2/core/theme/font_icon.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/presentation/bindings/update_binding.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/presentation/controllers/update_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('app_tabs/action', binding: UpdateBinding)
class UpdatePage extends GetView<UpdateController> {
  const UpdatePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('一括更新処理'), centerTitle: true, automaticallyImplyLeading: false),
      body: SafeArea(
        child: Obx(() {
          return RefreshIndicator(
            onRefresh: controller.refreshData,
            child: ListView(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              children: [
                _buildSectionTitle('新規実行'),
                if (controller.isLoading.value)
                  const Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                ...controller.state.value.labelAndIconList.map(
                  (item) => _buildMenuItem(
                    icon: item.actionIcon,
                    label: item.actionLabel,
                    onTap: () => controller.toActionList('2', item.actionLabel),
                  ),
                ),
                if (controller.state.value.isShowActionList && controller.state.value.authorityInfo['処理一覧'] == true)
                  _buildMenuItem(
                    icon: 'zmdi-format-list-bulleted',
                    label: '処理一覧',
                    onTap: () => controller.toActionList('1', '処理一覧'),
                  ),
                const SizedBox(height: 30),
                _buildSectionTitle('一時保存・実行中・実行完了'),
                if (controller.state.value.authorityInfo['一時保存'] == true)
                  _buildMenuItem(
                    icon: 'zmdi-floppy',
                    label: '一時保存・実行中',
                    count: controller.state.value.processingListCount.toString(),
                    onTap: () => controller.toTemporarilyPage(),
                  ),
                if (controller.state.value.authorityInfo['実行完了'] == true)
                  _buildMenuItem(icon: 'zmdi-check-all', label: '実行完了', onTap: () => controller.toCompletePage()),
                const SizedBox(height: 20),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, color: Colors.white, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildMenuItem({required String icon, required String label, String? count, required VoidCallback onTap}) {
    return Card(
      color: Colors.white.withAlpha(217),
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        radius: 8,
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 左侧图标区域
              Container(
                width: 60,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(8), bottomLeft: Radius.circular(8)),
                ),
                constraints: const BoxConstraints(minHeight: 60),
                child: Icon(FontIcons.getIcon(icon), color: const Color(0xFF0B3E86), size: 26),
              ),
              // 右侧内容区域
              Expanded(
                child: Container(
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(topRight: Radius.circular(8), bottomRight: Radius.circular(8)),
                  ),
                  constraints: const BoxConstraints(minHeight: 60),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
                      ),
                      if (count != null)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE6004D),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(count, style: const TextStyle(color: Colors.white, fontSize: 12)),
                        ),
                      const SizedBox(width: 8),
                      const Icon(Icons.chevron_right, color: Color(0xFF202020), size: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
