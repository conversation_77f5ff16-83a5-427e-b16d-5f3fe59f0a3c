import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/action/list/domain/repositories/complete_repository.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_asset_action_response.dart';

class CompleteRepositoryImpl extends CompleteRepository with RepositoryErrorHandler {
  final DioUtil dioUtil;

  CompleteRepositoryImpl(this.dioUtil);

  @override
  Future<SharedAssetActionResponse> getActionSaveListData(int processType, int skip, int row, String searchText) async {
    return executeRepositoryTask<SharedAssetActionResponse>(() async {
      final response = await dioUtil.get(
        GlobalVariable.actionListSearch,
        queryParams: {'type': processType, 'skip': skip, 'rows': row, 'searchText': searchText},
      );
      if (response.isSuccess()) {
        final dataResp = SharedAssetActionResponse.fromJson(response.data);
        if (dataResp.isSuccess()) {
          return dataResp;
        }
      }
      LogUtil.e('getActionSaveListData Failed: ${response.statusCode}');
      throw BusinessException('Failed to get action save list data due to API response status: ${response.statusCode}');
    }, 'Failed to get action save list data');
  }
}
