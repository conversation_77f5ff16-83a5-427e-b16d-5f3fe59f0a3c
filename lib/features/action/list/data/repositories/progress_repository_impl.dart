// import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
// import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/action/list/domain/repositories/progress_repository.dart';
// import 'package:dio/dio.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';

class ProgressRepositoryImpl extends ProgressRepository with RepositoryErrorHandler {
  final DioUtil dioUtil;

  ProgressRepositoryImpl(this.dioUtil);

  @override
  Future<bool> deleteActionByAssetActionId(int assetActionId, int processId) async {
    return executeRepositoryTask<bool>(() async {
      final response = await dioUtil.post(
        GlobalVariable.actionAssetActionListDelete,
        data: {'assetActionId': assetActionId, 'processId': processId},
        useFormUrlEncoded: true,
      );
      LogUtil.d('deleteActionByAssetActionId response: $response');
      if (response.isSuccess()) {
        final dataResp = BaseResponse.fromJson(response.data);
        return dataResp.isSuccess();
      }
      return false;
    }, 'Failed to delete action by asset action ID');
  }
}
