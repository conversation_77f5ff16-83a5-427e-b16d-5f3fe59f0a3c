import 'dart:convert';

import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/action/list/domain/repositories/complete_repository.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/models/asset_action_ui_model.dart';

class CompleteUsecase implements UseCase<LoadDataResult, CompleteQueryParam> {
  final CompleteRepository repository;

  CompleteUsecase(this.repository);

  @override
  Future<LoadDataResult> call(CompleteQueryParam params) async {
    try {
      final resp = await repository.getActionSaveListData(
        params.processType,
        params.skip,
        params.row,
        params.searchText,
      );
      if (resp.isSuccess()) {
        final dataList =
            resp.assetActionList
                ?.map((value) {
                  var count = 0;
                  final str = value.assetList ?? '';
                  if (str.isNotEmpty) {
                    try {
                      final t = jsonDecode(str);
                      if (t is List) {
                        count = t.length;
                      }
                    } catch (e) {
                      count = 0;
                    }
                  }

                  return AssetActionUIModel(assetTotal: count, assetActionData: value);
                })
                .whereType<AssetActionUIModel>()
                .toList() ??
            [];

        return LoadDataResult(count: dataList.length, dataList: dataList, hasMoreData: resp.moreThenLimit ?? false);
      }
    } catch (e) {
      // failed.
    }
    return LoadDataResult(count: 0, dataList: [], hasMoreData: false, success: false, msg: 'データの読み込みに失敗しました');
  }
}

class LoadDataResult {
  int count;
  List<AssetActionUIModel> dataList;
  bool hasMoreData;

  bool success;
  String msg;

  LoadDataResult({
    required this.count,
    required this.dataList,
    this.hasMoreData = true,
    this.success = true,
    this.msg = '',
  });
}

class CompleteQueryParam {
  int processType;
  int skip;
  int row;
  String searchText;

  CompleteQueryParam({required this.processType, required this.skip, required this.row, this.searchText = ''});
}
