import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/loading_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/features/action/list/domain/usecases/complete_usecase.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/models/asset_action_ui_model.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/models/complete_ui_state.dart';
import 'package:asset_force_mobile_v2/features/search/domain/enums/search_type.dart';
import 'package:asset_force_mobile_v2/features/search/domain/usecases/get_search_conditions_usecase.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/common_search_bar_widget.dart';
import 'package:get/get.dart';

/// 执行完了 和 一时保存 共用
abstract class ActionListController extends LoadingController {
  final CompleteUsecase useCase;

  var state = CompleteUIState().obs;

  SearchPageType searchEnum;

  int processType;
  int row;

  var currentPage = 1;

  ActionListController(
    this.useCase, {
    this.processType = 0,
    this.row = 15,
    this.searchEnum = SearchPageType.actionPage,
  });

  /// [params] is a boolean value to indicate whether to clear the list before fetching data.
  @override
  Future<void> fetchData(dynamic params) async {
    final s = (currentPage - 1) * row;

    final result = await useCase.call(
      CompleteQueryParam(processType: processType, skip: s, row: row, searchText: state.value.searchKey.value),
    );

    if (!result.success) {
      handleException(BusinessException(result.msg));
      isLoadingError.value = true;
      return;
    }
    if (params == true) {
      state.value.actionList.clear();
    }
    state.value.actionList.addAll(result.dataList);
    state.value.noMoreData.value = !result.hasMoreData;
  }

  Future<void> updateSearchKey(String value) async {
    state.value.searchKey.value = value;
    await onRefresh();
  }

  Future<void> onRefresh() async {
    if (isLoading.value) return;
    currentPage = 1;
    state.value.noMoreData.value = false;
    await fetchData(true);
  }

  Future<void> onLoadMore() async {
    if (isLoading.value || state.value.noMoreData.value) return;
    currentPage++;
    await fetchData(false);
  }

  // todo 此处需要处理 上一个搜索没结束， 下一个搜索就触发了 的情况。 ionic 是不管上一次是否结束， 每次都触发新的。
  void onTapSearchBarClear() {
    updateSearchKey('');
    Get.find<CommonSearchBarController>().setSearchKey(state.value.searchKey.value);
  }

  Future<void> onTapSearchBar() async {
    final result = await Get.toNamed(
      AutoRoutes.search,
      arguments: GetSearchConditionParams(searchEnum: searchEnum, searchKey: '', searchId: null, assetTypeId: null),
    );
    if (result != null) {
      updateSearchKey(result.searchKey);
      Get.find<CommonSearchBarController>().setSearchKey(state.value.searchKey.value);
    }
  }

  Future<void> toNextTaskClick({required AssetActionUIModel model});
}
