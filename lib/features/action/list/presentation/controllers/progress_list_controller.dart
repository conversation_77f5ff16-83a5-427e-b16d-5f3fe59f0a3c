import 'dart:convert';

import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/action_scan_service.dart';
import 'package:asset_force_mobile_v2/core/services/common_dialog_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/action/list/domain/usecases/progress_usecase.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/controllers/action_list_controller.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/models/asset_action_ui_model.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/search/domain/enums/search_type.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_arinfo.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_action_amount_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:get/get.dart';

class ProgressListController extends ActionListController {
  ProgressUsecase progressUsecase;
  GetUserRoleUseCaseUseCase getUserRoleUseCaseUseCase;
  ActionScanService actionScanService;

  ProgressListController(
    super.useCase, {
    required this.progressUsecase,
    required this.getUserRoleUseCaseUseCase,
    required this.actionScanService,
  }) : super(processType: 0, searchEnum: SearchPageType.actionProgressListPage);

  final RxBool isEditMode = false.obs;

  bool isManager = false;

  Future<void> setEditMode(bool value) async {
    isEditMode.value = value;
  }

  @override
  void onInit() {
    super.onInit();

    _analysisParam();
  }

  void _analysisParam() {}
  @override
  void onReady() async {
    super.onReady();

    await _loadRole();
  }

  Future<void> _loadRole() async {
    await getUserRoleUseCaseUseCase.call(const NoParams());
    final roleList = StorageUtils.get<String>(StorageUtils.keyUserRoleList);
    if (roleList != null && roleList.isNotEmpty) {
      final list = jsonDecode(roleList) as List?;
      if (list != null && list.isNotEmpty) {
        final ret = list.where((p) {
          // 此处不转换 为 对象 是为了效率。
          final obj = p as Map<String, dynamic>;
          return obj['roleId'] == 1;
        });
        if (ret.isNotEmpty) {
          isManager = true;
        }
      }
    }
  }

  void goBackPage() {
    final params = Get.arguments;
    if (params is Map && (params['fromHomePage'] ?? false)) {
      final controller = Get.find<TabsController>();
      controller.handleTabTap(SharedNavBarEnum.home);
    }
    Get.toNamed(AutoRoutes.appTabsAction, id: SharedNavBarEnum.actionList.navigatorId);
  }

  void toDeleteClick({required AssetActionUIModel item}) async {
    LogUtil.d('----- ProgressListController delete -----');
    final createdById = item.assetActionData.createdById;
    final userId = StorageUtils.get<String>(StorageUtils.keyUserId);
    if (createdById == userId || isManager) {
      if (SharedActionAmountTypeEnum.isInventory(item.assetActionData.amountType)) {
        CommonDialogService().show(
          title: '処理を削除しますか',
          content: '「${item.assetActionData.assetActionName}」は複数のユーザーと共有されています。',
          cancelText: 'キャンセル',
          confirmText: 'OK',
          onConfirm: () {
            _deleteAssetAction(item);
          },
        );
      } else {
        CommonDialogService().show(
          content: '${item.assetActionData.assetActionName}を削除します。よろしいですか？',
          cancelText: 'いいえ',
          confirmText: 'はい',
          onConfirm: () {
            _deleteAssetAction(item);
          },
        );
      }
      return;
    }
    CommonDialogService().show(content: 'ご自身で一時保存した処理設定以外は削除できません');
  }

  void _deleteAssetAction(AssetActionUIModel item) async {
    final assetActionId = item.assetActionData.assetActionId;
    if (assetActionId == null) {
      throw SystemException();
    }
    final processId = item.assetActionData.processId;
    if (processId == null) {
      throw SystemException();
    }
    final result = await progressUsecase.call(ActionUpdateQueryParam(assetActionId, processId));
    if (result) {
      state.value.actionList.removeWhere((element) => element.assetActionData.assetActionId == assetActionId);
      CommonDialogService().showToast('削除しました');
    }
  }

  @override
  Future<void> toNextTaskClick({required AssetActionUIModel model}) async {
    LogUtil.d('toDetail, item: $model');
    final List<SharedArInfo>? actionRe = await actionScanService.scanStartNextStep(
      clickNextStepData: ClickNextStepDataModel(
        data: model.assetActionData,
        actionLabel: '',
        tempActionListType: 2,
        isNewAction: false,
        isFromHome: false,
        isFromTakeMiddleButtonPage: false,
        searchKey: '',
        isNeedJump: true,
      ),
    );
    LogUtil.d('actionRe', actionRe);
  }
}
