import 'package:asset_force_mobile_v2/features/action/list/presentation/bindings/complete_binding.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/controllers/complete_controller.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/widgets/complete_item_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/common_search_bar_widget.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/refresh_load_more_list.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('action/complete/list', binding: CompleteBinding)
class CompleteListPage extends GetWidget<CompleteController> {
  const CompleteListPage({super.key});

  @override
  Widget build(BuildContext context) {
    Get.find<CommonSearchBarController>().setSearchKey(controller.state.value.searchKey.value);
    return Scaffold(
      appBar: AppBar(
        title: const Text('実行完了'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60.0),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 12.0, left: 4, right: 4),
            child: CommonSearchBarWidget(onTap: controller.onTapSearchBar, onClear: controller.onTapSearchBarClear),
          ),
        ),
      ),
      body: Obx(() {
        if (controller.isLoading.value && controller.state.value.actionList.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }
        return RefreshLoadMoreList(
          padding: const EdgeInsets.only(top: 12),
          onRefresh: controller.onRefresh,
          onLoadMore: () {
            return controller.onLoadMore();
          },
          isLoading: controller.isLoading,
          items: controller.state.value.actionList,
          itemBuilder: (context, index, item) {
            final int? processId = item.assetActionData.processId;
            return CompleteItemWidget(key: ValueKey(processId), item: item);
          },
          noMoreData: controller.state.value.noMoreData,
        );
      }),
    );
  }
}
