import 'package:asset_force_mobile_v2/features/action/list/presentation/bindings/progress_binding.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/controllers/progress_list_controller.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/models/asset_action_ui_model.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/widgets/progress_item_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/common_search_bar_widget.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/refresh_load_more_list.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('action/progress/list', binding: ProgressBinding)
class ProgressListPage extends GetWidget<ProgressListController> {
  const ProgressListPage({super.key});

  @override
  Widget build(BuildContext context) {
    Get.find<CommonSearchBarController>().setSearchKey(controller.state.value.searchKey.value);
    return Scaffold(
      appBar: AppBar(
        title: const Text('一時保存・実行中'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60.0),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 12.0, left: 4, right: 4),
            child: CommonSearchBarWidget(onTap: controller.onTapSearchBar, onClear: controller.onTapSearchBarClear),
          ),
        ),
        actions: [
          Container(
            width: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [_EditModeButton(controller: controller)],
            ),
          ),
        ],
        leading: IconButton(
          icon: const Icon(Icons.chevron_left, color: Colors.white, size: 30),
          onPressed: () => controller.goBackPage(),
        ),
      ),
      body: Obx(() {
        if (controller.isLoading.value && controller.state.value.actionList.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }
        return RefreshLoadMoreList<AssetActionUIModel>(
          padding: const EdgeInsets.only(top: 12),
          onRefresh: controller.onRefresh,
          onLoadMore: () {
            return controller.onLoadMore();
          },
          isLoading: controller.isLoading,
          items: controller.state.value.actionList,
          itemBuilder: (context, index, item) {
            final int? processId = item.assetActionData.processId;
            return ProgressItemWidget(key: ValueKey(processId), item: item);
          },
          noMoreData: controller.state.value.noMoreData,
        );
      }),
    );
  }
}

class _EditModeButton extends StatelessWidget {
  final ProgressListController controller;

  const _EditModeButton({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return TextButton(
        onPressed: () {
          controller.setEditMode(!controller.isEditMode.value);
        },
        child: Text(
          textAlign: TextAlign.right,
          controller.isEditMode.value ? 'キャンセル' : '編集',
          style: const TextStyle(color: Colors.white),
        ),
      );
    });
  }
}
