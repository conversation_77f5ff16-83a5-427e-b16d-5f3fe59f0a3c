import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/controllers/complete_controller.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/models/asset_action_ui_model.dart';
import 'package:asset_force_mobile_v2/features/action/list/presentation/widgets/action_item_base_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

/// 完成状态的 Action Item 组件
///
/// 显示已完成的操作项目，包含处理名、执行者、资产种类名和执行时间
/// 右侧显示操作相关的图标
class CompleteItemWidget extends GetWidget<CompleteController> {
  /// 操作项目数据模型
  final AssetActionUIModel item;

  const CompleteItemWidget({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    // 基准高度
    const double containerHeight = 30.0;

    // 根据高度计算其他相关值的比例
    final double minWidth = containerHeight; // 最小宽度等于高度，保证圆形
    final double borderRadius = containerHeight / 2; // 圆角半径为高度的一半
    final double fontSize = containerHeight * 0.5; // 字体大小为高度的60%
    final double horizontalPadding = containerHeight * 0.3; // 水平内边距为高度的30%
    final double shadowBlurRadius = containerHeight * 0.4; // 阴影模糊半径为高度的40%

    return InkWell(
      onTap: () => controller.toNextTaskClick(model: item),
      child: ActionItemBaseWidget(
        child: ActionItemBaseWidget.buildStandardLayout(
          infoItems: [
            ActionInfoItem.nullable('処理名', item.assetActionData.assetActionName),
            ActionInfoItem.nullable('実行者', item.assetActionData.createdByName),
            ActionInfoItem.nullable('資産種類名', item.assetActionData.assetTypeName),
            ActionInfoItem.nullable('実行時間', item.assetActionData.createdDate),
          ],
          rightContent: Container(
            height: containerHeight,
            constraints: BoxConstraints(minWidth: minWidth),
            padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
            alignment: Alignment.center, // 确保内容完全居中
            decoration: BoxDecoration(
              color: AppTheme.whiteColor,
              borderRadius: BorderRadius.circular(borderRadius),
              boxShadow: [BoxShadow(color: AppTheme.black12Color, blurRadius: shadowBlurRadius)],
            ),
            child: Text(
              '${item.assetTotal}',
              style: TextStyle(color: AppTheme.blackColor, fontSize: fontSize),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
