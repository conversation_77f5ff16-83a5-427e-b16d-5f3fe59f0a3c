import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/action/new/presentation/models/list_new_ui_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/action_repository.dart';

class ListNewUsecase implements UseCase<LoadDataResult, ActionListQuery> {
  ActionRepository repository;

  ListNewUsecase({required this.repository});

  @override
  Future<LoadDataResult> call(ActionListQuery params) async {
    final resp = await repository.getActionListBySearchKey(params);
    final assetActionList =
        resp.assetActionList?.map((value) {
          return ActionUIModel(
            assetActionName: value.assetActionName ?? '',
            assetTypeName: value.assetTypeName ?? '',
            assetActionId: value.assetActionId ?? 0,
            assetActionItem: value,
          );
        }).toList() ??
        [];

    return LoadDataResult(moreThenLimit: resp.moreThenLimit ?? false, assetActionList: assetActionList);
  }
}

class LoadDataResult {
  bool moreThenLimit;

  List<ActionUIModel> assetActionList;

  LoadDataResult({required this.moreThenLimit, required this.assetActionList});

  @override
  String toString() {
    return 'LoadDataResult{moreThenLimit: $moreThenLimit, assetActionList: $assetActionList}';
  }
}
