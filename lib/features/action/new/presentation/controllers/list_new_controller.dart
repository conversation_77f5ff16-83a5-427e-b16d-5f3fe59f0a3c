import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/loading_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/action_scan_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/action/new/domain/usecases/list_new_usecase.dart';
import 'package:asset_force_mobile_v2/features/action/new/presentation/controllers/list_new_page_param.dart';
import 'package:asset_force_mobile_v2/features/action/new/presentation/models/list_new_ui_model.dart';
import 'package:asset_force_mobile_v2/features/action/new/presentation/models/list_new_ui_state.dart';
import 'package:asset_force_mobile_v2/features/search/domain/enums/search_type.dart';
import 'package:asset_force_mobile_v2/features/search/domain/usecases/get_search_conditions_usecase.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/common_search_bar_widget.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_asset_action_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/action_repository.dart';
import 'package:get/get.dart';

class ListNewController extends LoadingController {
  ListNewUsecase useCase;
  ActionScanService actionScanService;
  final ListNewUIState state = ListNewUIState();

  SearchPageType searchEnum = SearchPageType.actionListPage;

  ListNewController({required this.useCase, required this.actionScanService});

  late ListNewPageParam param;

  @override
  void onInit() {
    if (Get.arguments is ListNewPageParam) {
      param = Get.arguments as ListNewPageParam;

      super.onInit();
    } else {
      handleException(SystemException());
      Get.back();
    }
  }

  String get getHeaderTitle => param.actionLabel;

  /// [params] is a boolean value to indicate whether to clear the list before fetching data.
  @override
  Future<void> fetchData(dynamic params) async {
    // do refresh.
    // reset
    final currentPage = state.currentPage.value;
    final rowPage = state.row.value;

    final s = (currentPage - 1) * rowPage;
    final resp = await useCase.call(
      ActionListQuery(
        skip: s,
        row: rowPage,
        searchKey: '',
        actionListType: param.actionListType,
        actionLabel: param.actionLabel,
      ),
    );

    if (params == true) {
      state.actionList.clear();
    }

    state.actionList.addAll(resp.assetActionList);

    _sortData();
    state.noMoreData.value = !resp.moreThenLimit;
  }

  void _sortData() {
    state.actionList.sort((a, b) {
      int retVal = 0;
      if (a.assetActionName.compareTo(b.assetActionName) > 0) {
        retVal = 1;
      } else if (a.assetActionName.compareTo(b.assetActionName) < 0) {
        retVal = -1;
      }

      // Reverse the order if it's 'descend'
      return state.order.value == 'descend' ? retVal * -1 : retVal;
    });
  }

  Future<void> updateSearchKey(String value) async {
    state.searchKey.value = value;
    await onRefresh();
  }

  Future<void> onRefresh() async {
    if (isLoading.value) return;
    state.currentPage.value = 1;
    state.noMoreData.value = false;
    await fetchData(true);
  }

  Future<void> onLoadMore() async {
    if (isLoading.value || state.noMoreData.value) return;
    state.currentPage.value++;
    await fetchData(false);
  }

  // TODO 此处需要处理 上一个搜索没结束， 下一个搜索就触发了 的情况。 ionic 是不管上一次是否结束， 每次都触发新的。
  void onTapSearchBarClear() {
    updateSearchKey('');
    Get.find<CommonSearchBarController>().setSearchKey(state.searchKey.value);
  }

  Future<void> onTapSearchBar() async {
    final result = await Get.toNamed(
      AutoRoutes.search,
      arguments: GetSearchConditionParams(searchEnum: searchEnum, searchKey: '', searchId: null, assetTypeId: null),
    );
    if (result != null) {
      updateSearchKey(result.searchKey);
      Get.find<CommonSearchBarController>().setSearchKey(state.searchKey.value);
    }
  }

  bool get isAscending => state.order.value == 'ascend';

  void toggleSortOrder() {
    if (isAscending) {
      state.order.value = 'descend';
    } else {
      state.order.value = 'ascend';
    }
    _sortData();
  }

  toDetail(ActionUIModel item) async {
    LogUtil.d('toDetail, item: $item');
    final SharedAssetAction? aai = item.assetActionItem;
    if (aai == null) {
      return;
    }
    final String searchKey = state.searchKey.value;
    final actionLabel = getHeaderTitle;

    final actionRe = actionScanService.scanStartNextStep(
      clickNextStepData: ClickNextStepDataModel(
        data: aai,
        actionLabel: actionLabel,
        tempActionListType: 2,
        isNewAction: true,
        isFromHome: false,
        isFromTakeMiddleButtonPage: false,
        searchKey: searchKey,
        isNeedJump: true,
      ),
    );
    LogUtil.d('list_new_controller.dart ===>', actionRe);
  }
}
