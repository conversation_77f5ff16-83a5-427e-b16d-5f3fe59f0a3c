import 'package:asset_force_mobile_v2/features/action/new/presentation/bindings/list_new_binding.dart';
import 'package:asset_force_mobile_v2/features/action/new/presentation/controllers/list_new_controller.dart';
import 'package:asset_force_mobile_v2/features/action/new/presentation/widgets/list_new_item_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/common_search_bar_widget.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/refresh_load_more_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('action/list/new', binding: ListNewBinding)
class ActionListNewPage extends GetWidget<ListNewController> {
  const ActionListNewPage({super.key});

  @override
  Widget build(BuildContext context) {
    Get.find<CommonSearchBarController>().setSearchKey(controller.state.searchKey.value);
    return Scaffold(
      appBar: AppBar(
        title: Text(controller.getHeaderTitle),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60.0),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 12.0, left: 4, right: 4),
            child: CommonSearchBarWidget(onTap: controller.onTapSearchBar, onClear: controller.onTapSearchBarClear),
          ),
        ),
        actions: [
          Obx(() {
            return IconButton(
              icon: SvgPicture.asset(
                controller.isAscending ? 'assets/icons/sort-alpha-down.svg' : 'assets/icons/sort-alpha-down-alt.svg',
                colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                width: 24,
                height: 24,
              ),
              onPressed: () {
                controller.toggleSortOrder();
              },
            );
          }),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value && controller.state.actionList.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }
        return RefreshLoadMoreList(
          onRefresh: controller.onRefresh,
          onLoadMore: () {
            return controller.onLoadMore();
          },
          isLoading: controller.isLoading,
          items: controller.state.actionList,
          itemBuilder: (context, index, item) {
            return ListNewItemWidget(
              key: ValueKey(item.assetActionId),
              topText: item.assetTypeName,
              bottomText: item.assetActionName,
              onTap: () => {controller.toDetail(item)},
            );
          },
          noMoreData: controller.state.noMoreData,
        );
      }),
    );
  }
}
