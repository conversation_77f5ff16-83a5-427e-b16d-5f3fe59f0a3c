import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class ListNewItemWidget extends StatelessWidget {
  final String topText;
  final String bottomText;
  final VoidCallback? onTap;

  const ListNewItemWidget({super.key, required this.topText, required this.bottomText, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 10, top: 10, right: 10),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            color: AppTheme.lightTheme.customTheme.cardBackgroundColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.only(left: 10, right: 10, top: 0, bottom: 0),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/icons/icon-assets-v2.svg',
                      width: 14,
                      colorFilter: const ColorFilter.mode(Color(0xFF0B3E86), BlendMode.srcIn),
                    ),
                    const SizedBox(width: 5),
                    Expanded(
                      child: Text(
                        topText,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 12, color: Color(0xFF0B3E86)),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                Text(
                  bottomText,
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Color(0xFF323232)),
                ),
              ],
            ),
            trailing: Icon(Icons.chevron_right, color: AppTheme.lightTheme.customTheme.cardArrowColor),
          ),
        ),
      ),
    );
  }
}
