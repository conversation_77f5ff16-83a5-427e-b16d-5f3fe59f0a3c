import 'package:asset_force_mobile_v2/features/action/new/presentation/models/list_new_ui_model.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/models/shared_action_asset_ui_model.dart';

// 目前只用到了如下的数据。
class AssetActionModel {
  var assetTypeName = '';
  var assetTypeId = 0;
  var amountType = '';

  toJson() {
    return {'assetTypeName': assetTypeName, 'assetTypeId': assetTypeId, 'amountType': amountType};
  }
}

class TemporarilyArgModel {
  var registeredAssetList = <SharedActionAssetUIModel>[];

  var actionLabel = '';
  var actionListType = '';

  var isFromHome = false;
  var isFromNewAction = false;
  var isFromScanType = 'barcode';
  var isFromTakeMiddleButtonPage = false;
  var isNewAction = false; // 新规 或者一时保存，  具体的区别在 返回的时候内容不一样。
  var processType = 0;
  var searchKey = '';
  // 所有的原始数据。
  ActionUIModel? assetActionModel;

  toJson() {
    return {
      'registeredAssetList': registeredAssetList,
      'actionLabel': actionLabel,
      'actionListType': actionListType,
      'isFromHome': isFromHome,
      'isFromNewAction': isFromNewAction,
      'isFromScanType': isFromScanType,
      'isFromTakeMiddleButtonPage': isFromTakeMiddleButtonPage,
      'isNewAction': isNewAction,
      'processType': processType,
      'searchKey': searchKey,
      'assetActionModel': assetActionModel,
    };
  }

  @override
  String toString() {
    return 'TemporarilyArgModel{registeredAssetList: $registeredAssetList, actionLabel: $actionLabel, actionListType: $actionListType, isFromHome: $isFromHome, isFromNewAction: $isFromNewAction, isFromScanType: $isFromScanType, isFromTakeMiddleButtonPage: $isFromTakeMiddleButtonPage, isNewAction: $isNewAction, processType: $processType, searchKey: $searchKey, assetActionModel: $assetActionModel}';
  }
}
