import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/bindings/temporarily_binding.dart';
import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/controllers/temporarily_controller.dart';
import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/widgets/temporarily_bottom_right_widget.dart';
import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/widgets/temporarily_fix_header_widget.dart';
import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/widgets/temporarily_item_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/common_search_bar_net_widget.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/refresh_load_more_list.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('action/temporarily', binding: TemporarilyBinding)
class TemporarilyPage extends GetWidget<TemporarilyController> {
  final Color defaultColor = const Color(0xFF0B3E86);
  const TemporarilyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('スキャン済リスト'),
        leading: IconButton(icon: const Icon(Icons.chevron_left), onPressed: controller.onBack),
        actions: [IconButton(icon: const Icon(Icons.camera_alt), onPressed: controller.toScan)],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60.0),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 12.0, left: 4, right: 4),
            child: CommonSearchBarNetWidget(
              searchKey: controller.state.inputKey.value,
              onSearch: controller.onTapSearchBar,
            ),
          ),
        ),
      ),
      body: Obx(() {
        if (controller.state.isLoading.value && controller.state.assetList.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }
        return RefreshLoadMoreList(
          padding: const EdgeInsets.only(top: 12),
          onRefresh: controller.onRefresh,
          onLoadMore: controller.onLoadMore,
          isLoading: controller.isLoading,
          noMoreData: controller.state.noMoreData,
          items: controller.state.assetList,
          itemBuilder: (context, index, item) {
            if (index == 0) {
              return const TemporarilyFixHeaderWidget();
            }
            return TemporarilyItemWidget(key: ValueKey(item.assetId), asset: item);
          },
        );
      }),
      bottomNavigationBar: Obx(() {
        return Container(
          padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [BoxShadow(color: Colors.black.withAlpha(20), blurRadius: 5, offset: const Offset(0, -1))],
          ),
          child: SafeArea(
            child: Row(
              children: [
                Text('全${controller.state.assetTotalCount.value}件'),
                const SizedBox(width: 10),
                // 右侧按钮区域
                const TemporarilyBottomRightWidget(),
              ],
            ),
          ),
        );
      }),
    );
  }
}
