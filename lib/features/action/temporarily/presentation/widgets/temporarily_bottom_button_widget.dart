import 'package:flutter/material.dart';

class TemporarilyBottomButtonWidget extends StatelessWidget {
  final Color bgColor;
  final Color foregroundColor;
  final String text;
  final VoidCallback onPressed;

  const TemporarilyBottomButtonWidget({
    super.key,
    required this.text,
    required this.onPressed,
    this.bgColor = const Color(0xFF0B3E86),
    this.foregroundColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        backgroundColor: bgColor,
        foregroundColor: foregroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.only(left: 15, right: 15, top: 8, bottom: 8),
        minimumSize: const Size(100, 30),
      ),
      child: Text(text, style: const TextStyle(fontWeight: FontWeight.w600)),
    );
  }
}
