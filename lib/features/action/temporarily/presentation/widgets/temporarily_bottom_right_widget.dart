import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/controllers/temporarily_controller.dart';
import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/widgets/temporarily_bottom_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TemporarilyBottomRightWidget extends GetView<TemporarilyController> {
  const TemporarilyBottomRightWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: 3, // 右侧区域占用剩余空间
      child: Obx(() {
        return Row(
          mainAxisAlignment: controller.state.isPrintAvailable.value && controller.state.assetTotalCount.value > 0
              ? MainAxisAlignment
                    .spaceBetween // 三个按钮时均匀分布
              : controller.state.isPrintAvailable.value || controller.state.assetTotalCount.value > 0
              ? MainAxisAlignment
                    .spaceBetween // 两个按钮时左右分布
              : MainAxisAlignment.start, // 一个按钮时靠左对齐
          children: [
            if (controller.state.isPrintAvailable.value) ...[
              TemporarilyBottomButtonWidget(
                text: '印刷',
                onPressed: controller.printLabel,
                bgColor: const Color(0xFF0B3E86),
              ),
            ],
            TemporarilyBottomButtonWidget(
              text: 'キャンセル',
              onPressed: controller.onCancelClick,
              bgColor: Colors.transparent,
              foregroundColor: const Color(0xFF0B3E86),
            ),
            if (controller.state.assetTotalCount.value > 0) ...[
              TemporarilyBottomButtonWidget(
                text: '次へ',
                onPressed: controller.goActionNextStep,
                bgColor: const Color(0xFF0B3E86),
              ),
            ],
          ],
        );
      }),
    );
  }
}
