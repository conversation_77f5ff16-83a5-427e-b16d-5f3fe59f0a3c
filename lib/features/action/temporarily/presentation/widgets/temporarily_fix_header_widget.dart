import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/controllers/temporarily_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TemporarilyFixHeaderWidget extends GetView<TemporarilyController> {
  const TemporarilyFixHeaderWidget({Key? key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 顶部文案
        Padding(
          padding: const EdgeInsets.only(top: 25, bottom: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${controller.getHeaderText()}',
                textAlign: TextAlign.center,
                style: const TextStyle(color: AppTheme.whiteColor),
              ),
            ],
          ),
        ),
        // 显示抬头信息
        Container(
          width: double.infinity,
          decoration: BoxDecoration(color: AppTheme.whiteColor, borderRadius: BorderRadius.circular(8.0)),
          margin: const EdgeInsets.fromLTRB(10, 15, 10, 15),
          padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 16.0),
          child: Obx(() {
            final int asCount = controller.state.assetSearchCount.value;
            final String assetTypeName = controller.state.assetActionModel.value?.assetTypeName ?? '';
            return Row(
              children: [
                Container(
                  width: 4,
                  height: 24,
                  decoration: BoxDecoration(color: AppTheme.darkBlueColor, borderRadius: BorderRadius.circular(2)),
                ),
                const SizedBox(width: 8),
                Text(
                  '$assetTypeName',
                  style: TextStyle(fontSize: AppTheme.textTheme.bodyLarge?.fontSize ?? 18, fontWeight: FontWeight.bold),
                ),
                Text('：$asCount件', style: TextStyle(fontSize: AppTheme.textTheme.bodyLarge?.fontSize ?? 18)),
              ],
            );
          }),
        ),
      ],
    );
  }
}
