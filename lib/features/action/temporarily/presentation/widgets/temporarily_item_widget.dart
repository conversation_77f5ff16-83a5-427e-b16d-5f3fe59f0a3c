import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/action/temporarily/presentation/controllers/temporarily_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_asset_item.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/models/shared_action_asset_ui_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TemporarilyItemWidget extends GetWidget<TemporarilyController> {
  final SharedActionAssetUIModel asset;
  TemporarilyItemWidget({super.key, required this.asset});

  final RxBool _isExpanded = false.obs;
  final TextEditingController _textEditingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return buildAssetDetailsListItem();
  }

  Widget buildAssetDetailsListItem() {
    // 确定是否需要显示"查看更多"按钮
    final bool needsExpandButton = asset.assetItemList.length > 10;
    _textEditingController.text = '${asset.count.value}';

    return Container(
      margin: const EdgeInsets.only(bottom: 10, left: 10, right: 10),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.customTheme.cardBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [BoxShadow(color: Colors.black.withAlpha(10), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 左侧: 删除按钮及竖线
            Container(
              width: 40,
              decoration: const BoxDecoration(
                border: Border(right: BorderSide(color: Colors.grey, width: 0.5)),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.black),
                    onPressed: () {
                      controller.presentDeleteConfirm(asset);
                    },
                  ),
                ],
              ),
            ),

            // 右侧: 主要内容
            Expanded(
              child: Column(
                children: [
                  // 上半部分: 包含信息区域和固定箭头的Row
                  InkWell(
                    onTap: () {
                      controller.toDetailPage(asset);
                    },
                    child: Row(
                      children: [
                        // 左侧: 信息区域
                        Expanded(child: Obx(() => Column(children: _getVisibleItems()))),
                        // 图片显示区域
                        if (asset.homeImageUrl.isNotEmpty) ...[
                          Obx(() {
                            return ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.network(
                                asset.homeImageTUrl.value,
                                width: 50,
                                height: 50,
                                fit: BoxFit.cover,
                                loadingBuilder: (context, child, loadingProgress) {
                                  if (loadingProgress == null) return child;
                                  return const Center(child: CircularProgressIndicator());
                                },
                                errorBuilder: (context, error, stackTrace) {
                                  Get.find<S3Repository>().getTurl(asset.homeImageUrl).then((value) {
                                    asset.homeImageTUrl.value = value;
                                  });

                                  return const SizedBox.shrink();
                                },
                              ),
                            );
                          }),
                        ],
                        // 右侧: 固定的 > 箭头
                        Container(
                          padding: const EdgeInsets.only(right: 4),
                          child: const Icon(Icons.chevron_right, color: Colors.black),
                        ),
                      ],
                    ),
                  ),

                  // さらに表示 按钮 (保留原有箭头)
                  if (needsExpandButton)
                    Obx(
                      () => InkWell(
                        onTap: () {
                          _isExpanded.value = !_isExpanded.value;
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppTheme.homeImageBorderColor),
                          ),
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  const SizedBox.shrink(),
                                  Text(
                                    _isExpanded.value ? '表示を減らす' : 'さらに表示',
                                    style: TextStyle(color: Colors.blue.shade800),
                                  ),
                                  Icon(
                                    _isExpanded.value ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                                    color: Colors.blue.shade800,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                  // 下半部分: 数量编辑相关
                  _bottomNumber(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 下半部分: 数量编辑相关
  Widget _bottomNumber() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('${controller.state.amountData.amountTextList[1]}', style: const TextStyle(fontSize: 14)),
          const SizedBox(height: 8),
          Row(
            children: [
              InkWell(
                onTap: () {
                  controller.minusClick(asset);
                  _textEditingController.text = '${asset.count.value}';
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.blue.shade800),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(child: Icon(Icons.remove, color: Colors.blue.shade800)),
                ),
              ),
              Expanded(
                child: Container(
                  height: 50,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  child: Center(
                    child: TextField(
                      controller: _textEditingController,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      textAlignVertical: TextAlignVertical.center,
                      style: const TextStyle(fontSize: 16),
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(vertical: 0),
                        filled: true,
                        fillColor: Colors.white,
                        border: _inputBorderStyle,
                        enabledBorder: _inputBorderStyle,
                        focusedBorder: _inputBorderStyle,
                      ),
                      onChanged: (value) {
                        controller.updateInput(asset, value.trim());
                        _textEditingController.text = '${asset.count.value}';
                      },
                    ),
                  ),
                ),
              ),
              InkWell(
                onTap: () {
                  controller.plusClick(asset);
                  _textEditingController.text = '${asset.count.value}';
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppTheme.lightTheme.primaryColor),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(child: Icon(Icons.add, color: Colors.blue.shade800)),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  InputBorder get _inputBorderStyle => OutlineInputBorder(
    borderRadius: BorderRadius.circular(8),
    borderSide: BorderSide(color: Colors.grey.shade400, width: 1), // 边框颜色为灰色
  );

  // 返回当前应该显示的列表项
  List<Widget> _getVisibleItems() {
    if (asset.assetItemList.length <= 10 || _isExpanded.value) {
      // 如果列表少于10项或已展开，显示全部
      return asset.assetItemList.map((item) => _buildAssetItemRow(item)).toList();
    } else {
      // 否则只显示前10项
      return asset.assetItemList.sublist(0, 10).map((item) => _buildAssetItemRow(item)).toList();
    }
  }

  Widget _buildAssetItemRow(SharedAssetItem item) {
    // Skip null or empty values
    if (item.itemDisplayName == null || item.value == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey.shade100, width: 0.5)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 120,
            child: Text(item.itemDisplayName!, style: TextStyle(fontSize: 14, color: Colors.grey.shade700)),
          ),
          Text(':   ', style: TextStyle(fontSize: 14, color: Colors.grey.shade700)),
          Expanded(
            child: Text(
              item.value!,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }
}
