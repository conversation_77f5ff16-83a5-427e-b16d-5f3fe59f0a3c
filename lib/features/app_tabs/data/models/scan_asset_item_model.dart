import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'scan_asset_item_model.g.dart';

@JsonSerializable()
class AssetInfoByAssetIdRepositoryModel extends BaseResponse {
  /// 資産情報リスト取得【モバイル専用】
  List<ScanAssetItemModel>? assetList;

  AssetInfoByAssetIdRepositoryModel({this.assetList, required super.code, required super.msg});

  factory AssetInfoByAssetIdRepositoryModel.fromJson(Map<String, dynamic> json) =>
      _$AssetInfoByAssetIdRepositoryModelFromJson(json);
  Map<String, dynamic> toJson() => _$AssetInfoByAssetIdRepositoryModelToJson(this);
}

@JsonSerializable()
class ScanAssetItemModel {
  /// 資産種類ID
  final int? assetTypeId;

  /// 資産種類名
  final String? assetTypeName;

  /// 資産ID
  final int? assetId;

  /// 資産名
  final String? assetName;

  /// 場所
  final String? location;

  /// バーコード
  final String? barcode;

  /// モバイル側表示内容
  final List<ShowAssetItem>? assetItemList;

  /// home image
  final String? homeImageUrl;

  ScanAssetItemModel({
    this.assetTypeId,
    this.assetTypeName,
    this.assetId,
    this.assetName,
    this.location,
    this.barcode,
    this.assetItemList,
    this.homeImageUrl,
  });

  factory ScanAssetItemModel.fromJson(Map<String, dynamic> json) => _$ScanAssetItemModelFromJson(json);
  Map<String, dynamic> toJson() => _$ScanAssetItemModelToJson(this);
}

@JsonSerializable()
class ShowAssetItem {
  /// 項目ID(自動採番)
  final int? itemId;

  /// 項目表示名称（必填，最大50字符）
  final String? itemDisplayName;

  /// 項目タイプ
  final String? itemType;

  /// 項目値
  final String? value;

  ShowAssetItem({this.itemId, this.itemDisplayName, this.itemType, this.value});

  factory ShowAssetItem.fromJson(Map<String, dynamic> json) => _$ShowAssetItemFromJson(json);
  Map<String, dynamic> toJson() => _$ShowAssetItemToJson(this);
}
