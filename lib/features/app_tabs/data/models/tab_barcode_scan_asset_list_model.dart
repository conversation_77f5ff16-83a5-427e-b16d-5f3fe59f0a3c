import 'package:json_annotation/json_annotation.dart';

part 'tab_barcode_scan_asset_list_model.g.dart';

// 自定义 fromJson
int? _fromJsonToInt(dynamic value) {
  if (value == null) {
    return null;
  } else if (value is int) {
    return value;
  } else if (value is String) {
    return int.tryParse(value);
  }
  return null;
}

// unregisterBarcodeList 转换
List<BarcodeItem>? _barcodeItemListFromJson(List<dynamic>? jsonList) {
  return jsonList?.map((e) => BarcodeItem.fromJson(Map<String, dynamic>.from(e))).toList();
}

List<Map<String, dynamic>>? _barcodeItemListToJson(List<BarcodeItem>? list) {
  return list?.map((e) => e.toJson()).toList();
}

// List<ArInfoItem> 转换
List<ArInfoItem> _fromJsonItems(dynamic json) {
  if (json is List) {
    return json
        .where((e) => e is Map) // 过滤非 Map 项
        .map((e) => ArInfoItem.fromJson(Map<String, dynamic>.from(e as Map)))
        .toList();
  }
  return [];
}

List<Map<String, dynamic>> _toJsonItems(List<ArInfoItem>? items) {
  return items?.map((e) => e.toJson()).toList() ?? [];
}

// 自定义 toJson 方法
dynamic _toJsonFromInt(int? value) {
  return value; // 转换为 int
}

@JsonSerializable()
class TabBarcodeScanAssetListModel {
  @JsonKey(fromJson: _barcodeItemListFromJson, toJson: _barcodeItemListToJson)
  final List<BarcodeItem>? unregisterBarcodeList;

  @JsonKey(fromJson: _fromJsonItems, toJson: _toJsonItems)
  final List<ArInfoItem>? showARInfo;

  @JsonKey(fromJson: _fromJsonToInt, toJson: _toJsonFromInt)
  int? assetId;
  @JsonKey(fromJson: _fromJsonToInt, toJson: _toJsonFromInt)
  int? assetTypeId;
  @JsonKey(fromJson: _fromJsonToInt, toJson: _toJsonFromInt)
  int? type;
  @JsonKey(name: 'barCode')
  String? barCode;

  TabBarcodeScanAssetListModel({
    required this.unregisterBarcodeList,
    required this.showARInfo,
    required this.assetId,
    required this.assetTypeId,
    required this.type,
    required this.barCode,
  });

  factory TabBarcodeScanAssetListModel.fromJson(Map<String, dynamic> json) =>
      _$TabBarcodeScanAssetListModelFromJson(json);
  Map<String, dynamic> toJson() => _$TabBarcodeScanAssetListModelToJson(this);
}

// Model class for items in 'unregisterBarcodeList'
@JsonSerializable()
class BarcodeItem {
  String? barCode;
  String? location;
  bool? isRfid;
  bool? isAssetScan;
  String? rfid;
  String? scanTechnical;

  BarcodeItem({this.barCode, this.location, this.isRfid, this.isAssetScan, this.rfid, this.scanTechnical});

  factory BarcodeItem.fromJson(Map<String, dynamic> json) => _$BarcodeItemFromJson(json);
  Map<String, dynamic> toJson() => _$BarcodeItemToJson(this);
}

// List<ScanItem>转换
List<ScanItem>? _scanItemListFromJson(List<dynamic>? jsonList) {
  return jsonList?.map((e) => ScanItem.fromJson(Map<String, dynamic>.from(e))).toList();
}

List<Map<String, dynamic>>? _scanItemListToJson(List<ScanItem>? list) {
  return list?.map((e) => e.toJson()).toList();
}

// Model class for items in 'showARInfo'
@JsonSerializable()
class ArInfoItem {
  String? scanTechnical;
  @JsonKey(name: 'assetId', fromJson: _fromJsonToInt, toJson: _toJsonFromInt)
  int? assetId;
  @JsonKey(name: 'scanItems', fromJson: _scanItemListFromJson, toJson: _scanItemListToJson)
  final List<ScanItem>? scanItems;

  int? quantity;
  String? arColor;
  bool? homeImageMobileDsiplayFlg;
  int? count;
  String? assetName;
  @JsonKey(name: 'assetTypeId', fromJson: _fromJsonToInt, toJson: _toJsonFromInt)
  int? assetTypeId;
  String? barCode;
  String? location;
  String? homeImageUrlForShow;

  ArInfoItem({
    this.scanTechnical,
    this.assetId,
    required this.scanItems,
    this.quantity,
    this.arColor,
    this.homeImageMobileDsiplayFlg,
    this.count,
    this.assetName,
    this.assetTypeId,
    this.barCode,
    this.location,
    this.homeImageUrlForShow,
  });

  factory ArInfoItem.fromJson(Map<String, dynamic> json) => _$ArInfoItemFromJson(json);
  Map<String, dynamic> toJson() => _$ArInfoItemToJson(this);
}

// Model class for items in 'scanItems' within showARInfo
@JsonSerializable()
class ScanItem {
  String? itemName;
  String? itemType;
  String? itemVal;
  String? itemOption;

  ScanItem({this.itemName, this.itemType, this.itemVal, this.itemOption});

  factory ScanItem.fromJson(Map<String, dynamic> json) => _$ScanItemFromJson(json);
  Map<String, dynamic> toJson() => _$ScanItemToJson(this);
}
