import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/data/models/scan_asset_item_model.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/domain/repositories/scan_barcode_list_repository.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';

class ScanBarcodeListRepositoryImpl with RepositoryErrorHandler implements ScanBarcodeListRepository {
  final DioUtil dioUtil;

  /// 构造函数，通过依赖注入获取 DioUtil 实例
  ScanBarcodeListRepositoryImpl({required this.dioUtil});

  @override
  Future<AssetInfoByAssetIdRepositoryModel> getAssetInfoByAssetIdList({required List<int> assetIdList}) async {
    return executeRepositoryTask<AssetInfoByAssetIdRepositoryModel>(() async {
      final response = await dioUtil.post(
        GlobalVariable.getAssetInfoByAssetIds,
        data: assetIdList,
        useFormUrlEncoded: false,
      );

      if (response.isSuccess()) {
        return AssetInfoByAssetIdRepositoryModel.fromJson(response.data);
      } else {
        LogUtil.e('getAssetInfoByAssetIdList Failed: ${response.statusCode}');
        throw BusinessException(
          'Failed to get asset info by asset ID list due to API response status: ${response.statusCode}',
        );
      }
    }, 'Failed to get asset info by asset ID list');
  }
}
