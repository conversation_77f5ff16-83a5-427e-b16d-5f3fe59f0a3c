import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/data/models/scan_asset_item_model.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/data/models/tab_barcode_scan_asset_list_model.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/domain/repositories/scan_barcode_list_repository.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_ui_model.dart';

class GetRegisteredScanBarcodeListUsecase implements UseCase<ReAssetTypeModel, TabBarcodeScanAssetListModel> {
  final ScanBarcodeListRepository scanBarcodeListRepository;
  GetRegisteredScanBarcodeListUsecase({required this.scanBarcodeListRepository});

  @override
  Future<ReAssetTypeModel> call(TabBarcodeScanAssetListModel params) async {
    final List<ArInfoItem>? showARInfoList = params.showARInfo;
    if (showARInfoList == null || showARInfoList.isEmpty) {
      return ReAssetTypeModel(totalAssetCount: 0);
    }
    final List<int> assetIdList = showARInfoList
        .map((sai) => sai.assetId)
        .where((assetId) => assetId != null)
        .map((assetId) => assetId!)
        .toList();

    final AssetInfoByAssetIdRepositoryModel? getAssetInfoRe = await scanBarcodeListRepository.getAssetInfoByAssetIdList(
      assetIdList: assetIdList,
    );

    if (getAssetInfoRe == null) {
      return ReAssetTypeModel(totalAssetCount: 0);
    }
    final List<ScanAssetItemModel>? assetList = getAssetInfoRe.assetList;
    if (assetList == null || assetList.isEmpty) {
      return ReAssetTypeModel(totalAssetCount: 0);
    }
    final Map<int, ShowAssetTypeModel> showScanMap = {};
    for (int i = 0; i < assetList.length; i++) {
      final ScanAssetItemModel sa = assetList[i];
      final int? assetTypeId = sa.assetTypeId;
      if (assetTypeId == null) {
        continue;
      }
      final String? assetTypeName = sa.assetTypeName;
      if (assetTypeName == null || assetTypeName.isEmpty) {
        continue;
      }
      final int? assetId = sa.assetId;
      if (assetId == null) {
        continue;
      }
      final List<AssetUIDisplay>? assetItemDisplay = sa.assetItemList?.map((si) {
        return AssetUIDisplay(title: si.itemDisplayName ?? '', content: si.value ?? '');
      }).toList();
      if (assetItemDisplay == null || assetItemDisplay.isEmpty) {
        continue;
      }

      final asset = AssetUIModel(
        assetId: sa.assetId ?? 0,
        assetDisplayList: assetItemDisplay,
        imageUrl: sa.homeImageUrl,
      );
      if (showScanMap.containsKey(assetTypeId)) {
        showScanMap[assetTypeId]!.assetUIList.add(asset);
      } else {
        showScanMap[assetTypeId] = ShowAssetTypeModel(
          assetUIList: [asset],
          assetTypeId: assetTypeId,
          assetTypeName: assetTypeName,
        );
      }
    }

    final registeredAssetMap = _sortGroupedById(groupedById: showScanMap);
    final int totalAssetCount = registeredAssetMap.values.fold(0, (sum, mw) {
      return sum + mw.assetUIList.length; // 累加每个 ModelWrapper 中的 models 数量
    });

    return ReAssetTypeModel(showItemAsset: registeredAssetMap, totalAssetCount: totalAssetCount);
  }

  // 封装排序逻辑
  Map<int, ShowAssetTypeModel> _sortGroupedById({required Map<int, ShowAssetTypeModel> groupedById}) {
    final sortedKeys = groupedById.keys.toList()..sort();

    final sortedMap = {for (var key in sortedKeys) key: groupedById[key]!};

    sortedMap.forEach((id, modelWrapper) {
      modelWrapper.assetUIList.sort((a, b) => a.assetId.compareTo(b.assetId)); // 排序每个 ModelWrapper 的 models
    });

    return sortedMap;
  }

  ReAssetTypeModel registeredDeleteAction({
    required int assetTypeId,
    required AssetUIModel itemAsset,
    required Map<int, ShowAssetTypeModel> registeredItemMap,
  }) {
    final Map<int, ShowAssetTypeModel> newMap = {};
    registeredItemMap.forEach((int k, ShowAssetTypeModel md) {
      newMap[k] = ShowAssetTypeModel(
        assetUIList: List<AssetUIModel>.from(md.assetUIList),
        assetTypeId: md.assetTypeId,
        assetTypeName: md.assetTypeName,
      );
    });
    if (newMap.containsKey(assetTypeId)) {
      // 删除对应 name 的元素
      newMap[assetTypeId]?.assetUIList.removeWhere((asset) => asset.assetId == itemAsset.assetId);

      // 如果该 key 下的列表为空，则移除整个 key
      if (newMap[assetTypeId]?.assetUIList.isEmpty ?? true) {
        newMap.remove(assetTypeId);
      }
      final int totalAssetCount = newMap.values.fold(0, (sum, mw) {
        return sum + mw.assetUIList.length; // 累加每个 ModelWrapper 中的 models 数量
      });
      return ReAssetTypeModel(showItemAsset: newMap, totalAssetCount: totalAssetCount);
    }
    return ReAssetTypeModel(totalAssetCount: 0);
  }
}

class ReAssetTypeModel {
  Map<int, ShowAssetTypeModel>? showItemAsset;
  int totalAssetCount;
  ReAssetTypeModel({this.showItemAsset, required this.totalAssetCount});
}

class ShowAssetTypeModel {
  List<AssetUIModel> assetUIList;
  int assetTypeId;
  String assetTypeName;

  ShowAssetTypeModel({required this.assetUIList, required this.assetTypeId, required this.assetTypeName});
}
