import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/data/models/tab_barcode_scan_asset_list_model.dart';

class GetUnregisteredScanBarcodeListUsecase
    implements UseCase<ReUnregisteredAssetTypeModel, TabBarcodeScanAssetListModel> {
  GetUnregisteredScanBarcodeListUsecase();

  @override
  Future<ReUnregisteredAssetTypeModel> call(TabBarcodeScanAssetListModel params) async {
    final List<BarcodeItem>? unregisterBarcodeList = params.unregisterBarcodeList;
    if (unregisterBarcodeList == null || unregisterBarcodeList.isEmpty) {
      return ReUnregisteredAssetTypeModel(totalUnregisterAssetCount: 0);
    }
    final List<String> showUnregisterAsset = unregisterBarcodeList.map((ub) {
      return (ub.scanTechnical == 'RFIDScan' ? ub.rfid : ub.barCode) ?? '';
    }).toList();

    return ReUnregisteredAssetTypeModel(
      showUnregisterAsset: showUnregisterAsset,
      totalUnregisterAssetCount: showUnregisterAsset.length,
    );
  }

  ReUnregisteredAssetTypeModel unRegisteredDeleteAction({
    required String unRegisteredBarcode,
    required List<String> unregisteredItems,
  }) {
    final List<String> newItems = List.from(unregisteredItems);
    newItems.remove(unRegisteredBarcode);
    return ReUnregisteredAssetTypeModel(showUnregisterAsset: newItems, totalUnregisterAssetCount: newItems.length);
  }
}

class ReUnregisteredAssetTypeModel {
  List<String>? showUnregisterAsset;
  int totalUnregisterAssetCount;
  ReUnregisteredAssetTypeModel({this.showUnregisterAsset, required this.totalUnregisterAssetCount});
}
