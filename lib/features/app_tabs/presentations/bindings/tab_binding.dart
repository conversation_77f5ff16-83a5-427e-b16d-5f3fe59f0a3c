import 'package:asset_force_mobile_v2/core/extensions/getx_extension.dart';
import 'package:asset_force_mobile_v2/core/services/get_navigation_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/domain/usecase/action_sheet_module_usecase.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:get/get.dart';

class TabsBinding extends Bindings {
  @override
  void dependencies() {
    // 懒加载 ModuleUseCase
    Get.lazyPutFenix<ModuleUseCase>(() => ModuleUseCase());
    Get.lazyPutFenix<NavigationService>(() => GetNavigationService());

    Get.lazyPutFenix(
      () => TabsController(moduleUseCase: Get.find<ModuleUseCase>(), navigationService: Get.find<NavigationService>()),
    );
  }
}
