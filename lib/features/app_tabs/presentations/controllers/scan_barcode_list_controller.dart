import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/common_dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/services/scan_handler_service.dart';
import 'package:asset_force_mobile_v2/core/utils/string_utils.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/data/models/tab_barcode_scan_asset_list_model.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/domain/usecase/action_sheet_module_usecase.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/domain/usecase/registered_scan_barcode_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/domain/usecase/unregistered_scan_barcode_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/states/scan_asset_ui_state.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_ui_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_type/presentation/controllers/asset_type_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_ai_ocr_type_enum.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/bottom_center_scan_plugin.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/ocr_car_number_plugin.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/ocr_universal_serial_number_plugin.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 已登录，未登录
enum ScanStatus { registered, unregistered }

class ScanBarcodeListController extends BaseController with GetSingleTickerProviderStateMixin {
  late TabController tabController;
  final GetRegisteredScanBarcodeListUsecase getRegisteredScanBarcodeListUsecase;
  final GetUnregisteredScanBarcodeListUsecase getUnregisteredScanBarcodeListUsecase;
  final ModuleUseCase moduleUseCase;
  final NavigationService navigationService;
  ScanBarcodeListController({
    required this.getRegisteredScanBarcodeListUsecase,
    required this.getUnregisteredScanBarcodeListUsecase,
    required this.moduleUseCase,
    required this.navigationService,
  });
  // 声明为变量
  late ScanAssetUIState uiState;

  @override
  void onInit() async {
    super.onInit();
    // 注册/获取状态管理类
    uiState = provideScanAssetUIState();
    tabController = TabController(vsync: this, length: 2);
    tabController.addListener(() {
      if (tabController.index.toDouble() == tabController.animation!.value) {
        uiState.selectedTab.value = tabController.index == 0 ? ScanStatus.registered : ScanStatus.unregistered;
      }
    });
  }

  @override
  void onReady() async {
    super.onReady();
    final Map<String, dynamic>? params = Get.arguments as Map<String, dynamic>;
    if (params == null || params.isEmpty) {
      return;
    }

    await handleScanResult(params, uiState);
  }

  goBackPage() {
    Get.back();
  }

  openScan(BuildContext context) async {
    final Map<String, dynamic> assetDataInfo = {
      'unregisterBarcodeList': uiState.unregisteredAssetList,
      'scanType': 'barcode',
      'showARInfo': uiState.registeredAssetList,
    };

    final SharedAIOcrTypeEnum selectedValue = await centerButtonShowScanModeSelectionActionSheet();

    // TODO 此处当项目原生趋于稳定后需要迁移到共同方法（起始）
    if (SharedAIOcrTypeEnum.CANCEL == selectedValue) {
      return null;
    }
    switch (selectedValue) {
      case SharedAIOcrTypeEnum.BARCODE_QR_RFID:
        final Map<String, dynamic> re = await CenterBarcodeScanPlugin().getLastPhotoTaken(data: assetDataInfo);
        await handleScanResult(re, uiState);

      case SharedAIOcrTypeEnum.TRAFFIC_RENTAL:
        final Map<String, dynamic> re = await OcrCarNumberPlugin().ocrCarNumber(data: assetDataInfo);
        await handleScanResult(re, uiState);

      case SharedAIOcrTypeEnum.WELCIA:
        final Map<String, dynamic> re = await OcrUniversalSerialNumberPlugin().universalSerialNumbersOcr(
          data: assetDataInfo,
        );
        await handleScanResult(re, uiState);

      default:
        return null;
    }
    // TODO 此处当项目原生趋于稳定后需要迁移到共同方法（结束）
  }

  // 提出一个方法来注册/获取uiState
  ScanAssetUIState provideScanAssetUIState() {
    if (!Get.isRegistered<ScanAssetUIState>()) {
      Get.put(ScanAssetUIState());
    }
    return Get.find<ScanAssetUIState>();
  }

  /// 处理扫描后的结果数据
  Future<void> handleScanResult(Map<String, dynamic> params, ScanAssetUIState uiState) async {
    final Map<String, dynamic>? scanData = params['data'];
    // 将扫描返回的参数 JSON 转换为资产模型对象
    final TabBarcodeScanAssetListModel? barcodeScanAsset = TabBarcodeScanAssetListModel.fromJson(scanData ?? params);

    // 如果转换失败（数据异常或为空），直接返回，不继续处理
    if (barcodeScanAsset == null) {
      return;
    }

    // 显示加载中动画，表示正在处理数据
    await showLoading();

    // ====== 处理已注册资产列表（AR 显示用） ======
    if (barcodeScanAsset.showARInfo != null) {
      uiState.registeredAssetList.assignAll(barcodeScanAsset.showARInfo!);
    } else {
      uiState.registeredAssetList.clear();
    }

    // ====== 处理未注册资产（扫码但未入库的）列表 ======
    if (barcodeScanAsset.unregisterBarcodeList != null) {
      uiState.unregisteredAssetList.assignAll(barcodeScanAsset.unregisterBarcodeList as Iterable<BarcodeItem>);
    } else {
      uiState.unregisteredAssetList.clear();
    }

    // ====== 获取详细的已注册资产数据 ======
    final ReAssetTypeModel registeredAssetCall = await getRegisteredScanBarcodeListUsecase.call(barcodeScanAsset);

    final Map<int, ShowAssetTypeModel>? registeredAssetMap = registeredAssetCall.showItemAsset;

    if (registeredAssetMap != null && registeredAssetMap.isNotEmpty) {
      uiState.registeredItemMap.value = registeredAssetMap;
    }

    uiState.totalRegisteredAssetCount.value = registeredAssetCall.totalAssetCount;

    // ====== 获取未注册资产详细数据 ======
    final ReUnregisteredAssetTypeModel unregisteredAssetCall = await getUnregisteredScanBarcodeListUsecase.call(
      barcodeScanAsset,
    );

    final List<String>? showUnregisterAssetList = unregisteredAssetCall.showUnregisterAsset;

    if (showUnregisterAssetList != null && showUnregisterAssetList.isNotEmpty) {
      uiState.unregisteredItems.value = showUnregisterAssetList;
    }

    uiState.totalUnregisteredAssetCount.value = unregisteredAssetCall.totalUnregisterAssetCount;

    // 隐藏加载动画，处理完成
    hideLoading();
  }

  /// 已登录资产列表删除
  registeredDeleteOnClick({required int assetTypeId, required AssetUIModel itemAsset}) async {
    await CommonDialogService().show(
      content: '${itemAsset.assetId}を削除しますか？',
      confirmText: 'はい',
      cancelText: 'いいえ',
      onConfirm: () {
        final ReAssetTypeModel registeredAssetCall = getRegisteredScanBarcodeListUsecase.registeredDeleteAction(
          assetTypeId: assetTypeId,
          itemAsset: itemAsset,
          registeredItemMap: uiState.registeredItemMap,
        );
        final Map<int, ShowAssetTypeModel>? registeredAssetMap = registeredAssetCall.showItemAsset;
        uiState.registeredItemMap.value = registeredAssetMap ?? {};
        uiState.totalRegisteredAssetCount.value = registeredAssetCall.totalAssetCount;
      },
    );
  }

  /// 未登录资产列表删除
  unRegisteredDeleteOnClick({required String unRegisteredBarcode}) async {
    await CommonDialogService().show(
      content: StringUtils.htmlSpecialChars(unRegisteredBarcode) + 'を削除しますか? ',
      confirmText: 'はい',
      cancelText: 'いいえ',
      onConfirm: () {
        final unregisteredAssetCall = getUnregisteredScanBarcodeListUsecase.unRegisteredDeleteAction(
          unRegisteredBarcode: unRegisteredBarcode,
          unregisteredItems: uiState.unregisteredItems,
        );
        final List<String>? showUnregisterAssetList = unregisteredAssetCall.showUnregisterAsset;
        uiState.unregisteredItems.value = showUnregisterAssetList ?? [];
        uiState.totalUnregisteredAssetCount.value = unregisteredAssetCall.totalUnregisterAssetCount;
      },
    );
  }

  /// 新规资产
  newAssetOnClick({required String unRegisteredBarcode}) async {
    final result = await Get.toNamed(AutoRoutes.assetType, arguments: {'showMenu': true});
    if (result == null) return;

    if (result is! AssetTypeResultModel) {
      return;
    }

    final assetType = result.assetTypeInfo;
    // 仿照TabsController用navigationService跳转，使用AutoRoutes常量
    navigationService.navigateTo(
      AutoRoutes.assetCreate, // 使用资产创建路由
      arguments: {'assetType': assetType, 'barcode': unRegisteredBarcode}, // 传递barcode参数
    );
  }

  /// 履历情报新规
  appurtenancesInformationOnClick() async {
    await Get.toNamed(AutoRoutes.assetAppurtenancesInformation);
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
}
