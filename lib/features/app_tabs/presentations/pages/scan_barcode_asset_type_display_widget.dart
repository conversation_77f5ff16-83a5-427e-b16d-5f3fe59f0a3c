import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/scan_barcode_list_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

class ScanBarcodeAssetTypeDisplayWidget extends GetView<ScanBarcodeListController> {
  final int itemCount;
  final String assetTypeName;
  final Color defaultColor;
  const ScanBarcodeAssetTypeDisplayWidget({
    super.key,
    required this.itemCount,
    required this.assetTypeName,
    required this.defaultColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: AppTheme.whiteColor, borderRadius: BorderRadius.circular(5)),
      child: Padding(
        padding: const EdgeInsets.only(left: 18, right: 18),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 4, // 宽度
                      height: 16, // 高度
                      decoration: BoxDecoration(
                        color: defaultColor,
                        borderRadius: BorderRadius.circular(10), // 设定圆角半径
                      ),
                      child: const SizedBox(),
                    ),
                    const SizedBox(width: 8),
                    // 资产种类名字 以及这个资产种类有多少个资产
                    Text(
                      '$assetTypeName',
                      style: const TextStyle(color: AppTheme.blackColor, fontSize: 16, fontWeight: FontWeight.w500),
                    ),
                    Text('：$itemCount 件', style: const TextStyle(color: AppTheme.blackColor, fontSize: 15)),
                  ],
                ),
                OutlinedButton(
                  onPressed: () => controller.appurtenancesInformationOnClick(),
                  style: OutlinedButton.styleFrom(
                    backgroundColor: defaultColor,
                    foregroundColor: AppTheme.whiteColor,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    padding: const EdgeInsets.only(left: 15, right: 15),
                    minimumSize: const Size(30, 30),
                  ),
                  // 履历情报按钮
                  child: const Text(
                    '履歴情報登録',
                    style: const TextStyle(color: AppTheme.whiteColor, fontWeight: FontWeight.w700),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
