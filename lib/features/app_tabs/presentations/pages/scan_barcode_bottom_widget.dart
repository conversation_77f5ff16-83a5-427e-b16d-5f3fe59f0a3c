import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/scan_barcode_list_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

/// 按钮之间的上下间距
const double buttonsTopAndBottomBetween = 5;

class ScanBarcodeBottomWidget extends GetView<ScanBarcodeListController> {
  const ScanBarcodeBottomWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppTheme.whiteColor,
      padding: const EdgeInsets.symmetric(vertical: buttonsTopAndBottomBetween),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(left: 10, right: 10),
          child: Row(
            children: [
              Obx(() {
                final ScanStatus selectedTab = controller.uiState.selectedTab.value;
                var totalCount = controller.uiState.totalRegisteredAssetCount.value;
                if (selectedTab == ScanStatus.unregistered) {
                  totalCount = controller.uiState.totalUnregisteredAssetCount.value;
                }
                return Text('全${totalCount}件');
              }),
              // 按钮间隔
              const SizedBox(width: 10),
              // 检索按钮
              OutlinedButton(
                onPressed: () => controller.goBackPage(),
                style: _outlinedButtonStyle(40),
                child: const Text('キャンセル', style: TextStyle(color: AppTheme.darkBlueColor)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// OutlinedButton 的公共样式
  ButtonStyle _outlinedButtonStyle(double height) {
    return OutlinedButton.styleFrom(
      foregroundColor: AppTheme.whiteColor,
      side: const BorderSide(color: AppTheme.darkBlueColor, width: 1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      padding: EdgeInsets.zero,
      minimumSize: Size(110, height),
    );
  }
}
