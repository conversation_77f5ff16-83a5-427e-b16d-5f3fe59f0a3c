import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/bindings/scan_barcode_list_binding.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/scan_barcode_list_controller.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/pages/scan_barcode_bottom_widget.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/pages/scan_barcode_registered_asset_list_widget.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/pages/scan_barcode_unregistered_asset_list_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/app_tab/scan_view_barcode_list', binding: ScanBarcodeListBinding)
class ScanBarcodeListPage extends GetView<ScanBarcodeListController> {
  const ScanBarcodeListPage({super.key});

  static Color defaultColor = AppTheme.darkBlueColor;
  static const double distanceEdgeSize = 16.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: defaultColor,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.chevron_left, color: AppTheme.whiteColor, size: 30),
          onPressed: () => controller.goBackPage(),
        ),
        title: const Text(
          'スキャン済リスト',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: AppTheme.whiteColor),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.camera_alt_outlined, color: Colors.white, size: 20),
            onPressed: () => controller.openScan(context),
          ),
        ],
        bottom: TabBar(
          controller: controller.tabController,
          labelColor: AppTheme.whiteColor,
          unselectedLabelColor: AppTheme.whiteColor,
          labelStyle: const TextStyle(fontWeight: FontWeight.w900),
          unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal, fontSize: 15),
          indicatorColor: AppTheme.whiteColor,
          indicatorWeight: 2,
          indicatorSize: TabBarIndicatorSize.tab,
          tabs: [
            Obx(() => Tab(text: '登録済（${controller.uiState.totalRegisteredAssetCount}件）')),
            Obx(() => Tab(text: '未登録（${controller.uiState.totalUnregisteredAssetCount}件）')),
          ],
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(left: 8, right: 8, top: 12),
          child: TabBarView(
            controller: controller.tabController,
            children: [
              // 已登录
              ScanBarcodeRegisteredAssetListWidget(defaultColor: defaultColor),
              // 未登录
              ScanBarcodeUnregisteredAssetListWidget(defaultColor: defaultColor),
            ],
          ),
        ),
      ),
      bottomNavigationBar: const ScanBarcodeBottomWidget(),
    );
  }
}
