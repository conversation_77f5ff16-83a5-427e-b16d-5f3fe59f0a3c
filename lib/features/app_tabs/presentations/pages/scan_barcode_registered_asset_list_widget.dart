import 'package:asset_force_mobile_v2/features/app_tabs/domain/usecase/registered_scan_barcode_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/scan_barcode_list_controller.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/pages/scan_barcode_asset_type_display_widget.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_ui_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/widgets/asset_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

class ScanBarcodeRegisteredAssetListWidget extends GetView<ScanBarcodeListController> {
  final Color defaultColor;
  const ScanBarcodeRegisteredAssetListWidget({super.key, required this.defaultColor});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return ListView.builder(
        itemCount: controller.uiState.registeredItemMap.length,
        itemBuilder: (context, index) {
          final int? assetTypeId = controller.uiState.registeredItemMap.keys.toList()[index];
          if (assetTypeId == null) {
            return const SizedBox.shrink();
          }
          final ShowAssetTypeModel? itemAsset = controller.uiState.registeredItemMap[assetTypeId];
          if (itemAsset == null) {
            return const SizedBox.shrink();
          }
          final List<AssetUIModel> assetUIList = itemAsset.assetUIList;
          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: ScanBarcodeAssetTypeDisplayWidget(
                  itemCount: assetUIList.length,
                  assetTypeName: itemAsset.assetTypeName,
                  defaultColor: defaultColor,
                ),
              ),
              ...assetUIList.map(
                (item) => Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(217),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              bottomLeft: Radius.circular(8),
                            ),
                            border: const Border(
                              right: BorderSide(
                                width: 1, // 线宽
                                color: Colors.black26, // 线的颜色
                              ),
                            ),
                          ),
                          width: 35,
                          child: IconButton(
                            padding: EdgeInsets.zero,
                            icon: const Icon(Icons.delete, color: Colors.black),
                            iconSize: 20,
                            onPressed: () =>
                                controller.registeredDeleteOnClick(assetTypeId: assetTypeId, itemAsset: item),
                          ),
                        ),
                        Expanded(
                          child: ClipRRect(
                            borderRadius: const BorderRadius.only(
                              topRight: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            ),
                            child: AssetItemWidget(
                              key: Key('asset-item-${item.assetId}'), // 为每个资产项分配唯一的 Key
                              assetUIModel: item,
                              isShowBorderRadius: false,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      );
    });
  }
}
