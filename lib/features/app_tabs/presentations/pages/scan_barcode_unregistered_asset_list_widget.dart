import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/scan_barcode_list_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

class ScanBarcodeUnregisteredAssetListWidget extends GetView<ScanBarcodeListController> {
  final Color defaultColor;

  const ScanBarcodeUnregisteredAssetListWidget({super.key, required this.defaultColor});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return ListView.builder(
        itemCount: controller.uiState.unregisteredItems.length,
        itemBuilder: (context, index) {
          final String? unregisteredItem = controller.uiState.unregisteredItems[index];
          if (unregisteredItem == null || unregisteredItem.isEmpty) {
            return const SizedBox.shrink();
          }
          return Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: IntrinsicHeight(
              child: Container(
                decoration: BoxDecoration(color: Colors.white.withAlpha(217), borderRadius: BorderRadius.circular(8.0)),
                child: Row(
                  children: [
                    Container(
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(
                            width: 1, // 线宽
                            color: Colors.black26, // 线的颜色
                          ),
                        ),
                      ),
                      width: 30,
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        icon: const Icon(Icons.delete, color: Colors.black),
                        iconSize: 20,
                        onPressed: () => controller.unRegisteredDeleteOnClick(unRegisteredBarcode: unregisteredItem),
                      ),
                    ),
                    Expanded(
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () => controller.newAssetOnClick(unRegisteredBarcode: unregisteredItem),
                        child: Container(
                          padding: const EdgeInsets.only(left: 10),
                          width: double.infinity,
                          height: double.infinity,
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  unregisteredItem,
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                              const Icon(Icons.chevron_right, color: Colors.black),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    });
  }
}
