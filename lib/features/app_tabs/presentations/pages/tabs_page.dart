import 'package:asset_force_mobile_v2/features/action/action_tabs/presentation/bindings/update_binding.dart';
import 'package:asset_force_mobile_v2/features/action/action_tabs/presentation/navigator/action_navigator.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/bindings/tab_binding.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/widgets/bottom_nav_bar.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/bindings/asset_list_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/navigator/asset_navigator.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/bindings/home_binding.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/pages/home_page.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/Indexed_stack_widget.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/presentation/bindings/workflow_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/presentation/navigator/workflow_navigator.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage(
  '/app_tab',
  binding: TabsBinding,
  bindings: [AssetListBinding, MyHomeBinding, WorkflowBinding, UpdateBinding],
)
class TabsPage extends GetView<TabsController> {
  const TabsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final Map<SharedNavBarEnum, Widget> navBarPages = {
      SharedNavBarEnum.home: const MyHomePage(),
      SharedNavBarEnum.assetList: const AssetNavigator(),
      SharedNavBarEnum.scanButton: const SizedBox(),
      SharedNavBarEnum.workflow: const WorkflowNavigator(),
      SharedNavBarEnum.actionList: const ActionNavigator(),
    };

    final List<Widget> pages = SharedNavBarEnum.values.map((e) => navBarPages[e]!).toList();

    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBody: true, // Important: allows the body to extend behind the nav bar
      body: Stack(
        children: [
          Obx(() {
            return IndexedStackWidget(
              index: controller.currentIndex.value,
              children: pages,
              preloadedPages: [SharedNavBarEnum.assetList, SharedNavBarEnum.workflow, SharedNavBarEnum.actionList],
            );
          }),
          // Camera button upper half click area - positioned on top of body content
          // 防止点击穿透：使用HitTestBehavior.opaque阻止点击传递到后面的内容
          Positioned(
            left: MediaQuery.of(context).size.width / 2 - 22,
            bottom: bottomPadding + 60, // 正好在BottomNavBar顶部
            width: 44,
            height: 22,
            child: GestureDetector(
              onTap: () => controller.handleScanTap(),
              behavior: HitTestBehavior.opaque, // 防止点击穿透到后面的内容
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(22), topRight: Radius.circular(22)),
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Obx(() {
        if (controller.showBottomNavBar.value)
          return SizedBox(
            height: 60 + bottomPadding,
            child: Stack(
              children: [
                // White container at the bottom to cover the curve area
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  height: bottomPadding,
                  child: Container(color: Colors.white),
                ),
                // Transparent bottom nav bar above the white area
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: bottomPadding,
                  height: 60,
                  child: Obx(
                    () => BottomNavBar(
                      currentIndex: controller.currentIndex.value,
                      onTabTap: (i) => controller.handleTabTap(SharedNavBarEnum.values[i]),
                      onScanTap: () => controller.handleScanTap(),
                      showWorkflowUnreadHint: controller.showWorkflowUnreadHint.value,
                      showActionTaskUnreadHint: controller.showActionTaskUnreadHint.value,
                    ),
                  ),
                ),
              ],
            ),
          );
        else
          return SizedBox(height: bottomPadding);
      }),
    );
  }
}
