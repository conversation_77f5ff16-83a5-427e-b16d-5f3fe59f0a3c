import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class BottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTabTap;
  final VoidCallback onScanTap;
  final bool showWorkflowUnreadHint;
  final bool showActionTaskUnreadHint;

  const BottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTabTap,
    required this.onScanTap,
    this.showWorkflowUnreadHint = false,
    this.showActionTaskUnreadHint = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      decoration: const BoxDecoration(
        color: Colors.transparent,
        image: DecorationImage(
          image: AssetImage('assets/images/tabs-bg.png'),
          fit: BoxFit.none,
          alignment: Alignment.topCenter,
          scale: 2.0,
        ),
      ),
      child: Row(
        children: [
          _buildTabItem(index: 0, icon: 'assets/icons/icon-my-home.svg', label: 'ホーム', iconSize: const Size(16, 18)),
          _buildTabItem(index: 1, icon: 'assets/icons/icon-assets-v2.svg', label: '資産', iconSize: const Size(19, 21)),
          _buildScanButton(),
          _buildTabItem(
            index: 3,
            icon: 'assets/icons/icon-workflow-v2.svg',
            label: 'WF',
            iconSize: const Size(18, 18),
            showBadge: showWorkflowUnreadHint,
          ),
          _buildTabItem(
            index: 4,
            icon: 'assets/icons/icon-task.svg',
            label: '更新処理',
            iconSize: const Size(21, 18),
            showBadge: showActionTaskUnreadHint,
          ),
        ],
      ),
    );
  }

  Widget _buildTabItem({
    required int index,
    required String icon,
    required String label,
    required Size iconSize,
    bool showBadge = false,
  }) {
    final isSelected = currentIndex == index;

    return Expanded(
      child: InkWell(
        onTap: () => onTabTap(index),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: iconSize.width + 12,
              height: iconSize.height,
              margin: const EdgeInsets.only(top: 4),
              child: Stack(
                clipBehavior: Clip.none,
                alignment: Alignment.center,
                children: [
                  SvgPicture.asset(
                    icon,
                    width: iconSize.width,
                    height: iconSize.height,
                    colorFilter: ColorFilter.mode(
                      isSelected ? const Color(0xFF4D94FF) : const Color(0xFF0B3E86),
                      BlendMode.srcIn,
                    ),
                  ),
                  if (showBadge)
                    Positioned(
                      top: 0,
                      right: _getBadgeRightPosition(index),
                      child: Container(
                        width: 5,
                        height: 5,
                        decoration: const BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 3),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: isSelected ? const Color(0xFF4D94FF) : const Color(0xFF0B3E86),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScanButton() {
    return Expanded(
      child: GestureDetector(
        onTap: onScanTap,
        behavior: HitTestBehavior.translucent,
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.topCenter,
          children: [
            Positioned(
              top: -22,
              child: Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(color: const Color(0xFF0B3E86).withAlpha(51), blurRadius: 6, offset: const Offset(0, 3)),
                  ],
                ),
                child: Material(
                  color: Colors.white,
                  shape: const CircleBorder(),
                  child: SvgPicture.asset('assets/icons/btn_camera.svg', fit: BoxFit.cover),
                ),
              ),
            ),
            const Positioned(
              bottom: 5,
              child: Text(
                'スキャン',
                style: TextStyle(fontSize: 14, color: Color(0xFF0B3E86), fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _getBadgeRightPosition(int index) {
    switch (index) {
      case 3: // WF
        return -5;
      case 4: // 更新処理
        return 1;
      default:
        return -2;
    }
  }
}
