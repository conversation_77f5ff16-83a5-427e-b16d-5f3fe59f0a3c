import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/common_dialog_service.dart';
import 'package:get/get.dart';

class AssetAppurtenancesInformationControllers extends BaseController {
  final RxList<String> filteredAssetAppurtenancesInformationTypes = <String>[].obs;
  AssetAppurtenancesInformationControllers();

  @override
  void onInit() {
    super.onInit();
    filteredAssetAppurtenancesInformationTypes.value = ['履歴情報A'];
  }

  goBackPage() {
    Get.back();
  }

  void updateSearch({required String query}) {}

  /// 履历情报选择中
  selectAppurtenancesInformationType({required dynamic information}) async {
    await CommonDialogService().show(content: '下一步进入履历情报详细${information}');
  }
}
