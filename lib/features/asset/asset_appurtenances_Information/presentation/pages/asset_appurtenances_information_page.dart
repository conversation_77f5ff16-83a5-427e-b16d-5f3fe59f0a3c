import 'package:asset_force_mobile_v2/features/asset/asset_appurtenances_Information/presentation/bindings/asset_appurtenances_information_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_appurtenances_Information/presentation/controllers/asset_appurtenances_information_controllers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/asset_appurtenances_information', binding: AssetAppurtenancesInformationBinding)
class AssetAppurtenancesInformationPage extends GetView<AssetAppurtenancesInformationControllers> {
  const AssetAppurtenancesInformationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text('履歴情報'),
        leading: IconButton(
          key: const Key('asset-type-list-page-back-button'),
          icon: const Icon(Icons.chevron_left),
          onPressed: () => controller.goBackPage(),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(56),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
            child: SizedBox(
              height: 40,
              child: TextField(
                key: const Key('asset-type-list-page-search-field'),
                decoration: InputDecoration(
                  hintText: '履歴情報を検索',
                  prefixIcon: Container(
                    padding: const EdgeInsets.all(8),
                    child: SvgPicture.asset(
                      'assets/icons/icon-search.svg',
                      colorFilter: const ColorFilter.mode(Color(0xFF0B3E86), BlendMode.srcIn),
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                ),
                style: const TextStyle(color: Colors.black, fontSize: 16),
                onChanged: (String text) => controller.updateSearch(query: text),
              ),
            ),
          ),
        ),
      ),
      body: Obx(() {
        final bool appurtenancesInformation = controller.filteredAssetAppurtenancesInformationTypes.isEmpty;
        if (appurtenancesInformation) {
          return Center(child: Text('データがありません', style: Theme.of(context).textTheme.bodyMedium));
        }
        return ListView.builder(
          padding: const EdgeInsets.only(left: 8, right: 8, bottom: 80, top: 8),
          itemCount: controller.filteredAssetAppurtenancesInformationTypes.length,
          itemBuilder: (context, index) {
            final item = controller.filteredAssetAppurtenancesInformationTypes[index];
            return GestureDetector(
              onTap: () => controller.selectAppurtenancesInformationType(information: item),
              child: Container(
                key: Key('appurtenances-information-item-${item}'),
                margin: const EdgeInsets.only(bottom: 10),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [BoxShadow(color: Colors.black.withAlpha(13), offset: const Offset(0, 1), blurRadius: 2)],
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text(
                          item,
                          softWrap: true,
                          style: const TextStyle(
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                            color: Colors.black87,
                            height: 1.3,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 15,
                        height: 15,
                        child: SvgPicture.asset(
                          'assets/icons/angle-right.svg',
                          colorFilter: const ColorFilter.mode(Colors.black, BlendMode.srcIn),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}
