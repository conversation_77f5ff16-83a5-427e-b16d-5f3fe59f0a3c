import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/controllers/category_controller.dart';

/// 参数类：分类数据组装所需的参数
class GetCategoryAssembleListParams {
  final List<Info> categoryOriginalList;
  final List<CategoryData> categoryList;
  final String? lastValue;

  GetCategoryAssembleListParams({required this.categoryOriginalList, required this.categoryList, this.lastValue});
}

/// 返回结果
class GetCategoryAssembleListResult {
  final List<CategoryData> categoryList;

  GetCategoryAssembleListResult({required this.categoryList});
}

/// 分类数据组装 UseCase
class GetCategoryAssembleListUseCase implements UseCase<GetCategoryAssembleListResult, GetCategoryAssembleListParams> {
  @override
  Future<GetCategoryAssembleListResult> call(GetCategoryAssembleListParams params) async {
    try {
      // 验证 categoryOriginalList 是否为空**
      if (params.categoryOriginalList.isEmpty) {
        LogUtil.e('categoryOriginalList is empty');
        throw BusinessException('カテゴリの取得に失敗しました。もう一度お試しください。');
      }

      LogUtil.d('== 开始分类数据组装 ==');

      // 克隆传入的列表以避免修改原数据
      final categoryOriginalListClone = params.categoryOriginalList.toList();
      final categoryListClone = params.categoryList.toList();
      final int categoryListLength = categoryListClone.length;

      // 判断是否到达最后一层或点击了 'すべて'
      if (_isFinalLayer(categoryListLength, categoryOriginalListClone, params.lastValue)) {
        _finalizeCategorySelection(
          categoryListClone: categoryListClone,
          index: categoryListLength,
          lastValue: params.lastValue,
        );
        return GetCategoryAssembleListResult(categoryList: categoryListClone);
      }

      // 获取当前层级的分类信息和创建对应的选中数据对象
      final Info currentInfo = categoryOriginalListClone[categoryListLength];
      final CategorySelectedData selectedData = _createCategorySelectedData(currentInfo, categoryListLength);

      // 更新上一层的选择状态
      _updateCurrentLayerSelection(
        categoryListClone: categoryListClone,
        index: categoryListLength,
        lastValue: params.lastValue,
      );

      // 将当前层级的分类数据添加到列表中
      _addCategoryData(categoryListClone: categoryListClone, ad: currentInfo, selectedData: selectedData);

      LogUtil.d('category选取第 $categoryListLength 层 ===> $categoryListClone');
      return GetCategoryAssembleListResult(categoryList: categoryListClone);
    } catch (e, stackTrace) {
      LogUtil.e('UseCase Error: $e', stackTrace: stackTrace);
      throw BusinessException('カテゴリの取得に失敗しました。もう一度お試しください。');
    }
  }

  /// 判断是否到达最后一层或点击了 'すべて'
  ///
  /// 如果当前索引已经超出原始分类层级长度或最后的选择值为 'すべて'，
  /// 则认为已经到达最终选择层级。
  bool _isFinalLayer(int index, List<Info> categoryOriginalList, String? lastValue) {
    final bool isFinal = index >= categoryOriginalList.length || lastValue == 'すべて';
    LogUtil.d('== 是否到达最后一层: $isFinal ==');
    return isFinal;
  }

  /// 完成最后一层的选择逻辑
  ///
  /// 更新列表中最后一个分类项的选中数据，将其标记为完成状态，并记录最后的选择值。
  void _finalizeCategorySelection({
    required List<CategoryData> categoryListClone,
    required int index,
    required String? lastValue,
  }) {
    final lastSelected = categoryListClone[index - 1];
    lastSelected.categorySelectedData?.categoryItemValue = lastValue ?? '';
    lastSelected.categorySelectedData?.itFinished = true;
    LogUtil.d('== 最后一层选择完成: ${lastValue ?? ''} ==');
  }

  /// 创建一个新的 CategorySelectedData 对象
  ///
  /// 根据传入的 [info] 和层级索引 [idx]，初始化一个新的选中数据对象，
  /// 如果分类类型为 'master'，则额外设置子项 ID。
  CategorySelectedData _createCategorySelectedData(Info info, int idx) {
    final selectedData = CategorySelectedData(
      categoryItemId: info.goBackLevelInfo?.itemId ?? 0,
      itemType: info.goBackLevelInfo?.itemType ?? '',
      index: idx,
      itFinished: false,
    );

    if (info.goBackLevelInfo?.itemType == 'master') {
      selectedData.categorySubItemId = info.goBackLevelInfo?.itemSubId;
    }

    LogUtil.d('== 创建 CategorySelectedData 对象: $selectedData ==');
    return selectedData;
  }

  /// 更新当前层的选择值
  ///
  /// 如果存在上一层分类（即 index > 0），则更新其选中数据的值为 [lastValue]。
  void _updateCurrentLayerSelection({
    required List<CategoryData> categoryListClone,
    required int index,
    required String? lastValue,
  }) {
    if (index > 0) {
      categoryListClone[index - 1].categorySelectedData?.categoryItemValue = lastValue ?? '';
      LogUtil.d('== 当前层级选择更新: ${lastValue ?? ''} ==');
    }
  }

  /// 将新的分类数据添加到列表中
  ///
  /// 使用提供的 [Info] 和 [selectedData] 创建一个新的 CategoryData 对象，并添加到列表中。
  void _addCategoryData({
    required List<CategoryData> categoryListClone,
    required Info ad,
    required CategorySelectedData selectedData,
  }) {
    categoryListClone.add(
      CategoryData(titleVal: ad.titleValue ?? '', categoryInfo: ad.goBackLevelInfo, categorySelectedData: selectedData),
    );
    LogUtil.d('== 添加新的 CategoryData: ${ad.titleValue ?? ''} ==');
  }
}
