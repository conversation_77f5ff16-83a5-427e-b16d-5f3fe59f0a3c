import 'package:asset_force_mobile_v2/features/asset/asset_history/data/models/appurtenances_information_by_type_id.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/domain/enums/asset_history_editor_mode.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/domain/enums/asset_history_source_page.dart';

/// 资产履历编辑页面参数类
///
/// 定义传递给资产履历编辑页面的所有参数，提供类型安全的参数传递机制
/// 支持多种构造方式，确保向后兼容性和未来扩展性
///
/// 使用示例：
/// ```dart
/// // 推荐方式：直接使用枚举
/// final params = AssetHistoryEditorParams(
///   mode: AssetHistoryEditorMode.create,
///   headerText: '请填写资产维护记录',
/// );
///
/// // 使用工厂方法创建（带默认头部文本）
/// final createParams = AssetHistoryEditorParams.create(
///   assetId: 123,
///   headerText: '新建资产履历',
/// );
///
/// // 编辑模式（自动设置头部文本）
/// final editParams = AssetHistoryEditorParams.edit(
///   recordId: 456,
///   isReadOnly: true, // 会自动设置为"查看履历记录"
/// );
///
/// // 从现有参数修改
/// final newParams = params.copyWith(
///   mode: AssetHistoryEditorMode.edit,
///   recordId: 123,
///   headerText: '编辑履历信息',
/// );
///
/// // 清除头部文本
/// final noHeaderParams = params.copyWithNullHeaderText();
/// ```
class AssetHistoryEditorParams {
  /// 编辑模式
  final AssetHistoryEditorMode mode;

  /// 履历记录ID（编辑模式时使用）
  final int? recordId;

  /// 资产ID（关联的资产）
  final int? assetId;

  /// 资产ID列表（一扩插入模式时使用）
  final List<int>? assetIds;

  /// 履历类型ID
  final int? historyTypeId;

  /// 是否只读模式
  final bool isReadOnly;

  /// 来源页面标识（用于返回时的特殊处理）
  final AssetHistorySourcePage? fromPage;

  /// 履历记录数据（编辑模式时使用）
  final AppurtenancesInformationItem? appurtenancesInformationItem;

  /// 是否有编辑权限
  final bool isEditPermission;

  /// 页面头部显示文本
  ///
  /// 用于在履历编辑页面顶部显示自定义的提示信息或说明文本
  /// 常用场景：
  /// - 显示特定的操作提示信息
  /// - 展示与当前履历相关的重要说明
  /// - 提供上下文相关的帮助信息
  /// - 显示来源页面的相关信息
  ///
  /// 示例用法：
  /// ```dart
  /// AssetHistoryEditorParams.create(
  ///   headerText: '请填写资产维护记录的详细信息',
  /// );
  /// ```
  final String? headerText;

  /// 主构造函数
  ///
  /// [mode] 编辑模式，必填参数
  /// [recordId] 履历记录ID，编辑模式时通常需要提供
  /// [assetId] 关联的资产ID
  /// [historyTypeId] 履历类型ID
  /// [isReadOnly] 是否只读模式，默认为 false
  /// [fromPage] 来源页面标识
  /// [headerText] 页面头部显示文本，用于显示自定义提示信息
  /// [appurtenancesInformationItem] 履历记录数据，编辑模式时需要提供
  const AssetHistoryEditorParams({
    required this.mode,
    this.recordId,
    this.assetId,
    this.historyTypeId,
    this.isReadOnly = false,
    this.fromPage,
    this.headerText,
    this.assetIds,
    required this.isEditPermission,
    this.appurtenancesInformationItem,
  });

  /// 验证参数的有效性
  ///
  /// 返回验证结果，如果有错误则返回错误信息
  String? validate() {
    // 编辑模式必须提供记录ID
    if (mode.isEdit && recordId == null) {
      return '编辑模式必须提供履历记录ID';
    }

    // 记录ID必须为正数
    if (recordId != null && recordId! <= 0) {
      return '履历记录ID必须为正数';
    }

    // 资产ID必须为正数
    if (assetId != null && assetId! <= 0) {
      return '资产ID必须为正数';
    }

    // 履历类型ID必须为正数
    if (historyTypeId != null && historyTypeId! <= 0) {
      return '履历类型ID必须为正数';
    }

    return null; // 验证通过
  }

  /// 判断是否为创建模式
  bool get isCreate => mode.isCreate;

  /// 判断是否为编辑模式
  bool get isEdit => mode.isEdit;

  /// 获取页面标题
  String get pageTitle => mode.title;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AssetHistoryEditorParams &&
        other.mode == mode &&
        other.recordId == recordId &&
        other.assetId == assetId &&
        other.historyTypeId == historyTypeId &&
        other.isReadOnly == isReadOnly &&
        other.fromPage == fromPage &&
        other.isEditPermission == isEditPermission &&
        other.headerText == headerText;
  }

  @override
  int get hashCode {
    return Object.hash(mode, recordId, assetId, historyTypeId, isReadOnly, fromPage, isEditPermission, headerText);
  }

  @override
  String toString() {
    return 'AssetHistoryEditorParams('
        'mode: $mode, '
        'recordId: $recordId, '
        'assetId: $assetId, '
        'historyTypeId: $historyTypeId, '
        'isReadOnly: $isReadOnly, '
        'fromPage: $fromPage, '
        'isEditPermission: $isEditPermission, '
        'headerText: $headerText'
        'assetIds: $assetIds'
        'appurtenancesInformationItem: $appurtenancesInformationItem'
        ')';
  }
}
