import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';

class DeleteFileFromS3Params {
  final List<String> fileToDelete;

  DeleteFileFromS3Params({required this.fileToDelete});
}

class DeleteFileFromS3Result {
  final bool success;

  DeleteFileFromS3Result({this.success = false});
}

class DeleteFileFromS3UseCase implements UseCase<DeleteFileFromS3Result, DeleteFileFromS3Params> {
  final S3Repository s3Repository;

  DeleteFileFromS3UseCase({required this.s3Repository});

  @override
  Future<DeleteFileFromS3Result> call(DeleteFileFromS3Params params) async {
    // 删除文件为空
    if (params.fileToDelete.isNotEmpty) {
      LogUtil.d('削除対象のファイル: ${params.fileToDelete.join(',')}');
      try {
        final bool success = await s3Repository.deleteFileFromS3(params.fileToDelete);
        if (success) {
          LogUtil.d('ファイルの削除に成功しました。');
          return DeleteFileFromS3Result(success: true);
        } else {
          LogUtil.d('ファイルの削除に失敗しました。');
        }
      } catch (e) {
        LogUtil.e('ファイルの削除に失敗しました: $e');
      }
    }
    return DeleteFileFromS3Result();
  }
}
