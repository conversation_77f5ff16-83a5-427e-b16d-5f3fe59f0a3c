import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_layout_setting_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_my_account_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';

/// 获取编辑履历情报数据结果
///
/// 包含用户信息和布局设置信息，用于履历编辑功能
class GetEditAppurtDataResult {
  /// 用户信息
  final SharedUserModel? userInfo;

  /// 布局设置列表信息
  final List<SharedLayoutSetting>? layoutSettingList;

  /// 构造函数
  ///
  /// [userInfo] 用户基本信息
  /// [layoutSettingList] 布局设置列表
  GetEditAppurtDataResult({required this.userInfo, required this.layoutSettingList});
}

/// 获取编辑履历情报数据用例
///
/// 负责获取履历编辑所需的用户信息和布局设置信息
class GetEditAppurtDataUseCase implements UseCase<GetEditAppurtDataResult, NoParams> {
  /// 用户仓储接口
  final UserRepository userRepo;

  /// 资产仓储接口
  final AssetRepository assetRepo;

  /// 构造函数
  ///
  /// [userRepo] 用户数据仓储
  /// [assetRepo] 资产数据仓储
  GetEditAppurtDataUseCase({required this.userRepo, required this.assetRepo});

  /// 执行用例逻辑
  ///
  /// [params] 无参数输入
  ///
  /// 返回包含用户信息和布局设置的结果对象
  ///
  /// 抛出异常：
  /// - 当用户信息获取失败时抛出相关异常
  /// - 当布局设置获取失败时抛出相关异常
  @override
  Future<GetEditAppurtDataResult> call(NoParams params) async {
    // 并行获取用户信息和布局设置以提高性能
    final results = await Future.wait([userRepo.getUserInfo(), assetRepo.getLayoutSetting()]);

    // 类型安全的转换
    final userInfoResponse = results[0] as SharedMyAccountModel;
    final layoutSettingResponse = results[1] as AssetLayoutSettingResponse;

    // 构建并返回结果对象
    return GetEditAppurtDataResult(
      userInfo: userInfoResponse.data,
      layoutSettingList: layoutSettingResponse.layoutSettingList,
    );
  }
}
