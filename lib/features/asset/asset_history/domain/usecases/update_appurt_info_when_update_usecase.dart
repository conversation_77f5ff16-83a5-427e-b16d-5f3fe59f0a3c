import 'dart:convert';

import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/data/models/appurtenances_information_by_type_id.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';

/// 更新履历情报时的参数类
///
/// 用于封装更新履历情报对象时所需的参数
class UpdateAppurtInfoWhenUpdateParams {
  /// 履历情报信息项目
  ///
  /// 包含需要更新的履历情报数据
  final AppurtenancesInformationItem item;

  /// 布局设置列表
  ///
  /// 用于确定如何处理和显示各个字段的配置信息
  final List<SharedLayoutSetting> layoutSettings;

  /// 构造函数
  ///
  /// [item] 履历情报信息项目
  /// [layoutSettings] 布局设置列表
  UpdateAppurtInfoWhenUpdateParams({required this.item, required this.layoutSettings});
}

/// 更新履历情报对象用例
///
/// 用于处理履历情报更新时的数据转换和格式化逻辑。
/// 主要功能包括：
/// - 解析履历情报信息文本数据
/// - 根据布局设置处理各种类型的字段
/// - 特殊处理Master类型字段的显示逻辑
/// - 处理Checkbox类型字段的值转换
/// - 生成格式化后的文本项目列表
class UpdateAppurtInfoWhenUpdateUseCase
    implements UseCase<AppurtenancesInformationItem, UpdateAppurtInfoWhenUpdateParams> {
  /// 执行用例逻辑
  ///
  /// 根据布局设置处理履历情报信息，生成格式化的文本项目列表
  ///
  /// 参数:
  /// [params] 包含履历情报信息和布局设置的参数对象
  ///
  /// 返回:
  /// 处理后的履历情报信息项目
  @override
  Future<AppurtenancesInformationItem> call(UpdateAppurtInfoWhenUpdateParams params) async {
    // 解析履历情报信息文本为JSON对象
    final appurInfoTxtObj = jsonDecode(params.item.appurtenancesInformationText ?? '{}');

    // 遍历布局设置，处理每个字段的显示逻辑
    params.item.textItemList = params.layoutSettings.map((mItem) {
      // 初始化显示标题、类型和值
      String? titleNameD = mItem.itemDisplayName;
      String? itemTypeD = mItem.itemType;
      dynamic itemValueD = appurInfoTxtObj[mItem.itemName] ?? '';

      // 处理Master类型字段的特殊逻辑
      if (SharedItemTypeEnum.master.equals(mItem.itemType)) {
        final subInfo = mItem.subInfo;

        // 构建Master类型的显示标题（包含子项目显示名称）
        titleNameD = '${mItem.itemDisplayName}(${subInfo?.itemDisplayName})';
        itemTypeD = subInfo?.itemType;
        itemValueD = subInfo?.itemValue;

        // 从嵌套的display对象中获取实际值
        final subItemObj = (appurInfoTxtObj[mItem.itemName] ?? {})['display'] ?? {};
        itemValueD = subItemObj[subInfo?.itemId?.toString() ?? ''] ?? '';

        // チェックボックス
        if (SharedItemTypeEnum.checkbox.equals(itemTypeD)) {
          final optionObj = OptionObjModel.fromJson(jsonDecode(subInfo?.option ?? '{}'));
          final masterSubCheckboxMultiFlg = optionObj.checkboxMultiFlg;

          // 单选Checkbox的值转换（'1' -> 'あり', 其他 -> 'なし'）
          if (masterSubCheckboxMultiFlg == '0') {
            itemValueD = itemValueD == '1' ? 'あり' : 'なし';
          }
        }
      }
      // 创建文本项目模型
      return TextItemModel(title: titleNameD, value: itemValueD, itemType: itemTypeD, option: mItem.option);
    }).toList();

    // 返回原始的履历情报信息项目
    return params.item;
  }
}
