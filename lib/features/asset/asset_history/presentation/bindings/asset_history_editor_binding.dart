import 'package:asset_force_mobile_v2/core/services/common_dialog_service.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/data/repositories/appurtenances_information_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/domain/repositories/appurtenances_information_repository.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/domain/usecases/delete_file_from_s3_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/domain/usecases/get_appurtenances_information_mobile_setting_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/domain/usecases/get_edit_appurt_data_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/domain/usecases/save_asset_history_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/presentation/controllers/asset_history_editor_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:get/get.dart';

/// 资产履历编辑页面依赖绑定类
///
/// 负责注册和管理资产履历编辑页面所需的所有依赖项
/// 遵循 Clean Architecture 原则，确保依赖注入的正确性
///
/// 依赖注入层次结构：
/// 1. 基础设施层：DioUtil（已在应用启动时注册）
/// 2. 数据层：Repository 实现类
/// 3. 领域层：UseCase 用例类
/// 4. 表现层：Controller 控制器
class AssetHistoryEditorBinding extends Bindings {
  @override
  void dependencies() {
    /// 注册共通对话框服务
    ///
    Get.lazyPut<CommonDialogService>(() => CommonDialogService());

    // ==================== 数据层依赖注册 ====================

    /// 注册用户仓储实现
    ///
    /// 提供用户相关的数据访问功能，包括用户信息获取等
    /// 使用懒加载模式，只在需要时创建实例
    Get.lazyPut<UserRepository>(() => UserRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    /// 注册资产仓储实现
    ///
    /// 提供资产相关的数据访问功能，包括布局设置获取等
    /// 使用懒加载模式，只在需要时创建实例
    Get.lazyPut<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    /// 注册履历信息仓储实现
    ///
    /// 提供履历信息相关的数据访问功能，包括履历数据获取等
    /// 使用懒加载模式，只在需要时创建实例
    Get.lazyPut<AppurtenancesInformationRepository>(() => AppurtenancesInformationRepositoryImpl(Get.find<DioUtil>()));

    // ==================== 领域层依赖注册 ====================

    /// 注册获取编辑履历数据用例
    ///
    /// 负责获取履历编辑所需的用户信息和布局设置信息
    /// 该用例会并行调用用户仓储和资产仓储以提高性能
    ///
    /// 依赖项：
    /// - UserRepository: 获取用户信息
    /// - AssetRepository: 获取布局设置信息
    Get.lazyPut<GetEditAppurtDataUseCase>(
      () => GetEditAppurtDataUseCase(userRepo: Get.find<UserRepository>(), assetRepo: Get.find<AssetRepository>()),
    );

    Get.lazyPut(() => SaveAssetHistoryUseCase(repo: Get.find<AppurtenancesInformationRepository>()));

    /// 注册获取履历信息移动端设置用例
    ///
    /// 负责获取履历信息的移动端显示设置和布局配置
    /// 该用例会调用履历信息仓储和资产仓储获取相关设置
    ///
    /// 依赖项：
    /// - AppurtenancesInformationRepository: 获取履历信息设置
    /// - AssetRepository: 获取布局设置信息
    Get.lazyPut<GetAppurtenancesInformationMobileSettingUseCase>(
      () => GetAppurtenancesInformationMobileSettingUseCase(
        appurRepo: Get.find<AppurtenancesInformationRepository>(),
        assetRepo: Get.find<AssetRepository>(),
      ),
    );

    /// 注册从 S3 删除文件用例
    ///
    Get.lazyPut<DeleteFileFromS3UseCase>(() => DeleteFileFromS3UseCase(s3Repository: Get.find<S3Repository>()));

    // ==================== 表现层依赖注册 ====================

    /// 注册资产履历编辑页面控制器
    ///
    /// 负责管理页面状态、处理用户交互和协调业务逻辑
    /// 集成多个用例以获取编辑所需的数据和履历信息
    ///
    /// 依赖项：
    /// - GetEditAppurtDataUseCase: 获取编辑数据的用例
    /// - GetAppurtenancesInformationMobileSettingUseCase: 获取履历移动端设置的用例
    /// - SaveAssetHistoryUseCase: 保存资产履历的用例
    /// - DeleteFileFromS3UseCase: S3文件删除的用例
    /// - CommonDialogService: 通用对话框服务
    Get.lazyPut<AssetHistoryEditorController>(
      () => AssetHistoryEditorController(
        getEditAppurtDataUseCase: Get.find<GetEditAppurtDataUseCase>(),
        getAppurtenancesInformationMobileSettingUseCase: Get.find<GetAppurtenancesInformationMobileSettingUseCase>(),
        commonDialogService: Get.find<CommonDialogService>(),
        saveAssetHistoryUseCase: Get.find<SaveAssetHistoryUseCase>(),
        deleteFileFromS3UseCase: Get.find<DeleteFileFromS3UseCase>(),
      ),
    );
  }
}
