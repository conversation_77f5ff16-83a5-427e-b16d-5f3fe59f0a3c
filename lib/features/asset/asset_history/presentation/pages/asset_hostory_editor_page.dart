import 'package:asset_force_mobile_v2/core/presentation/widgets/common_app_bar.dart';
import 'package:asset_force_mobile_v2/core/presentation/widgets/common_button.dart';
import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/bindings/af_customize_view_bindings.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/presentation/bindings/asset_history_editor_binding.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_history/presentation/controllers/asset_history_editor_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/builder.dart';

/// 履历情报新规、编辑画面
///
/// 支持新建和编辑两种模式，根据传入的参数动态显示对应的页面标题
/// 使用枚举管理页面状态，确保类型安全和代码可维护性
/// 集成自定义视图组件，支持履历数据的展示和编辑
@GetRoutePage('/asset_history_editor', bindings: [AfCustomizeViewBindings, AssetHistoryEditorBinding])
class AssetHistoryEditorPage extends GetView<AssetHistoryEditorController> {
  // ==================== 尺寸常量 ====================

  /// 头部文本区域底部边距
  /// 用于头部文本区域与下方内容的间距
  static const double _headerTextBottomMargin = 20.0;

  /// 头部文本区域顶部边距
  /// 用于头部文本区域与AppBar的间距
  static const double _headerTextTopMargin = 15.0;

  /// 履历情报种类名水平内边距
  /// 用于履历情报种类名左右两侧的内边距
  static const double _typeNameHorizontalPadding = 16.0;

  /// 底部导航栏顶部内边距
  /// 用于底部导航栏与内容区域的间距
  static const double _bottomNavTopPadding = 10.0;

  /// 底部导航栏左右边距
  /// 用于底部导航栏按钮与屏幕边缘的间距
  static const double _bottomNavSideMargin = 10.0;

  /// 底部导航栏按钮宽度
  /// 用于取消和保存按钮的统一宽度
  static const double _bottomNavButtonWidth = 130.0;

  /// 底部导航栏按钮圆角半径
  /// 用于取消按钮的圆角设置
  static const double _bottomNavButtonBorderRadius = 8.0;

  /// 扫描图标大小
  /// 用于AppBar中扫描按钮的图标尺寸
  static const double _scanIconSize = 24.0;

  // ==================== 字体和样式常量 ====================

  /// 头部文本字体大小
  /// 用于头部文本列表中每行文本的字体大小
  static const double _headerTextFontSize = 16.0;

  /// 头部文本行高
  /// 用于确保头部文本行间距为0的行高设置
  static const double _headerTextLineHeight = 1.0;

  /// 履历情报种类名字体大小
  /// 用于履历情报种类名的字体大小
  static const double _typeNameFontSize = 18.0;

  /// 履历情报种类名字符间距
  /// 用于履历情报种类名的字符间距设置
  static const double _typeNameLetterSpacing = 0.5;

  // ==================== 文本常量 ====================

  /// 取消按钮文本
  /// 用于底部导航栏的取消按钮
  static const String _cancelButtonText = 'キャンセル';

  /// 保存按钮文本
  /// 用于底部导航栏的保存按钮
  static const String _saveButtonText = '保存';

  /// 扫描按钮提示文本
  /// 用于扫描按钮的工具提示
  static const String _scanTooltipText = 'スキャン';

  /// 头部文本句号后缀
  /// 用于头部文本每行末尾自动添加的句号
  static const String _headerTextSuffix = '。';

  const AssetHistoryEditorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: BasicAppBar(
          titleText: controller.pageTitle,
          centerTitle: true,
          showBackButton: true,
          actions: _buildActionMenu(),
          onBackPressed: controller.handleCancelAction,
        ),
        body: _buildBody(),
        bottomNavigationBar: _buildNavigationBar(),
      );
    });
  }

  /// 构建页面主体内容
  ///
  /// 根据编辑模式显示相应的表单内容
  /// 包含头部文本显示区域和履历情报种类名显示区域
  Widget _buildBody() {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Container(
        width: double.infinity,
        child: Column(
          children: [
            // 头部文本显示区域
            _buildHeaderTextList(),

            // 履历情报种类名显示区域
            _buildAppurtenancesInformationTypeName(),

            // 表单内容区域
            _buildFormContent(),
          ],
        ),
      ),
    );
  }

  /// 构建操作菜单
  ///
  /// 根据编辑模式和权限显示相应的操作按钮
  /// 包含扫描、保存等功能按钮，并设置适当的右侧边距
  List<Widget> _buildActionMenu() {
    final List<Widget> actions = [];

    // 扫描按钮 - 在新建模式下显示
    if (!controller.isReadOnly) {
      actions.add(
        IconButton(
          onPressed: controller.handleScanAction,
          icon: const Icon(Icons.qr_code_scanner, color: Colors.white, size: _scanIconSize),
          tooltip: _scanTooltipText,
        ),
      );
    }

    return actions;
  }

  /// 构建底部导航栏
  ///
  /// 根据来源页面决定是否显示底部导航栏
  /// 保存按钮的启用/禁用状态依赖于控制器中的编辑权限
  _buildNavigationBar() {
    return Container(
      padding: const EdgeInsets.only(top: _bottomNavTopPadding),
      color: Colors.white,
      child: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            const SizedBox(width: _bottomNavSideMargin),
            // 取消按钮 - 始终可用
            CommonButton(
              width: _bottomNavButtonWidth,
              text: _cancelButtonText,
              textColor: AppTheme.darkBlueColor,
              borderColor: AppTheme.darkBlueColor,
              borderRadius: _bottomNavButtonBorderRadius,
              backgroundColor: Colors.white,
              fontWeight: FontWeight.bold,
              type: CommonButtonType.text,
              onPressed: controller.handleCancelAction,
            ),
            const Spacer(),
            // 保存按钮 - 根据编辑权限控制启用/禁用状态
            Obx(() {
              final hasEditPermission = controller.isEditPermission.value;

              return CommonButton(
                text: _saveButtonText,
                width: _bottomNavButtonWidth,
                fontWeight: FontWeight.bold,
                // 权限控制：有编辑权限时启用，无权限时禁用
                onPressed: hasEditPermission ? controller.handleSaveAction : null,
                // 禁用状态的视觉反馈：降低透明度
                backgroundColor: hasEditPermission
                    ? AppTheme.darkBlueColor
                    : AppTheme.darkBlueColor.withValues(alpha: 0.5),
              );
            }),
            const SizedBox(width: _bottomNavSideMargin),
          ],
        ),
      ),
    );
  }

  /// 构建头部文本列表显示区域
  ///
  /// 当 headerTextList 不为空时显示
  /// 遍历 headerTextList 数组，每个文本项显示为一行
  /// 每行文本末尾自动添加句号（。）
  /// 样式要求：白色文字、居中对齐、底部间距20px、行间距为0
  Widget _buildHeaderTextList() {
    return Obx(() {
      // 条件渲染：当头部文本列表不为空时显示
      if (controller.headerTextList.isEmpty) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: const EdgeInsets.only(bottom: _headerTextBottomMargin, top: _headerTextTopMargin),
        child: Column(
          children: controller.headerTextList.map((headerText) {
            return Padding(
              padding: EdgeInsets.zero, // 行间距为0
              child: Text(
                '$headerText$_headerTextSuffix', // 每行文本末尾自动添加句号
                textAlign: TextAlign.center, // 居中对齐
                style: const TextStyle(
                  color: Colors.white, // 白色文字
                  fontSize: _headerTextFontSize,
                  height: _headerTextLineHeight, // 行高设置，确保行间距为0
                ),
              ),
            );
          }).toList(),
        ),
      );
    });
  }

  /// 构建履历情报种类名显示区域
  ///
  /// 当 appurtenancesInformationTypeName 不为空时显示
  /// 使用 appurtent_txt 样式类的样式效果
  /// 上边距10px、下边距20px
  Widget _buildAppurtenancesInformationTypeName() {
    return Obx(() {
      // 条件渲染：当履历情报种类名不为空时显示
      if (controller.appurtenancesInformationTypeName.value.isEmpty) {
        return const SizedBox.shrink();
      }

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: _typeNameHorizontalPadding),
        child: Text(
          controller.appurtenancesInformationTypeName.value,
          textAlign: TextAlign.center,
          style: const TextStyle(
            // appurtent_txt 样式类的样式效果
            fontSize: _typeNameFontSize,
            fontWeight: FontWeight.bold,
            color: AppTheme.whiteColor,
            letterSpacing: _typeNameLetterSpacing,
          ),
        ),
      );
    });
  }

  /// 构建表单内容区域
  ///
  /// 集成 AfCustomizeView 组件，用于展示和编辑履历数据
  Widget _buildFormContent() {
    return Obx(() {
      // 检查是否有履历数据
      if (controller.assetDict.isEmpty) {
        return const SizedBox.shrink();
      }
      return Container(
        width: double.infinity,
        margin: const EdgeInsets.all(10),
        padding: const EdgeInsets.fromLTRB(16, 0, 10, 10),
        decoration: const BoxDecoration(
          color: AppTheme.white85Color,
          borderRadius: BorderRadius.all(Radius.circular(10)),
        ),
        child: // 使用 SingleChildScrollView 包装 AfCustomizeView
        AfCustomizeView(
          // 使用资产ID作为实例标识
          instance: '',
          // 传递履历数据字典
          assetDict: controller.assetDict,
          // 设置场景为资产详情编辑
          scene: controller.scene,
          // 不需要section分组，因为编辑页面通常是平铺显示
          needSection: false,
          // 进入时不需要验证，编辑完成时再验证
          needEnterValidate: false,
          // 根据编辑权限设置预览模式
          isPreview: !controller.isEditPermission.value,
          // 显示默认值
          isShowMasterDefaultValue: true,
        ),
      );
    });
  }
}
