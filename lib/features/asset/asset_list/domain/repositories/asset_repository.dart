import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_history_records_count_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_mobile_setting_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/customize_asset_list_model_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/master_layout_setting_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/load_data_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_by_id_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_by_keyword_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_layout_setting_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_relation_list_response.dart';

/// 資産に関連するデータ操作を定義するリポジトリインターフェース。
abstract class AssetRepository {
  /// カスタマイズされた検索結果を取得するメソッド。
  ///
  /// [searchParams]: 検索パラメータ。
  ///
  /// 戻り値:
  /// - 資産 (`Asset`) のリスト。
  Future<CustomizeAssetListModelResponse> getCustomizeSearchForMobile({
    required CustomizeSearchForMobileRequestQuery searchParams,
  });

  /// mobileSettings と assetItemList を取得するメソッド。
  ///
  /// [assetTypeId]: 資産タイプのID。
  ///
  /// 戻り値:
  /// - `AssetMobileResponse` オブジェクト。
  Future<AssetMobileResponse> getAssetMobileSettings({required int assetTypeId});

  /// 获取所有主布局设置的方法。
  ///
  /// [classification]: 分类参数。
  ///
  /// 返回:
  /// - `MasterLayoutSettingResponse` 对象。
  Future<MasterLayoutSettingResponse> getAllMasterLayoutSetting({required int typeId});

  /// 用户关联资产列表
  ///
  /// params:
  /// - assetId: 资产ID
  ///
  /// return：
  /// - `AssetRelationListResponse`类型中包含`assetRelationList`列表
  Future<AssetRelationListResponse> getAssetRelationList({required int assetId});

  /// 获取资产详细信息
  ///
  /// params:
  /// - assetId：资产ID
  ///
  /// return：
  /// - 资产详细信息对象
  Future<AssetByIdResponse> getAssetById({required int assetId});

  /// 返回是否可以打印
  ///
  /// params:
  /// - assetTypeIdsStr: 资产类型ID字符串,如果存在多个的时候将id 数组拼为一个字符串用`,`分割
  Future<bool> getAssetsPrintAble({required String assetTypeIdsStr});

  /// 获取资产类型列表
  ///
  /// params:
  /// - assetTypeId: 资产类型ID
  ///
  /// return:
  /// - 资产类型列表
  Future<AssetItemResponse> getAssetItemType({required String assetTypeId});

  /// 資産情報の保存
  ///
  /// params:
  /// - assetId: 資産ID
  /// - assetTypeId: 資産タイプID
  /// - assetText: 資産テキスト
  Future<BaseResponse> saveAsset({
    required String assetId,
    required String assetTypeId,
    required String assetText,
    String? modifiedDate,
    String? barcode,
    String? relationAssetIdList,
  });

  /// 创建新资产数据
  ///
  /// params:
  /// - assetData: 资产数据Map，包含所有表单数据
  ///
  /// return:
  /// - BaseResponse: 创建结果，包含新资产的ID等信息
  Future<BaseResponse> insertAsset(Map<String, dynamic> assetData);

  /// 资产详细调用
  ///
  /// params:
  /// - typeId: 资产类型ID
  /// - classification: 分类参数
  Future<AssetLayoutSettingResponse> getLayoutSetting({String typeId = '0', String? classification = '5'});

  /// 资产履历情报
  ///
  /// params:
  /// - assetId: 资产ID
  /// - appurtenancesInformationTypeId: 资产履历情报类型ID
  ///
  /// return:
  /// - 返回资产履历情报个数
  Future<AssetHistoryRecordsCountResponse> getAssetHistoryRecordsCount({
    required int assetId,
    required String appurtenancesInformationTypeId,
  });

  /// 根据关键字查找资产
  ///
  /// params:
  /// - assetTypeid: 资产类型ID
  /// - keyword: 关键字
  /// - currentPage: 当前页码
  /// - pageSize: 每页大小
  ///
  /// return:
  /// - 返回资产列表
  Future<AssetByKeywordResponse> findAssetByKeyword({
    required int assetTypeid,
    required String keyword,
    required int currentPage,
    required int pageSize,
  });
}
