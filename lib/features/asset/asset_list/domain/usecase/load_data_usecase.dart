import 'dart:convert';

import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/presentation/models/asset_search_category_ui_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_mobile_setting.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_mobile_setting_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/customize_asset_list_model_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/master_layout_setting_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/get_search_id_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_list_ui_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/models/asset_ui_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_functional_processing_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_type_repository.dart';
import 'package:collection/collection.dart';

/// 规定后台每次都返回的条数
const int backendReturnedAssetNumber = 100;

/// 错误用户未输入检索关键字并且未选择检索条件
const String keywordOrSavedSearchNotImportError = 'キーワードを入力して検索するか、「保存された検索条件」を選択して対象資産を表示してください。';

/// 错误没有数据
const String dataNotAvailableAssetError = 'データがありません';

/// 警告数据获取中
const String loadingAvailableAssetWarn = '読み込んでいます...';

/// 已完成全部数据获取
const String allAvailableAssetDisplayedSuccess = '全件表示されました';

/// 资产一览数据逻辑层
class LoadDataUseCase implements UseCase<LoadDataResult, LoadDataParams> {
  /// 网络请求
  final AssetRepository assetRepository;

  /// 网络请求
  final AssetTypeRepository assetTypeRepository;

  final AssetFunctionalProcessingHelperRepository assetHelper;

  /// 1～5层级接口（資産リストで表示される項目設定）取得
  AssetMobileResponse? _assetMobileResponse;

  /// 当搜索时没有输入任何搜索词条，只有条件检索ID时候，显示在资产一览搜索框中以下文字 （searchUserInput：用户输入的检索文字，isUserInput：为了防止用户也输入同样的検索中 . . .这里单独做一个校验 ）
  // FixedSearching? _fixedSearching;

  // 构造函数
  LoadDataUseCase({required this.assetRepository, required this.assetTypeRepository, required this.assetHelper});

  @override
  Future<LoadDataResult> call(LoadDataParams params) {
    return _loadDataAssetListData(params);
  }

  /// 主实现方法
  Future<LoadDataResult> _loadDataAssetListData(LoadDataParams params) async {
    final assetTypeId = params.assetTypeId;
    final assetSearchConditionList = params.assetSearchConditionList;
    final searchId = params.searchId;
    final keyword = params.keyword;
    final nextPage = params.currentPage;

    // 后台数据默认参数，请求资产一览数据
    final CustomizeSearchForMobileRequestQuery customizeSearch = CustomizeSearchForMobileRequestQuery(
      assetTypeId: assetTypeId,
      rows: backendReturnedAssetNumber,
    );
    // 如果为空
    customizeSearch.searchId = searchId;

    if (customizeSearch.searchId == searchIdConditionZero) {
      customizeSearch.searchId = searchIdConditionTwo;
    }
    final bool isNotKeyWord = keyword.isEmpty || keyword.trim().isEmpty;
    if (isNotKeyWord) {
      customizeSearch.keyword = '';
    } else {
      customizeSearch.keyword = keyword;
    }

    // 式样规定，如果searchId为0情况下，并且用户没有输入任何关键字，那么就返回空页面。
    if (searchId == 0 && isNotKeyWord) {
      LogUtil.w('---絞り込み　はキーワードがいなないです。');
      return LoadDataResult(
        isMoreThenLimit: false,
        searchAssetCount: 0,
        sumAssetCount: 0,
        isLoading: false,
        assetUIModelList: null,
        assetErrorMsg: keywordOrSavedSearchNotImportError,
        assetCategoryList: [],
      );
    }

    if (assetSearchConditionList.isEmpty) {
      // 用户先前没有Category操作，没有设定数据
      customizeSearch.assetSearchConditionList = [];
    } else {
      // 取道用户先前Category设定数据
      customizeSearch.assetSearchConditionList = assetSearchConditionList;
    }

    // 初始化页面
    final int nextSkip = nextPage > 0 ? nextPage - 1 : 0;

    if (nextSkip <= 0) {
      // 首条数据
      customizeSearch.skip = 0;
      // 调优设置,只有刚一进来次页面或者第一页时候调用
      // 用户在web设置一览画面item显示内容，以及Category内容
      // 如果用户改变“資産リストで表示される項目”这时候需要到第一页下拉刷新就会显示新设置的项目，或者切换资产种类也会切换
      _assetMobileResponse = await assetRepository.getAssetMobileSettings(assetTypeId: assetTypeId);
    } else {
      // 从x位开始取到资产，x代表着数据库中针对资产的起始位置
      customizeSearch.skip = nextSkip * backendReturnedAssetNumber;
    }

    // 取得资产列表核心接口
    final CustomizeAssetListModelResponse searchResults = await assetRepository.getCustomizeSearchForMobile(
      searchParams: customizeSearch,
    );
    // 用户检索到的资产条数
    final int searchAssetCount = searchResults.searchAssetCount ?? 0;
    // 用户资产数据是否有下一页`
    final bool moreThenLimit = searchResults.moreThenLimit ?? false;
    // 用户总资产条数
    final int sumAssetCount = searchResults.sumAssetCount ?? 0;

    if (searchAssetCount == 0 || sumAssetCount == 0) {
      // 空资产数据
      LogUtil.w('空数据 searchAssetCount(搜索数据):', searchAssetCount);
      LogUtil.w('空数据 searchAssetCount(搜索数据):', sumAssetCount);
      return LoadDataResult(
        isMoreThenLimit: moreThenLimit,
        searchAssetCount: searchAssetCount,
        sumAssetCount: sumAssetCount,
        isLoading: false,
        assetErrorMsg: dataNotAvailableAssetError,
        assetCategoryList: [],
      );
    }
    // 取得资产列表
    final List<Asset> assetList = searchResults.assetList!;
    // 组装资产列表提供给UI显示
    final List<AssetUIModel> assetUIList = await _dataReorganization(
      assetItemList: assetList,
      assetMobileInfo: _assetMobileResponse!,
    );
    final List<CategoryModel> getCategoryList = await _getAssetCategoryList(assetMobileInfo: _assetMobileResponse!);

    return LoadDataResult(
      isMoreThenLimit: moreThenLimit,
      searchAssetCount: searchAssetCount,
      sumAssetCount: sumAssetCount,
      isLoading: false,
      assetUIModelList: assetUIList,
      assetErrorMsg: null,
      assetCategoryList: getCategoryList,
    );
  }

  /// UI层AsstItemList数据组装
  Future<List<AssetUIModel>> _dataReorganization({
    required List<Asset> assetItemList,
    required AssetMobileResponse assetMobileInfo,
  }) async {
    LogUtil.i('assetItemList: $assetItemList');
    LogUtil.i('assetMobileInfo: $assetMobileInfo');
    final AssetInfo? assetInfo = assetMobileInfo.assetMobileSettingForMobile?.assetInfo;
    final List<SharedLayoutSetting>? layoutSettingList = assetMobileInfo.assetItemList;
    if (layoutSettingList == null) {
      return [];
    }
    if (assetInfo == null) {
      return [];
    }
    // 取道无层的item的id
    final List<AssetItemLevelInfo> assetItemLevelList = [];
    final Set<int> masterTypeIdList = <int>{};
    // 1～5 转为数组，为接下来处理做准备
    for (int e = 1; e < 6; e++) {
      final int? assetLevelId = assetInfo.toJson()['assetLevel${e}ItemId'];
      if (assetLevelId == null) {
        continue;
      }
      final int? assetLevelSubItemId = assetInfo.toJson()['assetLevel${e}SubItemId'];
      final String? assetLevelItemType = assetInfo.toJson()['assetLevel${e}ItemType'];
      final String? assetLevelItemOption = assetInfo.toJson()['assetLevel${e}ItemOption'];

      if (SharedItemTypeEnum.master.equals(assetLevelItemType)) {
        if (assetLevelItemOption == null || assetLevelItemOption.isEmpty) {
          continue;
        }
        final Map<String, dynamic> assetLevelItemOptionObj = jsonDecode(assetLevelItemOption);
        final int? masterTypeId = assetLevelItemOptionObj['masterTypeId'];
        if (masterTypeId == null) {
          continue;
        }
        masterTypeIdList.add(masterTypeId);
      }
      assetItemLevelList.add(
        AssetItemLevelInfo(
          assetLevelItemId: assetLevelId,
          assetLevelItemName: assetInfo.toJson()['assetLevel${e}ItemName'],
          assetLevelItemType: assetLevelItemType,
          assetLevelItemOption: assetLevelItemOption,
          assetLevelSubItemId: assetLevelSubItemId,
        ),
      );
    }
    List<MasterLayoutSettingResponse>? assetAllMasterInfoList;
    if (masterTypeIdList.isNotEmpty) {
      // 通过api取得Master类型的表示名(之前的api都是项目名，所以这里通过另外api取得表示名)
      final Iterable<Future<MasterLayoutSettingResponse>> assetMasterInfoApiList = masterTypeIdList.map(
        (m) => assetRepository.getAllMasterLayoutSetting(typeId: m),
      );
      assetAllMasterInfoList = await Future.wait(assetMasterInfoApiList);
    }

    // 如果为空就证明没有web设置1～5层，则采取默认给予itemId 2 和 3
    if (assetItemLevelList.isEmpty) {
      final List<SharedLayoutSetting> assetDefaultItemList = layoutSettingList
          .where((assetItem) => [2, 3].contains(assetItem.itemId))
          .toList();
      assetItemLevelList.addAll(
        assetDefaultItemList.map(
          (adi) => AssetItemLevelInfo(
            assetLevelItemId: adi.itemId!,
            assetLevelItemName: adi.itemName!,
            assetLevelItemType: adi.itemType!,
            assetLevelItemOption: adi.option,
          ),
        ),
      );
    }

    final List<AssetUIModel> copyAssetItemList = [];
    for (int i = 0; i < assetItemList.length; i++) {
      final Asset assetItem = assetItemList[i];
      final int? assetId = assetItem.assetId;
      if (assetId == null) {
        continue;
      }
      if (assetItem.assetText == null) {
        continue;
      }
      assetItem.assetTextObj ??= jsonDecode(assetItem.assetText!);
      // 此处图片显示需要调用权限接口所以有异步
      final String? assetImageUrl = await assetHelper.getHomeImageFun(
        assetItemTypeSettingList: layoutSettingList,
        assetItem: assetItem,
      );
      // item统一拼装方法
      final List<AssetUIDisplay> defaultItemDisplay = _showAssembleItem(
        assetItemTypeSettingList: layoutSettingList,
        assetItem: assetItem,
        assetItemLevelInfoList: assetItemLevelList,
        assetAllMasterInfoList: assetAllMasterInfoList,
      );
      // 组装
      final AssetUIModel assetItemUI = AssetUIModel(
        assetDisplayList: defaultItemDisplay,
        imageUrl: assetImageUrl,
        assetId: assetId,
      );
      copyAssetItemList.add(assetItemUI);
    }
    return copyAssetItemList;
  }

  /// Category组装
  Future<List<CategoryModel>> _getAssetCategoryList({required AssetMobileResponse assetMobileInfo}) async {
    final ArInfo? arInfo = assetMobileInfo.assetMobileSettingForMobile?.arInfo;
    final List<SharedLayoutSetting>? layoutSettingList = assetMobileInfo.assetItemList;
    if (layoutSettingList == null) {
      return [];
    }
    if (arInfo == null) {
      return [];
    }
    final List<CategoryModel> getCategoryList = [];
    // 1～5 转为数组，为接下来处理做准备
    for (int e = 1; e < 6; e++) {
      final int? assetCategoryItemId = arInfo.toJson()['category${e}ItemId'];
      if (assetCategoryItemId == null) {
        continue;
      }
      final SharedLayoutSetting? layoutSetting = layoutSettingList.firstWhereOrNull(
        (ls) => ls.itemId == assetCategoryItemId,
      );
      if (layoutSetting == null) {
        continue;
      }
      final String? assetCategoryItemName = layoutSetting.itemName;
      if (assetCategoryItemName == null) {
        continue;
      }
      String? assetCategoryItemDisplayName = layoutSetting.itemDisplayName;
      if (assetCategoryItemDisplayName == null) {
        continue;
      }
      final String? assetCategoryItemType = layoutSetting.itemType;
      if (assetCategoryItemType == null) {
        continue;
      }
      final String? assetCategoryItemOption = layoutSetting.option;
      Map<String, dynamic>? assetLevelItemOptionObj;
      final int? assetCategorySubItemId = arInfo.toJson()['category${e}SubItemId'];
      String? assetCategorySubItemName;
      // String? assetCategorySubDisplayName;
      String? assetCategorySubItemType;
      // String? assetCategorySubItemOption;
      if (SharedItemTypeEnum.master.equals(assetCategoryItemType)) {
        if (assetCategoryItemOption == null) {
          continue;
        }
        assetLevelItemOptionObj = jsonDecode(assetCategoryItemOption);
        final int? masterTypeId = assetLevelItemOptionObj?['masterTypeId'];
        if (masterTypeId == null) {
          continue;
        }
        final MasterLayoutSettingResponse assetMasterInfoApi = await assetRepository.getAllMasterLayoutSetting(
          typeId: masterTypeId,
        );
        final List<SharedLayoutSetting>? categoryMasterLayoutSettingList = assetMasterInfoApi.layoutSettingList;
        if (categoryMasterLayoutSettingList == null || categoryMasterLayoutSettingList.isEmpty) {
          continue;
        }
        final SharedLayoutSetting? masterLayoutSettingInfo = categoryMasterLayoutSettingList.firstWhereOrNull(
          (a) => a.itemId == assetCategorySubItemId,
        );
        if (masterLayoutSettingInfo == null) {
          continue;
        }
        assetCategoryItemDisplayName = '$assetCategoryItemDisplayName(${masterLayoutSettingInfo.itemDisplayName})';
        assetCategorySubItemName = masterLayoutSettingInfo.itemName;
        // assetCategorySubDisplayName = masterLayoutSettingInfo.itemDisplayName;
        assetCategorySubItemType = masterLayoutSettingInfo.itemType;
        // assetCategorySubItemOption = masterLayoutSettingInfo.option;
      }

      getCategoryList.add(
        CategoryModel(
          itemId: assetCategoryItemId,
          itemDisplayName: assetCategoryItemDisplayName,
          itemName: assetCategoryItemName,
          itemType: assetCategoryItemType,
          optionObj: assetLevelItemOptionObj,
          itemSubId: assetCategorySubItemId,
          itemSubName: assetCategorySubItemName,
          // itemSubDisplayName: assetCategorySubDisplayName,
          itemSubType: assetCategorySubItemType,
          // subOption: assetCategorySubItemOption
        ),
      );
    }
    return getCategoryList;
  }

  /// item统一拼装方法
  List<AssetUIDisplay> _showAssembleItem({
    required List<SharedLayoutSetting> assetItemTypeSettingList,
    required Asset assetItem,
    required List<AssetItemLevelInfo> assetItemLevelInfoList,
    List<MasterLayoutSettingResponse>? assetAllMasterInfoList,
  }) {
    if (assetItemTypeSettingList.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> assetItemTypeSettingList 值为空');
      return [];
    }
    if (assetItemLevelInfoList.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> assetItemLevelInfoList 值为空');
      return [];
    }
    final assetTextObj = assetItem.assetTextObj;
    if (assetTextObj == null || assetTextObj.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> assetTextObj 值为空');
      return [];
    }
    final List<AssetUIDisplay> assetUIDisplayList = [];
    // 'number', 'currency', 'calculate', 'date', 'appurInfoSummary' 类型处理
    final List<SharedItemTypeEnum> mathTypeList = [
      SharedItemTypeEnum.number,
      SharedItemTypeEnum.currency,
      SharedItemTypeEnum.calculate,
      SharedItemTypeEnum.date,
      SharedItemTypeEnum.appurInfoSummary,
    ];

    for (int i = 0; i < assetItemLevelInfoList.length; i++) {
      final AssetItemLevelInfo assetItemLevelInfo = assetItemLevelInfoList[i];
      final int assetLevelItemId = assetItemLevelInfo.assetLevelItemId;
      SharedLayoutSetting? assetItemTypeSetting;
      if (assetLevelItemId == 0) {
        assetItemTypeSetting = SharedLayoutSetting(
          itemId: assetLevelItemId,
          itemName: '資産種類',
          itemDisplayName: '資産種類',
          itemType: SharedItemTypeEnum.input.value,
        );
      } else {
        assetItemTypeSetting = assetItemTypeSettingList.firstWhereOrNull((a) => a.itemId == assetLevelItemId);
      }
      if (assetItemTypeSetting == null) {
        // 能进入当前错误基本上是数据库出现错误
        LogUtil.e('数据库值返回错误 ==> assetItemTypeSetting 值为空');
        continue;
      }
      if (assetItemTypeSetting.isNotShowMobileLayout) {
        // 移动端不显示标志符
        LogUtil.d('web设置为移动端不显示 --> item:', assetItemTypeSetting.toString());
        continue;
      }
      final String? assetLevelItemType = assetItemTypeSetting.itemType;
      AssetItemInfo? assetItemInfo;
      if (SharedItemTypeEnum.contains(itemTypeEnumList: mathTypeList, value: assetLevelItemType)) {
        // 需要处理数字类型
        assetItemInfo = _assembledMathTypeItem(
          assetItemTypeSetting: assetItemTypeSetting,
          layoutSettingList: assetItemTypeSettingList,
          assetTextObj: assetTextObj,
        );
      } else if (SharedItemTypeEnum.master.equals(assetLevelItemType)) {
        // 需要master类型
        assetItemInfo = _assembledMasterTypeItem(
          assetItemTypeSetting: assetItemTypeSetting,
          assetTextObj: assetTextObj,
          assetItemLevelInfo: assetItemLevelInfo,
          assetAllMasterInfoList: assetAllMasterInfoList,
        );
      } else {
        // 其他类型
        assetItemInfo = _assembledTextTypeItem(assetItemTypeSetting: assetItemTypeSetting, assetTextObj: assetTextObj);
      }
      if (assetItemInfo == null) {
        continue;
      }
      assetUIDisplayList.add(
        AssetUIDisplay(title: assetItemInfo.assetItemDisplayTitle, content: assetItemInfo.assetItemDisplayValue),
      );
    }
    return assetUIDisplayList;
  }

  /// 组装需要计算与转换数组类型
  AssetItemInfo? _assembledMathTypeItem({
    required SharedLayoutSetting assetItemTypeSetting,
    required List<SharedLayoutSetting> layoutSettingList,
    required Map<String, dynamic> assetTextObj,
  }) {
    final String? itemName = assetItemTypeSetting.itemName;
    final String? itemDisplayName = assetItemTypeSetting.itemDisplayName;
    final String? itemType = assetItemTypeSetting.itemType;
    if (itemName == null || itemDisplayName == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> itemName 或 itemDisplayName 值为空');
      return null;
    }
    if (itemName.isEmpty || itemDisplayName.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> itemName 或 itemDisplayName 值为空');
      return null;
    }
    final dynamic itemValue = assetTextObj[itemName];

    if (SharedItemTypeEnum.number.equals(itemType)) {
      String? displayVal = assetHelper.assembledNumberTypeValue(layoutSetting: assetItemTypeSetting, value: itemValue);
      displayVal ??= '';
      return AssetItemInfo(assetItemDisplayTitle: itemDisplayName, assetItemDisplayValue: displayVal);
    }
    if (SharedItemTypeEnum.currency.equals(itemType)) {
      String? displayVal = assetHelper.assembledCurrencyTypeValue(
        layoutSetting: assetItemTypeSetting,
        value: itemValue,
      );
      displayVal ??= '';
      return AssetItemInfo(assetItemDisplayTitle: itemDisplayName, assetItemDisplayValue: displayVal);
    }
    if (SharedItemTypeEnum.appurInfoSummary.equals(itemType)) {
      String? displayVal = assetHelper.assembledAppurInfoSummaryTypeValue(
        layoutSetting: assetItemTypeSetting,
        value: itemValue,
      );
      displayVal ??= '';
      return AssetItemInfo(assetItemDisplayTitle: itemDisplayName, assetItemDisplayValue: displayVal);
    }
    if (SharedItemTypeEnum.date.equals(itemType)) {
      String? displayVal = assetHelper.assembledDateTimeTypeValue(
        layoutSetting: assetItemTypeSetting,
        value: itemValue,
      );
      displayVal ??= '';
      return AssetItemInfo(assetItemDisplayTitle: itemDisplayName, assetItemDisplayValue: displayVal);
    }

    if (SharedItemTypeEnum.calculate.equals(itemType)) {
      String? displayVal = assetHelper.assembledCalculateTypeValue(
        layoutSetting: assetItemTypeSetting,
        value: itemValue,
      );
      displayVal ??= '';
      return AssetItemInfo(assetItemDisplayTitle: itemDisplayName, assetItemDisplayValue: displayVal);
    }

    return null;
  }

  /// 组装master类型
  AssetItemInfo? _assembledMasterTypeItem({
    required SharedLayoutSetting assetItemTypeSetting,
    required Map<String, dynamic> assetTextObj,
    required AssetItemLevelInfo assetItemLevelInfo,
    List<MasterLayoutSettingResponse>? assetAllMasterInfoList,
  }) {
    final String? itemName = assetItemTypeSetting.itemName;
    final String? itemDisplayName = assetItemTypeSetting.itemDisplayName;
    final String? option = assetItemTypeSetting.option;
    if (assetAllMasterInfoList == null || assetAllMasterInfoList.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> assetAllMasterInfoList 值为空');
      return null;
    }
    if (itemName == null || itemDisplayName == null || option == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> itemName 或 itemDisplayName 或 option 值为空');
      return null;
    }
    if (itemName.isEmpty || itemDisplayName.isEmpty || option.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> itemName 或 itemDisplayName 或 option 值为空');
      return null;
    }
    final Map<String, dynamic>? optionObj = jsonDecode(option);
    if (optionObj == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> optionObj 值为空');
      return null;
    }
    final int? masterTypeId = optionObj['masterTypeId'];
    if (masterTypeId == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> masterTypeId 值为空');
      return null;
    }
    final MasterLayoutSettingResponse? masterLayoutSettingResInfo = assetAllMasterInfoList.firstWhereOrNull(
      (a) => a.layoutSettingList?[0].typeId == masterTypeId,
    );
    if (masterLayoutSettingResInfo == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> masterLayoutSettingResInfo 值为空');
      return null;
    }
    final int? subItemId = assetItemLevelInfo.assetLevelSubItemId;
    if (subItemId == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> subItemId 值为空');
      return null;
    }
    final masterLayoutSettingList = masterLayoutSettingResInfo.layoutSettingList;
    if (masterLayoutSettingList == null || masterLayoutSettingList.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> masterLayoutSettingList 值为空');
      return null;
    }
    final SharedLayoutSetting? masterDisplayItem = masterLayoutSettingList.firstWhereOrNull(
      (ml) => ml.itemId == subItemId,
    );
    if (masterDisplayItem == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> masterDisplayItem 值为空');
      return null;
    }
    final String subAssetDisplayName = masterDisplayItem.itemDisplayName ?? '';
    final dynamic itemValue = assetTextObj[itemName];
    final String itemMasterDisplayName = '$itemDisplayName-($subAssetDisplayName)';
    // checkbox 会是List， 其他可能为string，这个值需要根据itemType来推断类型
    dynamic displayVal;
    if (itemValue == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> itemValue 值为空');
      return null;
    }
    if (itemValue is String) {
      displayVal = itemValue;
    }
    if (itemValue is Map<String, dynamic>) {
      final Map<String, dynamic>? displayMap = itemValue['display'];
      if (displayMap == null) {
        displayVal = '';
      } else {
        displayVal = displayMap[subItemId.toString()];
      }
    }

    if (SharedItemTypeEnum.checkbox.equals(masterDisplayItem.itemType)) {
      final String? checkboxValue = assetHelper.assembledCheckboxTypeValue(
        layoutSetting: masterDisplayItem,
        value: displayVal,
      );
      if (checkboxValue == null) {
        displayVal = '';
      } else {
        displayVal = checkboxValue;
      }
    }

    if (SharedItemTypeEnum.number.equals(masterDisplayItem.itemType)) {
      displayVal = assetHelper.assembledNumberTypeValue(layoutSetting: masterDisplayItem, value: displayVal);
    }
    displayVal ??= '';
    return AssetItemInfo(assetItemDisplayTitle: itemMasterDisplayName, assetItemDisplayValue: displayVal as String);
  }

  /// 组装文本类型
  AssetItemInfo? _assembledTextTypeItem({
    required SharedLayoutSetting assetItemTypeSetting,
    Map<String, dynamic>? assetTextObj,
  }) {
    final String? itemName = assetItemTypeSetting.itemName;
    final String? itemDisplayName = assetItemTypeSetting.itemDisplayName;
    if (itemName == null || itemDisplayName == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> itemName 或 itemDisplayName 值为空');
      return null;
    }
    if (itemName.isEmpty || itemDisplayName.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> itemName 或 itemDisplayName 值为空');
      return null;
    }
    if (assetTextObj == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> assetTextObj 值为空');
      return null;
    }
    final String itemValue = assetHelper.itemValueToString(value: assetTextObj[itemName]);
    return AssetItemInfo(assetItemDisplayTitle: itemDisplayName, assetItemDisplayValue: itemValue);
  }
}

/// 传进资产一览逻辑层参数
class LoadDataParams {
  int searchId;
  List<AssetSearchCategoryUIModel> assetSearchConditionList;
  String keyword;
  int assetTypeId;
  int currentPage;
  LoadDataParams({
    required this.searchId,
    required this.assetSearchConditionList,
    required this.keyword,
    required this.currentPage,
    required this.assetTypeId,
  });
}

///  资产请求数据，后台需要数据
class CustomizeSearchForMobileRequestQuery {
  int? assetTypeId;
  int? rows;
  int? skip;
  List<AssetSearchCategoryUIModel>? assetSearchConditionList;
  int? searchId;
  String? keyword;

  // 构造函数
  CustomizeSearchForMobileRequestQuery({
    this.assetTypeId,
    this.rows,
    this.skip,
    this.assetSearchConditionList,
    this.searchId,
    this.keyword,
  });

  // 将类转换为 Map (通常用于与 API 或数据库交互)
  Map<String, dynamic> toMap() {
    return {
      'assetTypeId': assetTypeId,
      'rows': rows,
      'skip': skip,
      'assetSearchConditionList': assetSearchConditionList,
      'searchId': searchId,
      'keyword': keyword,
    };
  }
}

/// 检索需要
class FixedSearching {
  String? searchUserInput;
  bool? isUserInput;

  FixedSearching({this.searchUserInput, this.isUserInput});

  factory FixedSearching.fromMap(Map<String, dynamic> map) {
    return FixedSearching(searchUserInput: map['searchUserInput'], isUserInput: map['isUserInput']);
  }

  Map<String, dynamic> toMap() {
    return {'searchUserInput': searchUserInput, 'isUserInput': isUserInput};
  }
}

/// 页面资产中item每层的要显示细节
class AssetItemLevelInfo {
  // 資産項目1
  int assetLevelItemId;
  // 資産項目5名
  String? assetLevelItemName;
  // 資産項目5タイプ
  String? assetLevelItemType;
  // 資産項目5オプション
  String? assetLevelItemOption;
  // サブ資産項目
  int? assetLevelSubItemId;

  AssetItemLevelInfo({
    required this.assetLevelItemId,
    required this.assetLevelItemName,
    required this.assetLevelItemType,
    this.assetLevelItemOption,
    this.assetLevelSubItemId,
  });

  factory AssetItemLevelInfo.fromMap(Map<String, dynamic> map) {
    return AssetItemLevelInfo(
      assetLevelItemId: map['assetLevelItemId'],
      assetLevelItemName: map['assetLevelItemName'],
      assetLevelItemType: map['assetLevelItemType'],
      assetLevelItemOption: map['assetLevelItemOption'],
      assetLevelSubItemId: map['assetLevelSubItemId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'assetLevelItemId': assetLevelItemId,
      'assetLevelItemName': assetLevelItemName,
      'assetLevelItemType': assetLevelItemType,
      'assetLevelItemOption': assetLevelItemOption,
      'assetLevelSubItemId': assetLevelSubItemId,
    };
  }
}

/// 显示到页面的名称和值
class AssetItemInfo {
  // 資産表示名
  String assetItemDisplayTitle;
  // 資産表示值
  String assetItemDisplayValue;

  AssetItemInfo({required this.assetItemDisplayTitle, required this.assetItemDisplayValue});

  factory AssetItemInfo.fromMap(Map<String, dynamic> map) {
    return AssetItemInfo(
      assetItemDisplayTitle: map['assetItemDisplayTitle'],
      assetItemDisplayValue: map['assetItemDisplayValue'],
    );
  }

  Map<String, dynamic> toMap() {
    return {'assetItemDisplayTitle': assetItemDisplayTitle, 'assetItemDisplayValue': assetItemDisplayValue};
  }
}
