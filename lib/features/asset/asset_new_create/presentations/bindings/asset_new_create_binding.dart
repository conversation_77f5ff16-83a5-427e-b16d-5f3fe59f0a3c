import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/data/models/asset_new_create_arguments_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/domain/usecase/asset_new_create_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/domain/usecase/insert_asset_usecase.dart'
    as AssetInsert;
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/presentations/controllers/asset_new_create_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/presentations/states/asset_new_create_ui_state.dart';
import 'package:get/get.dart';

class AssetNewCreateBinding extends Bindings {
  @override
  void dependencies() {
    final argsMap = Get.arguments as Map<String, dynamic>?;
    final assetNewCreateArguments = argsMap != null
        ? AssetNewCreateArguments.fromMap(argsMap)
        : throw Exception('参数缺失');

    // 首先注册 UI 状态
    Get.put(AssetNewCreateUiState());

    Get.lazyPut<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPut<AssetNewCreateUseCase>((() => AssetNewCreateUseCase(assetRepository: Get.find<AssetRepository>())));

    Get.lazyPut<AssetInsert.InsertAssetUseCase>(
      () => AssetInsert.InsertAssetUseCase(assetRepository: Get.find<AssetRepository>()),
    );

    // 最后注册控制器
    Get.lazyPut(
      () => AssetNewCreateController(
        assetNewCreateArguments: assetNewCreateArguments,
        useCase: Get.find<AssetNewCreateUseCase>(),
        insertAssetUseCase: Get.find<AssetInsert.InsertAssetUseCase>(),
      ),
    );
  }
}
