import 'dart:convert';

import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/user_select_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_asset_item_wrapper.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/check_validate_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/states/asset_relation_state.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/data/models/asset_new_create_arguments_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/domain/usecase/asset_new_create_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/domain/usecase/insert_asset_usecase.dart'
    as AssetInsert;
import 'package:asset_force_mobile_v2/features/asset/asset_new_create/presentations/states/asset_new_create_ui_state.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:get/get.dart';

/// 资产新建页面控制器
///
/// 该控制器负责管理资产新建页面的所有业务逻辑和用户交互。
/// 采用GetX的MVVM架构模式，分离UI逻辑和业务逻辑。
class AssetNewCreateController extends BaseController with GetSingleTickerProviderStateMixin {
  /// 资产新建页面的导航参数
  final AssetNewCreateArguments assetNewCreateArguments;

  /// UI状态管理实例
  late AssetNewCreateUiState uiState;

  /// 资产关系状态管理
  final AssetRelationState relationState = AssetRelationState();

  /// 业务逻辑处理实例
  final AssetNewCreateUseCase useCase;

  /// 资产创建业务逻辑处理实例
  final AssetInsert.InsertAssetUseCase insertAssetUseCase;

  /// 构造函数
  AssetNewCreateController({
    required this.assetNewCreateArguments,
    required this.useCase,
    required this.insertAssetUseCase,
  }) {
    uiState = Get.find<AssetNewCreateUiState>();
  }

  /// 页面初始化方法
  @override
  void onInit() {
    super.onInit();
    configureNavigationBarVisibility();
  }

  /// 页面准备就绪方法
  @override
  Future<void> onReady() async {
    super.onReady();

    // 参照资产详细画面的方式，在loading包装下初始化数据
    await initPageDataOnFirstLoad();
  }

  /// 首次页面数据加载
  /// 参照asset_detail_controller.dart的模式，使用loading包装避免初始错误显示
  Future<void> initPageDataOnFirstLoad() async {
    await executeWithLoading(
      _loadAllData,
      showLoadingDialog: true,
      errorMessage: '資産項目データの取得に失敗しました',
      onError: _handleError,
    );
  }

  /// 核心数据加载逻辑
  /// 包含所有数据初始化逻辑，参照资产详细画面的模式
  Future<void> _loadAllData() async {
    uiState.assetTypeList.value = assetNewCreateArguments.assetType;

    LogUtil.d('AssetNewCreateController onReady - 资产类型列表长度: ${uiState.assetTypeList.length}');

    if (uiState.assetTypeList.isNotEmpty) {
      final assetTypeId = uiState.assetTypeList.first.assetTypeId ?? 0;
      LogUtil.d('AssetNewCreateController - 开始获取资产项目，资产类型ID: $assetTypeId');
      LogUtil.d('AssetNewCreateController - 使用条形码: ${assetNewCreateArguments.barcode}');

      final params = AssetNewCreateParams(assetTypeId: assetTypeId, barcode: assetNewCreateArguments.barcode);

      // 获取经过数据处理的资产项目数据（包含barcode设置等）
      final processedData = await useCase.call(params);

      final sectionDic = processedData['sectionDic'] as Map<String, List<dynamic>>? ?? {};
      final sectionList = processedData['sectionList'] as List<String>? ?? [];

      // 直接使用处理过的数据
      uiState.assetDict = sectionDic;

      LogUtil.d('AssetNewCreateController - 可见节数量: ${sectionList.length}');
      LogUtil.d('AssetNewCreateController - 资产数据字典设置完成，包含barcode: ${assetNewCreateArguments.barcode}');
    } else {
      LogUtil.d('AssetNewCreateController - 资产类型列表为空');
      uiState.assetDict = <String, List<dynamic>>{};
    }

    configureNavigationBarVisibility();
  }

  /// 统一的错误处理方法
  /// 参照asset_detail_controller.dart的错误处理模式
  void _handleError(dynamic e, String message) {
    LogUtil.d('AssetNewCreateController - Error: $message');

    // 对于页面初始化错误，不立即显示弹窗，让用户有机会查看页面状态
    // 可以考虑在用户尝试操作时再提示错误
  }

  /// 取消操作处理方法
  void onCancel() {
    Get.back();
  }

  /// 资产登录/注册操作处理方法
  void onRegister() async {
    try {
      LogUtil.d('AssetNewCreateController - 开始注册处理');

      // 使用与资产详细页面相同的验证逻辑
      if (!(await _validateFormWithCheckValidateUseCase())) {
        LogUtil.d('AssetNewCreateController - 验证失败，停止注册');
        return;
      }

      LogUtil.d('AssetNewCreateController - 验证通过，显示确认对话框');

      CommonDialog.show(
        content: 'この資産を登録しますか？',
        type: DialogType.confirm,
        cancelText: 'キャンセル',
        confirmText: 'OK',
        onConfirm: () async {
          await _executeAssetCreation();
        },
      );
    } catch (e) {
      LogUtil.d('AssetNewCreateController - 注册处理异常: $e');
      CommonDialog.show(content: '登録処理中にエラーが発生しました', type: DialogType.error);
    }
  }

  /// 执行资产创建的私有方法
  Future<void> _executeAssetCreation() async {
    try {
      showLoading();

      final formData = collectFormData();
      if (formData.isEmpty) {
        CommonDialog.show(content: 'フォームデータの収集に失敗しました', type: DialogType.error);
        return;
      }

      formData['資産種類'] = uiState.assetTypeList.isNotEmpty ? uiState.assetTypeList.first.assetTypeName : '';
      formData['作成日'] = DateTime.now().toIso8601String();

      _cleanTurlFields(formData);

      final assetTextJson = jsonEncode(formData);
      LogUtil.d('AssetNewCreateController - assetTextJson type: ${assetTextJson.runtimeType}');
      LogUtil.d('AssetNewCreateController - assetTextJson value: $assetTextJson');

      final insertParams = AssetInsert.InsertAssetParams(
        assetData: {
          'assetTypeId': uiState.assetTypeList.isNotEmpty ? uiState.assetTypeList.first.assetTypeId.toString() : '',
          'barcode': assetNewCreateArguments.barcode ?? '',
          'assetText': assetTextJson,
        },
      );

      LogUtil.d('AssetNewCreateController - insertParams.assetData: ${insertParams.assetData}');
      LogUtil.d(
        'AssetNewCreateController - assetText in params type: ${insertParams.assetData['assetText'].runtimeType}',
      );

      final result = await insertAssetUseCase.call(insertParams);

      if (result.isSuccess()) {
        await _handleCreationSuccess(result, formData);
      } else {
        await _handleCreationFailure(result);
      }
    } catch (e) {
      LogUtil.d('AssetNewCreateController - 创建执行异常: $e');

      // 如果是BusinessException，显示具体的业务错误信息
      if (e is BusinessException) {
        final errorMessage = e.message.isNotEmpty ? e.message : '資産の作成に失敗しました';

        // 检查是否是条码重复错误
        if (errorMessage.contains('既に存在している') && errorMessage.contains('識別コード')) {
          // 提取条码和场所信息（如果可能）
          _showDuplicateBarcodeDialog(errorMessage);
        } else {
          // 其他业务错误直接显示
          CommonDialog.show(content: errorMessage, type: DialogType.error);
        }
      } else {
        // 其他异常显示通用错误信息
        CommonDialog.show(content: '資産の作成中にエラーが発生しました: ${e.toString()}', type: DialogType.error);
      }
    } finally {
      hideLoading();
    }
  }

  /// 处理创建成功的情况
  Future<void> _handleCreationSuccess(dynamic result, Map<String, dynamic> formData) async {
    try {
      LogUtil.d('AssetNewCreateController - 资产创建成功');

      // 使用与资产详情页面相同的Toast样式
      CommonDialog.showCustomToast('${formData['assetName'] ?? '資産'}の登録が完了しました');

      // 延迟后自动返回上一页
      await Future.delayed(const Duration(milliseconds: 2000));
      Get.back();
      Get.back();
    } catch (e) {
      LogUtil.d('AssetNewCreateController - 处理创建成功结果时异常: $e');
    }
  }

  /// 处理创建失败的情况
  Future<void> _handleCreationFailure(dynamic result) async {
    try {
      LogUtil.d('AssetNewCreateController - 资产创建失败: ${result.msg}');

      // 检查是否包含服务器返回的错误代码信息
      if (result is Map<String, dynamic>) {
        final code = result['code'];
        final msg = result['msg'] as String?;

        // 处理 errorCode 99 的情况（参照资产详细页面的处理方式）
        if (code == 99) {
          if (msg != null && msg.isNotEmpty) {
            // 检查是否是条码重复错误
            if (msg.contains('既に存在している') && msg.contains('識別コード')) {
              _showDuplicateBarcodeDialog(msg);
            } else {
              // 其他 99 错误代码情况，参照 Utils.systemError 的处理方式
              CommonDialog.show(content: msg, type: DialogType.error);
            }
          } else {
            CommonDialog.show(content: '不明なエラーが発生しました。管理者までご連絡ください。', type: DialogType.error);
          }
          return;
        }

        // 处理其他错误代码
        if (msg != null && msg.isNotEmpty) {
          CommonDialog.show(content: msg, type: DialogType.error);
        } else {
          CommonDialog.show(content: '資産の作成に失敗しました', type: DialogType.error);
        }
      } else {
        CommonDialog.show(content: result.msg ?? '資産の作成に失敗しました', type: DialogType.error);
      }
    } catch (e) {
      LogUtil.d('AssetNewCreateController - 处理创建失败结果时异常: $e');
      CommonDialog.show(content: '資産の作成に失敗しました', type: DialogType.error);
    }
  }

  /// 显示条码重复错误的专门对话框
  void _showDuplicateBarcodeDialog(String errorMessage) {
    CommonDialog.show(
      content: errorMessage,
      type: DialogType.error,
      confirmText: 'OK',
      onConfirm: () {
        // 关闭对话框，用户可以修改表单内容
      },
    );
  }

  /// 菜单按钮点击处理方法
  void onMenuTap() {
    Get.snackbar('提示', '点击了右上角按钮');
  }

  /// 验证表单数据的完整性和正确性
  Future<bool> validateForm() async {
    try {
      LogUtil.d('AssetNewCreateController - 表单验证：开始验证');

      final errors = <String>[];

      if (uiState.assetDict.isEmpty) {
        errors.add('表单数据为空，请刷新页面重试');
        _showValidationErrors(errors);
        return false;
      }

      if (uiState.assetTypeList.isEmpty) {
        errors.add('资产类型信息缺失');
        _showValidationErrors(errors);
        return false;
      }

      for (final sectionName in uiState.assetDict.keys) {
        final sectionItems = uiState.assetDict[sectionName];
        if (sectionItems != null) {
          for (final item in sectionItems) {
            if (item.sysSetFlg == '1' || item.inputFlg == '0') {
              continue;
            }

            // 检查是否必填，使用optionObject中的check字段
            final isRequired = item.optionObject?.check == '1';
            if (isRequired) {
              // 检查空值 - 根据数据类型进行不同的空值检查
              bool isEmpty = false;

              if (item.defaultData == null) {
                isEmpty = true;
              } else if (item.defaultData is String) {
                isEmpty = (item.defaultData as String).isEmpty;
              } else if (item.defaultData is Map) {
                isEmpty = (item.defaultData as Map).isEmpty;
              } else if (item.defaultData is List) {
                isEmpty = (item.defaultData as List).isEmpty;
              } else if (item.defaultData is UserSelectModel) {
                // UserSelectModel被认为是有效值，不为空
                isEmpty = false;
              } else {
                // 对于其他类型，如果有值就认为不为空
                isEmpty = false;
              }

              if (isEmpty) {
                final displayName = item.itemDisplayName?.isNotEmpty == true ? item.itemDisplayName : item.itemName;
                errors.add('$displayName は必須項目です');
              }
            }

            // 如果有数据，验证数据格式
            bool hasData = false;

            if (item.defaultData != null) {
              if (item.defaultData is String) {
                hasData = (item.defaultData as String).isNotEmpty;
              } else if (item.defaultData is Map) {
                hasData = (item.defaultData as Map).isNotEmpty;
              } else if (item.defaultData is List) {
                hasData = (item.defaultData as List).isNotEmpty;
              } else if (item.defaultData is UserSelectModel) {
                hasData = true; // UserSelectModel总是有数据
              } else {
                hasData = true; // 其他类型如果不为null就认为有数据
              }
            }

            if (hasData) {
              if (!_validateFieldFormat(item)) {
                final displayName = item.itemDisplayName?.isNotEmpty == true ? item.itemDisplayName : item.itemName;
                errors.add('$displayName の形式が正しくありません');
              }
            }
          }
        }
      }

      if (errors.isNotEmpty) {
        _showValidationErrors(errors);
        return false;
      }

      LogUtil.d('AssetNewCreateController - 表单验证：验证通过');
      return true;
    } catch (e) {
      LogUtil.d('AssetNewCreateController - 表单验证时发生错误: $e');
      CommonDialog.show(content: '検証中にエラーが発生しました', type: DialogType.error);
      return false;
    }
  }

  /// 使用 CheckValidateUseCase 进行全面验证（与资产详细页面相同的验证逻辑）
  Future<bool> _validateFormWithCheckValidateUseCase() async {
    try {
      LogUtil.d('AssetNewCreateController - 开始使用 CheckValidateUseCase 验证');

      // 获取 CheckValidateUseCase 实例
      final checkValidateUseCase = Get.find<CheckValidateUseCase>();

      // 验证所有资产项目
      final errorItems = <CheckValidateResult>[];

      for (final sectionName in uiState.assetDict.keys) {
        final sectionItems = uiState.assetDict[sectionName];
        if (sectionItems != null) {
          for (final item in sectionItems) {
            // 创建 RxAssetItemWrapper 包装器（与资产详细页面相同的数据结构）
            final wrapper = RxAssetItemWrapper.fromAssetItem(item);

            // 使用 CheckValidateUseCase 进行验证
            final result = await checkValidateUseCase(
              CheckValidateParams(entry: wrapper, scene: AfCustomizeViewScene.assetNewCreate),
            );

            if (result.showAlert) {
              LogUtil.d('AssetNewCreateController - 验证失败: ${item.itemName} - ${result.alertMessage}');
              errorItems.add(result);
            }
          }
        }
      }

      // 如果有验证错误，显示第一个错误信息
      if (errorItems.isNotEmpty) {
        LogUtil.d('AssetNewCreateController - 总共发现 ${errorItems.length} 个验证错误');
        CommonDialog.show(content: errorItems.first.alertMessage ?? '入力エラーがあるのでご確認ください', confirmText: 'OK');
        return false;
      }

      LogUtil.d('AssetNewCreateController - CheckValidateUseCase 验证通过');
      return true;
    } catch (e) {
      LogUtil.e('AssetNewCreateController - CheckValidateUseCase 验证时发生错误: $e');

      // 如果专业验证失败，回退到基础验证
      LogUtil.d('AssetNewCreateController - 回退到基础验证');
      return await validateForm();
    }
  }

  /// 显示验证错误信息
  void _showValidationErrors(List<String> errors) {
    final errorMessage = errors.take(5).join('\n');
    CommonDialog.show(content: errorMessage, type: DialogType.error, confirmText: 'OK');
  }

  /// 验证单个字段的格式
  bool _validateFieldFormat(dynamic item) {
    try {
      final value = item.defaultData as String?;
      if (value == null || value.isEmpty) {
        return true;
      }

      final itemType = item.itemType as String?;

      switch (itemType) {
        case 'number':
        case 'currency':
          final numValue = value.replaceAll(',', '');
          return double.tryParse(numValue) != null;

        case 'date':
          return DateTime.tryParse(value) != null;

        case 'email':
          final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
          return emailRegex.hasMatch(value);

        case 'url':
          return Uri.tryParse(value) != null;

        default:
          return true;
      }
    } catch (e) {
      LogUtil.d('AssetNewCreateController - 字段格式验证异常: $e');
      return false;
    }
  }

  /// 收集所有表单数据
  Map<String, dynamic> collectFormData() {
    try {
      final formData = <String, dynamic>{};

      LogUtil.d('AssetNewCreateController - 数据收集：开始收集表单数据');

      for (final sectionName in uiState.assetDict.keys) {
        final sectionItems = uiState.assetDict[sectionName];
        if (sectionItems != null) {
          for (final item in sectionItems) {
            final fieldName = item.itemName;
            final fieldValue = item.defaultData;
            final itemType = item.itemType;

            if (item.sysSetFlg == '1') {
              continue;
            }

            final convertedValue = _convertFieldValue(item, fieldValue, itemType);

            if (convertedValue != null) {
              formData[fieldName] = convertedValue;
            }
          }
        }
      }

      formData['assetTypeId'] = uiState.assetTypeList.isNotEmpty ? uiState.assetTypeList.first.assetTypeId : null;
      formData['createTime'] = DateTime.now().toIso8601String();

      if (assetNewCreateArguments.barcode?.isNotEmpty == true) {
        formData['barcode'] = assetNewCreateArguments.barcode;
      }

      LogUtil.d('AssetNewCreateController - 数据收集完成：${formData.length} 个字段');

      return formData;
    } catch (e) {
      LogUtil.d('AssetNewCreateController - 收集表单数据时发生错误: $e');
      return <String, dynamic>{};
    }
  }

  /// 转换字段值为适当的数据类型
  dynamic _convertFieldValue(dynamic item, dynamic fieldValue, String? itemType) {
    try {
      if (fieldValue == null) {
        return null;
      }

      switch (itemType) {
        case 'input':
        case 'textarea':
          return fieldValue.toString();

        case 'number':
        case 'currency':
          if (fieldValue is String && fieldValue.isNotEmpty) {
            final numStr = fieldValue.replaceAll(',', '');
            return double.tryParse(numStr) ?? fieldValue;
          }
          return fieldValue;

        case 'date':
          if (fieldValue is String && fieldValue.isNotEmpty) {
            final date = DateTime.tryParse(fieldValue);
            return date?.toIso8601String() ?? fieldValue;
          }
          return fieldValue;

        case 'master':
          return _convertMasterFieldValue(item, fieldValue);

        case 'calculate':
          return fieldValue;

        case 'homeImage':
          return fieldValue;

        default:
          return fieldValue;
      }
    } catch (e) {
      LogUtil.d('AssetNewCreateController - 字段值转换异常: $e');
      return fieldValue;
    }
  }

  /// 转换Master字段的值
  dynamic _convertMasterFieldValue(dynamic item, dynamic fieldValue) {
    try {
      if (fieldValue == null || fieldValue.toString().isEmpty) {
        return null;
      }

      final displayList = item.displayList as List?;
      if (displayList != null && displayList.isNotEmpty) {
        final displayData = <String, dynamic>{};

        for (final displayItem in displayList) {
          final itemId = displayItem.itemId?.toString();
          final itemValue = displayItem.itemValue;

          if (itemId != null && itemValue != null) {
            displayData[itemId] = itemValue;
          }
        }

        return {'masterId': fieldValue, 'display': displayData};
      }

      return fieldValue;
    } catch (e) {
      LogUtil.d('AssetNewCreateController - Master字段转换异常: $e');
      return fieldValue;
    }
  }

  /// 判断是否应该显示导航栏
  bool shouldShowNavigationBar() {
    return false;
  }

  /// 清理turl字段
  void _cleanTurlFields(Map<String, dynamic> formData) {
    try {
      for (final key in formData.keys) {
        final value = formData[key];

        if (value is List) {
          for (final subValue in value) {
            if (subValue is Map<String, dynamic> && subValue.containsKey('turl')) {
              subValue['turl'] = '';
            }
          }
        }
      }
    } catch (e) {
      LogUtil.d('AssetNewCreateController - turl字段清理异常: $e');
    }
  }

  /// 配置导航栏可见性
  void configureNavigationBarVisibility() {
    LogUtil.d('AssetNewCreateController - 配置导航栏可见性');
  }
}
