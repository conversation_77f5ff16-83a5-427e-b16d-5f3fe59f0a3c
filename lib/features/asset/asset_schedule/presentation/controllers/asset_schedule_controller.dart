import 'dart:convert';

import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/extensions/nullable_extensions.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/datetime_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/enums/schedule_item_ids.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/data/model/un_permission_response_entity.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/domain/entity/asset_schedule_arugment.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/domain/entity/permission_params_entity.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/domain/usecase/get_master_layout_setting_by_master_type_id_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/domain/usecase/get_tenant_info_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/domain/usecase/get_un_permission_usercase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/domain/usecase/schedule_check_asset_appoint_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/domain/usecase/schedule_save_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/presentation/states/asset_schedule_ui_state.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/tenant_info_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:get/get.dart';

/// 资产日程控制器
class AssetScheduleController extends BaseController {
  /// UI状态
  final AssetScheduleUiState uiState = AssetScheduleUiState();

  /// 导航服务
  final NavigationService navigationService;

  /// 获取权限列表
  final GetUnPermissionUsercase getUnPermissionUsercase;

  /// 获取租户信息
  final GetTenantInfoUseCase getTenantInfoUseCase;

  /// 获取master类型
  final GetMasterLayoutSettingByMasterTypeIdUsecase getMasterLayoutSettingByMasterTypeIdUsecase;

  /// 页面标题
  RxString pageTitle = ''.obs;

  String? userName;

  int? userId;

  /// 所有权限参数
  List<UnPermissionResponseUnPermissionListEntity?>? allUnPermissionParams;

  /// 页面权限参数
  List<UnPermissionResponseUnPermissionListEntity?>? pageUnPermissionParams;

  /// 权限参数
  /// 权限参数实体
  PermissionParamsEntity permissionParams = const PermissionParamsEntity(
    deleteAuthority: null,
    modifyAuthority: null,
    addAuthority: null,
  );

  /// 日程数据
  final Rx<AppointmentListResponse?> scheduleData = Rx<AppointmentListResponse?>(null);

  /// 预约列表
  Appointment? appointment;

  /// 保存成功后的回调函数
  Function(Appointment updatedAppointment)? onSaveSuccess;

  /// 输入限制最大日期
  String? inputLimmitMaxDate;

  /// 布局设置
  ///
  /// key: masterTypeId
  /// value: List<SharedLayoutSetting>
  Map<int, List<SharedLayoutSetting>?> layoutSettingModelMap = {};

  /// 检查Schedule数据
  final ScheduleCheckAssetAppointUseCase checkAssetAppointUsecase;

  /// 保存Schedule
  final ScheduleSaveUsecase scheduleSaveUsecase;

  /// 保存按钮显隐控制
  final RxBool isSaveButtonVisible = false.obs;

  /// 构造函数
  AssetScheduleController({
    required this.navigationService,
    required this.getUnPermissionUsercase,
    required this.getTenantInfoUseCase,
    required this.getMasterLayoutSettingByMasterTypeIdUsecase,
    required this.checkAssetAppointUsecase,
    required this.scheduleSaveUsecase,
  });

  @override
  void onReady() {
    super.onReady();
    _initParams();
  }

  /// 初始化参数
  Future<void> _initParams() async {
    // 在调用 showLoading() 之前先保存原始参数，避免 Loading 对话框影响 Get.arguments
    final originalArguments = Get.arguments;
    LogUtil.d('Schedule Page - Original Arguments: $originalArguments');

    try {
      await showLoading();

      // 确保原始参数是 AssetScheduleArgument 类型
      if (originalArguments is! AssetScheduleArgument) {
        throw Exception('Invalid arguments type: ${originalArguments.runtimeType}');
      }

      // 检查权限
      await checkUnPromiss();

      // 处理日程数据
      await _processScheduleData(originalArguments);

      // 处理布局设置
      await _processLayoutSettings();

      // 初始化保存按钮
      _initSaveButtonVisible();
    } catch (e) {
      LogUtil.e('AssetScheduleController: Error parsing parameters - $e');
    } finally {
      hideLoading();
    }
  }

  /// 处理日程数据
  Future<void> _processScheduleData(AssetScheduleArgument arguments) async {
    final tempScheduleData = arguments.scheduleData;
    LogUtil.d('Schedule Page - Schedule Data: $tempScheduleData');

    final tempAppointmentListItem = arguments.appointment;
    LogUtil.d('Schedule Page - Appointment List: $tempAppointmentListItem');

    final appointmentList = tempScheduleData.appointmentList;
    LogUtil.d('Schedule Page - Appointment List: $appointmentList');

    // 处理预约列表数据
    _processAppointmentList(appointmentList);

    // 处理事件类型列表
    final masterTypeIdSet = _processEventTypeList(tempScheduleData, tempAppointmentListItem);

    // 处理预约公共项目列表
    _processReservationItemCommonList(tempScheduleData, tempAppointmentListItem, masterTypeIdSet);

    // 处理额外预约项目列表
    _processExtraReservationItemList(tempScheduleData, masterTypeIdSet);

    // 设置数据
    scheduleData.value = tempScheduleData;
    appointment = tempAppointmentListItem;
    onSaveSuccess = arguments.onSaveSuccess; // 保存回调函数
    pageTitle.value = tempAppointmentListItem.reservationName ?? '';
    inputLimmitMaxDate = DateTimeUtils.addYearsAndFormat(
      date: DateTime.now(),
      numberOfYears: 10,
      dateFormat: 'yyyy-12-31',
    );

    // 清除缓存的预约列表数据
    _clearCachedReservationData();
  }

  /// 处理预约列表数据
  void _processAppointmentList(List<Appointment?>? appointmentList) {
    appointmentList?.forEach((element) {
      LogUtil.d('Schedule Page - Appointment List: ${element?.reservationName}');
      element?.extraCommonTextObj = jsonDecode(element.extraCommonText ?? '{}');
      element?.reservationTextObj = jsonDecode(element.reservationText ?? '{}');
    });
  }

  /// 处理事件类型列表
  Set<int> _processEventTypeList(AppointmentListResponse tempScheduleData, Appointment tempAppointmentListItem) {
    final masterTypeIdSet = <int>{};

    tempScheduleData.eventTypeList?.forEach((element) {
      element?.itemList?.forEach((item) {
        item?.itemValObj = jsonDecode(item.itemVal ?? '{}');

        // 判断是否是master类型
        if (item?.itemType == SharedItemTypeEnum.master.value) {
          final itemValMap = item?.itemValObj as Map<String, dynamic>?;
          masterTypeIdSet.add(itemValMap?['masterTypeId'] ?? 0);
        }

        // 判断是否为日付类型
        if (item?.itemType == SharedItemTypeEnum.date.value) {
          _processDateTypeItem(item, tempAppointmentListItem);
        }
      });
    });

    return masterTypeIdSet;
  }

  /// 处理日期类型项目
  void _processDateTypeItem(EventTypeItem? item, Appointment tempAppointmentListItem) {
    final itemId = item?.itemId;
    final reservationTextobj = tempAppointmentListItem.reservationTextObj;
    final reservationTextMap = reservationTextobj as Map<dynamic, dynamic>?;
    final val = reservationTextMap?[itemId.toString()];
    final itemValObj = item?.itemValObj as Map<dynamic, dynamic>?;
    final formatTime = itemValObj?['dateType'] == 'date' ? 'date' : 'dateTime';
    item?.defaultData = (val?.isEmpty ?? true) ? '' : val.replaceAll('-', '/');
    item?.option = jsonEncode({'dateType': formatTime});
    LogUtil.d('Schedule Page - Item Val: $val');
  }

  /// 处理预约公共项目列表
  void _processReservationItemCommonList(
    AppointmentListResponse tempScheduleData,
    Appointment tempAppointmentListItem,
    Set<int> masterTypeIdSet,
  ) {
    tempScheduleData.reservationItemCommonList?.forEach((element) {
      element?.itemValObj = jsonDecode(element.itemVal ?? '{}');

      // 名称
      if (ScheduleItemIds.name.equals(element?.itemId?.toString() ?? '')) {
        element?.defaultData = tempAppointmentListItem.reservationName;
        return;
      }

      // 开始日时
      if (ScheduleItemIds.startDate.equals(element?.itemId?.toString() ?? '')) {
        element?.defaultData = tempAppointmentListItem.start;
        return;
      }

      // 终了日时
      if (ScheduleItemIds.endDate.equals(element?.itemId?.toString() ?? '')) {
        element?.defaultData = tempAppointmentListItem.end;
        return;
      }

      // 终日
      if (ScheduleItemIds.allDay.equals(element?.itemId?.toString() ?? '')) {
        element?.defaultData = tempAppointmentListItem.unitDay;
        return;
      }

      // アラート設定
      if (ScheduleItemIds.alertSetting.equals(element?.itemId?.toString() ?? '')) {
        element?.defaultData = tempAppointmentListItem.alertSetting;
        return;
      }

      // 判断是否是master类型
      if (element?.itemType == SharedItemTypeEnum.master.value) {
        final itemValMap = element?.itemValObj as Map<String, dynamic>?;
        masterTypeIdSet.add(itemValMap?['masterTypeId'] ?? 0);
      }

      // 判断是否为日付类型
      if (element?.itemType == SharedItemTypeEnum.date.value) {
        _processDateTypeReservationItem(element, tempAppointmentListItem);
      }
    });
  }

  /// 处理日期类型预约项目
  void _processDateTypeReservationItem(ReservationItemCommon? element, Appointment tempAppointmentListItem) {
    final itemId = element?.itemId;
    final reservationTextobj = tempAppointmentListItem.reservationTextObj;
    final reservationTextMap = reservationTextobj as Map<dynamic, dynamic>?;
    final val = reservationTextMap?[itemId.toString()];
    final itemValObj = element?.itemValObj as Map<dynamic, dynamic>?;
    final formatTime = itemValObj?['dateType'] == 'date' ? 'date' : 'dateTime';
    element?.defaultData = (val?.isEmpty ?? true) ? '' : val.split('-').join('/');
    element?.option = jsonEncode({'dateType': formatTime});
  }

  /// 处理额外预约项目列表
  void _processExtraReservationItemList(AppointmentListResponse tempScheduleData, Set<int> masterTypeIdSet) {
    tempScheduleData.extraReservationItemCommonList?.forEach((element) {
      // itemId === 6 イベントタイプ eventTypeList isRequired（默认一定必須入力）
      if (ScheduleItemIds.eventType.equals(element?.itemId?.toString() ?? '')) {
        element?.isRequired = '1';
      }

      element?.itemValObj = jsonDecode(element.itemVal ?? '{}');

      if (element?.itemType == SharedItemTypeEnum.master.value) {
        final itemValMap = element?.itemValObj as Map<String, dynamic>?;
        masterTypeIdSet.add(itemValMap?['masterTypeId'] ?? 0);
      }
    });

    LogUtil.d('Schedule Page - Master Type Id Set: $masterTypeIdSet');
  }

  /// 处理布局设置
  Future<void> _processLayoutSettings() async {
    final masterTypeIdSet = _collectAllMasterTypeIds();
    if (masterTypeIdSet.isNotEmpty) {
      await secureLayoutSetting(masterTypeIdSet);
    }
  }

  /// 收集所有master类型ID
  Set<int> _collectAllMasterTypeIds() {
    final masterTypeIdSet = <int>{};

    // 从事件类型列表收集
    scheduleData.value?.eventTypeList?.forEach((element) {
      element?.itemList?.forEach((item) {
        if (item?.itemType == SharedItemTypeEnum.master.value) {
          final itemValMap = item?.itemValObj as Map<String, dynamic>?;
          masterTypeIdSet.add(itemValMap?['masterTypeId'] ?? 0);
        }
      });
    });

    // 从预约公共项目列表收集
    scheduleData.value?.reservationItemCommonList?.forEach((element) {
      if (element?.itemType == SharedItemTypeEnum.master.value) {
        final itemValMap = element?.itemValObj as Map<String, dynamic>?;
        masterTypeIdSet.add(itemValMap?['masterTypeId'] ?? 0);
      }
    });

    // 从额外预约项目列表收集
    scheduleData.value?.extraReservationItemCommonList?.forEach((element) {
      if (element?.itemType == SharedItemTypeEnum.master.value) {
        final itemValMap = element?.itemValObj as Map<String, dynamic>?;
        masterTypeIdSet.add(itemValMap?['masterTypeId'] ?? 0);
      }
    });

    return masterTypeIdSet;
  }

  Future secureLayoutSetting(Set<int> masterTypeIdSet) async {
    if (masterTypeIdSet.isEmpty) {
      return;
    }
    layoutSettingModelMap = await getMasterLayoutSettingByMasterTypeIdUsecase(masterTypeIdSet);
    LogUtil.d('Schedule Page - Master Layout Setting: $layoutSettingModelMap');
  }

  /// 获取权限信息
  Future<dynamic> _fetchUnPermission() async {
    try {
      // 缓存无效，执行网络请求
      final result = await getUnPermissionUsercase(const NoParams());

      return result;
    } catch (e) {
      LogUtil.e('Error fetching unPermission: $e');
      // 出错时返回null，让调用者处理
      return null;
    }
  }

  /// 获取租户信息
  Future<dynamic> _fetchTenantInfo() async {
    try {
      // 缓存无效，执行网络请求
      final result = await getTenantInfoUseCase(const NoParams());
      return result;
    } catch (e) {
      LogUtil.e('Error fetching tenantInfo: $e');
      // 出错时返回null，让调用者处理
      return null;
    }
  }

  /// 返回上一页
  void goBack() {
    navigationService.goBack();
  }

  /// 检查权限
  Future<void> checkUnPromiss() async {
    try {
      // 显示加载指示器
      // 获取用户信息
      userName = StorageUtils.get<String>(StorageUtils.keyUserName);
      userId = StorageUtils.get<int>(StorageUtils.keyUserId);

      // 并行执行网络请求
      final futures = await Future.wait([_fetchUnPermission(), _fetchTenantInfo()]);

      // 处理结果
      final unPermissionResult = futures[0] as GetUnPermissionResult;
      final tenantInfoResult = futures[1] as TenantInfoResponse;

      // 设置管理员状态
      final managerId = tenantInfoResult.tenant?.managerMail;
      uiState.isManager.value = managerId == userName;
      LogUtil.d('managerId: $managerId, userName: $userName, isManager: ${uiState.isManager.value}');

      // 处理权限数据
      _processPermissions(unPermissionResult.responseEntity?.unPermissionList);
    } catch (e) {
      LogUtil.e('Error in checkUnPromiss: $e');
    }
  }

  /// 处理权限数据
  void _processPermissions(List<UnPermissionResponseUnPermissionListEntity?>? permissions) {
    allUnPermissionParams = permissions;

    // 检查权限列表是否为空
    if (permissions == null || permissions.isEmpty) {
      LogUtil.d('INFO:appointment-list-bryntum権限データがないため、権限チェックが実施しない。');
      return;
    }

    // 过滤出当前页面的权限
    pageUnPermissionParams = permissions.where((element) => element?.pageId == 'asset-list').toList();
    if (pageUnPermissionParams == null || pageUnPermissionParams!.isEmpty) {
      LogUtil.d('INFO:appointment-list-bryntum権限データがないため、権限チェックが実施しない。');
      return;
    }

    // 使用Map优化权限处理
    _updatePermissions();
  }

  /// 更新权限参数
  void _updatePermissions() {
    // 处理添加权限
    final addAuthorityPermissions = pageUnPermissionParams?.where((element) => element?.resourceId == 2).toList();
    if ((addAuthorityPermissions?.length ?? 0) > 0) {
      permissionParams = permissionParams.copyWith(addAuthority: addAuthorityPermissions?.first);
    }

    // 处理修改权限
    final modifyAuthority = pageUnPermissionParams?.where((element) => element?.resourceId == 3).toList();
    if ((modifyAuthority?.length ?? 0) > 0) {
      permissionParams = permissionParams.copyWith(modifyAuthority: modifyAuthority?.first);
    }

    // 处理删除权限
    final deleteAuthority = pageUnPermissionParams?.where((element) => element?.resourceId == 4).toList();
    if ((deleteAuthority?.length ?? 0) > 0) {
      permissionParams = permissionParams.copyWith(deleteAuthority: deleteAuthority?.first);
    }
  }

  /// 获取是否显示日程类型
  bool getIsScheduleTypeVisible(ReservationItemCommon? element) {
    if (element == null) return false;
    if (element.itemId != 6) return false;
    return true;
  }

  /// 获取预约项目
  List<ReservationItemCommon> getReservationItems() {
    final reservationItemCommonList = scheduleData.value?.reservationItemCommonList;
    if (reservationItemCommonList == null) return [];

    final validItems = <ReservationItemCommon>[];
    for (var element in reservationItemCommonList) {
      if (element == null) continue;
      validItems.add(element);
    }
    return validItems;
  }

  /// 获取额外预约项目
  List<ReservationItemCommon> getExtraReservationItems() {
    return _filterReservationItems(scheduleData.value?.extraReservationItemCommonList);
  }

  /// 过滤预约项目
  List<ReservationItemCommon> _filterReservationItems(List<ReservationItemCommon?>? items) {
    if (items == null) return [];

    final validItems = <ReservationItemCommon>[];
    for (var element in items) {
      if (element == null) continue;
      validItems.add(element);
    }
    return validItems;
  }

  /// 保存
  void save() async {
    // 在显示确认对话框之前先进行校验
    if (!await _validateScheduleData()) {
      return;
    }

    CommonDialog.show(
      content: '保存しますか？',
      type: DialogType.info,
      cancelText: 'いいえ',
      confirmText: 'はい',
      onConfirm: () async {
        _doSaveSchedule();
      },
    );
  }

  /// 校验Schedule数据
  ///
  /// 参考asset_detail_controller.dart中的校验方式
  /// 获取自定义视图控制器并调用validateAndShowErrors方法
  ///
  /// 返回值:
  /// * [Future<bool>] - 如果校验通过返回 true，否则返回 false
  Future<bool> _validateScheduleData() async {
    try {
      // 获取自定义视图控制器，使用空字符串作为tag（与AssetSchedulePage中的instance一致）
      final afCustomizeController = Get.find<AfCustomizeViewController>(tag: '');

      // 校验所有项目
      return await afCustomizeController.validateAndShowErrors();
    } catch (e) {
      LogUtil.e('校验Schedule数据时发生错误: $e');
      // 如果无法获取控制器或校验过程中出错，默认返回true继续保存流程
      return true;
    }
  }

  /// 执行保存
  Future<void> _doSaveSchedule() async {
    try {
      // 检查appointment是否为null
      if (appointment == null) {
        LogUtil.w('appointment为null，无法保存日程数据');
        return;
      }

      await appointment!.let((it) async {
        LogUtil.d('Save - appointment eventTypeId: ${it.eventTypeId}');
        LogUtil.d('Save - appointment eventTypeName: ${it.eventTypeName}');

        final scheduleCheckAssetAppointParams = ScheduleCheckAssetAppointParams(
          assetId: it.assetId ?? 0,
          reservationNo: it.reservationNo?.toString() ?? '',
          eventTypeId: it.eventTypeId ?? 0,
          start: it.start?.toString() ?? '',
          end: it.end?.toString() ?? '',
        );
        final isVerified = await checkAssetAppointUsecase(scheduleCheckAssetAppointParams);
        if (isVerified) {
          showLoading();
          await scheduleSaveUsecase(ScheduleSaveParams(appointment: it));
          hideLoading();

          // 保存成功后，调用回调函数通知外部页面更新数据
          if (onSaveSuccess != null && appointment != null) {
            await onSaveSuccess!(appointment!);
          }

          // 确保在导航之前先关闭Loading对话框
          hideLoading();

          // 等待一个微任务，确保Loading对话框完全关闭
          await Future.delayed(Duration.zero);

          LogUtil.d('准备执行导航返回操作');
          navigationService.goBack();
          LogUtil.d('导航返回操作已执行');
        } else {
          LogUtil.w('验证失败，无法保存日程数据');
        }
      });
    } catch (e) {
      LogUtil.e('Error saving schedule: $e');
      // 发生异常时确保关闭Loading对话框
      hideLoading();
      if (e is SystemException) {
        CommonDialog.show(content: e.message);
      }
    }
  }

  /// 缓存的预约列表数据
  List<ReservationItemCommon>? _cachedReservationListData;

  /// 获取预约列表数据（合并所有预约项目）
  List<ReservationItemCommon> getReservationListData() {
    if (_cachedReservationListData != null) {
      return _cachedReservationListData!;
    }

    final items = getReservationItems();
    final extraItems = getExtraReservationItems();
    _prepareReservationItems(extraItems);
    final list = [...items, ...extraItems];
    _cachedReservationListData = list;
    return list;
  }

  /// 检查是否有预约数据
  bool hasReservationData() {
    final items = getReservationItems();
    final extraItems = getExtraReservationItems();
    return items.isNotEmpty || extraItems.isNotEmpty;
  }

  /// 清除缓存的预约列表数据
  void _clearCachedReservationData() {
    _cachedReservationListData = null;
  }

  /// 准备预约项目
  void _prepareReservationItems(List<ReservationItemCommon> items) {
    LogUtil.d('_prepareReservationItems - appointment eventTypeId before: ${appointment?.eventTypeId}');

    final extraCommonTextObj = appointment?.extraCommonTextObj as Map<String, dynamic>?;
    items.forEach((element) {
      if (element.itemId != ScheduleItemIds.eventType.value) {
        element.defaultData = extraCommonTextObj?[element.itemId?.toString() ?? ''] ?? '';
        LogUtil.d('Schedule Page - Default Data:${element.itemId} ${element.defaultData}');
      } else {
        LogUtil.d('Schedule Page - Skipping eventType item (itemId: ${element.itemId})');
      }
    });
    if (extraCommonTextObj != null) {
      appointment?.extraCommonTextObj = extraCommonTextObj;
    }

    LogUtil.d('_prepareReservationItems - appointment eventTypeId after: ${appointment?.eventTypeId}');
  }

  /// 初始化保存按钮的可见性
  void _initSaveButtonVisible() {
    // 初始化是否可以展示保存按钮
    if (appointment == null) {
      LogUtil.d('_initSaveButtonVisible: appointment is null, setting isSaveButtonVisible to false');
      isSaveButtonVisible.value = false;
    } else {
      final userId = StorageUtils.get<int>(StorageUtils.keyUserId);
      final isCreatedByUser = appointment?.createdById != null && appointment?.createdById == userId?.toString();
      final isManager = uiState.isManager.value;
      final hasModifyAuthority = permissionParams.modifyAuthority == null;

      LogUtil.d('_initSaveButtonVisible: userId=$userId, createdById=${appointment?.createdById}');
      LogUtil.d(
        '_initSaveButtonVisible: isCreatedByUser=$isCreatedByUser, isManager=$isManager, hasModifyAuthority=$hasModifyAuthority',
      );

      if ((isCreatedByUser || isManager) && hasModifyAuthority) {
        LogUtil.d('_initSaveButtonVisible: setting isSaveButtonVisible to true');
        isSaveButtonVisible.value = true;
      } else {
        LogUtil.d('_initSaveButtonVisible: setting isSaveButtonVisible to false');
        isSaveButtonVisible.value = false;
      }
    }

    LogUtil.d('_initSaveButtonVisible: final isSaveButtonVisible.value = ${isSaveButtonVisible.value}');
  }
}
