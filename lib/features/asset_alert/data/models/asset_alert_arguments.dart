import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';

/// 资产警报页面参数模型
class AssetAlertArguments {
  /// 预约事件列表
  final AppointmentListResponse? scheduleEventList;

  /// 预约信息
  final Appointment? appointment;

  /// 保存按钮是否可见
  final bool? isSaveButtonVisible;

  const AssetAlertArguments({this.scheduleEventList, this.appointment, this.isSaveButtonVisible});

  /// 从Map创建参数对象
  factory AssetAlertArguments.fromMap(Map<String, dynamic> map) {
    return AssetAlertArguments(
      scheduleEventList: map['schedule'] as AppointmentListResponse?,
      appointment: map['appointment'] as Appointment?,
      isSaveButtonVisible: map['isSaveButtonVisible'] as bool?,
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {'schedule': scheduleEventList, 'appointment': appointment, 'isSaveButtonVisible': isSaveButtonVisible};
  }

  /// 验证参数是否有效
  bool get isValid {
    return appointment != null;
  }

  /// 获取预约项目通用列表
  List<ReservationItemCommon?>? get reservationItemCommonList {
    return scheduleEventList?.reservationItemCommonList;
  }

  /// 获取事件类型列表
  List<EventType?>? get eventTypeList {
    return scheduleEventList?.eventTypeList;
  }

  /// 获取预约列表
  List<Appointment?>? get appointmentList {
    return scheduleEventList?.appointmentList;
  }

  @override
  String toString() {
    return 'AssetAlertArguments(scheduleEventList: $scheduleEventList, appointment: $appointment, isSaveButtonVisible: $isSaveButtonVisible)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AssetAlertArguments &&
        other.scheduleEventList == scheduleEventList &&
        other.appointment == appointment &&
        other.isSaveButtonVisible == isSaveButtonVisible;
  }

  @override
  int get hashCode {
    return scheduleEventList.hashCode ^ appointment.hashCode ^ isSaveButtonVisible.hashCode;
  }
}
