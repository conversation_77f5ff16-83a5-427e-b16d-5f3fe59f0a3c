import 'dart:convert';

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset_alert/data/models/asset_alert_arguments.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_role_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/domain/models/user_group_selector_params.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/domain/usecase/get_user_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/user_group_selector/presentation/constants/user_group_selector_constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AssetAlertController extends BaseController {
  final RxBool emailEvent = false.obs;
  final RxBool pushEvent = false.obs;

  /// 是否启用
  final RxBool enable = false.obs;

  /// 预警时间
  final RxString alertTimeType = ''.obs;

  /// 预警时间前
  final RxString alertTimeBefore = ''.obs;

  /// 预警时间前数值
  final RxString alertTimeValue = ''.obs;

  /// 繰り返し选择（アラート時刻用）
  final RxString repeatType = '1'.obs;

  /// 繰返し通知状态
  final RxBool returnNotice = false.obs;

  /// 繰返し通知的重复类型选择
  final RxString returnNoticeRepeatType = '1'.obs;

  final RxList<String> timeIds = <String>[].obs;

  /// 时间选项列表
  final RxList<String> timeOptions = <String>[].obs;

  // 临时数据存储变量，用于保存用户修改但未保存的数据
  /// 临时通知类型数据
  final RxString _tempNoticeType = ''.obs;

  /// 临时件名数据
  final RxString _tempNoticeTitle = ''.obs;

  /// 临时文本内容数据
  final RxString _tempNoticeMessage = ''.obs;

  /// 临时 alertTimeDynamic 数据
  final RxString _tempAlertTimeDynamic = ''.obs;

  /// 临时繰返し通知JSON数据
  final RxString _tempReturnNoticeData = ''.obs;

  /// 时间数值文本框控制器
  final TextEditingController timeValueController = TextEditingController();

  /// 时间数值文本框焦点节点
  final FocusNode timeValueFocusNode = FocusNode();

  /// 时间数值文本框是否获取焦点
  final RxBool isTimeValueFocused = false.obs;

  /// 繰返し通知间隔数值文本框控制器
  final TextEditingController repeatIntervalController = TextEditingController();

  /// 繰返し通知间隔数值文本框焦点节点
  final FocusNode repeatIntervalFocusNode = FocusNode();

  /// 繰返し通知间隔数值文本框是否获取焦点
  final RxBool isRepeatIntervalFocused = false.obs;

  /// 繰返し通知次数文本框控制器
  final TextEditingController repeatCountController = TextEditingController();

  /// 繰返し通知次数文本框焦点节点
  final FocusNode repeatCountFocusNode = FocusNode();

  /// 繰返し通知次数文本框是否获取焦点
  final RxBool isRepeatCountFocused = false.obs;

  /// 件名文本框控制器
  final TextEditingController nameController = TextEditingController();

  /// 件名文本框焦点节点
  final FocusNode nameFocusNode = FocusNode();

  /// 件名文本框是否获取焦点
  final RxBool isNameFocused = false.obs;

  /// 文本内容文本框控制器
  final TextEditingController textController = TextEditingController();

  /// 文本内容文本框焦点节点
  final FocusNode textFocusNode = FocusNode();

  /// 文本内容文本框是否获取焦点
  final RxBool isTextFocused = false.obs;

  /// 页面参数
  late AssetAlertArguments arguments;

  final NavigationService navigationService;
  final GetUserListUseCase getUserListUseCase;
  final GetRoleListUseCase getRoleListUseCase;

  /// 用户列表
  final RxList<SharedUserModel> userList = <SharedUserModel>[].obs;

  /// 角色列表
  final RxList<SharedRoleModel> roleList = <SharedRoleModel>[].obs;

  /// 通知用户
  final RxString noticeUserIds = ''.obs;

  /// 通知组
  final RxString noticeGroupIds = ''.obs;

  /// 保存按钮显隐控制
  final RxBool isSaveButtonVisible = false.obs;

  AssetAlertController(this.navigationService, this.getUserListUseCase, this.getRoleListUseCase);

  @override
  void onInit() {
    super.onInit();
    _initParams();

    // 监听焦点变化
    _initListener();

    // 添加监听
    _addListener();
  }

  /// 添加监听
  void _addListener() {
    // 监听件名文本变化
    nameController.addListener(_onNameTextChanged);
    // 监听文本内容变化
    textController.addListener(_onTextContentChanged);
    // 监听时间数值文本变化
    timeValueController.addListener(_onTimeValueTextChanged);
    // 监听繰返し通知间隔文本变化
    repeatIntervalController.addListener(_onRepeatIntervalTextChanged);
    // 监听繰返し通知次数文本变化
    repeatCountController.addListener(_onRepeatCountTextChanged);
  }

  /// 监听焦点变化
  void _initListener() {
    _setupFocusListener(timeValueFocusNode, isTimeValueFocused);
    _setupFocusListener(nameFocusNode, isNameFocused);
    _setupFocusListener(textFocusNode, isTextFocused);
    _setupFocusListener(repeatIntervalFocusNode, isRepeatIntervalFocused);
    _setupFocusListener(repeatCountFocusNode, isRepeatCountFocused);
  }

  /// 通用焦点变化处理方法
  void _setupFocusListener(FocusNode focusNode, RxBool focusState) {
    focusNode.addListener(() {
      focusState.value = focusNode.hasFocus;
    });
  }

  /// 件名文本变化处理
  void _onNameTextChanged() {
    _tempNoticeTitle.value = nameController.text;
  }

  /// 文本内容变化处理
  void _onTextContentChanged() {
    _tempNoticeMessage.value = textController.text;
  }

  /// 时间数值文本变化处理
  void _onTimeValueTextChanged() {
    _updateAlertTimeDynamic(timeValueController.text);
  }

  /// 繰返し通知间隔文本变化处理
  void _onRepeatIntervalTextChanged() {
    _updateReturnNoticeData();
  }

  /// 繰返し通知次数文本变化处理
  void _onRepeatCountTextChanged() {
    _updateReturnNoticeData();
  }

  /// 获取通知消息
  String getNoticeMessage() {
    return arguments.appointment?.alertData?['noticeMessage']?.toString() ?? '';
  }

  /// 解析 alertTimeDynamic JSON 字符串，获取 methodValue 并设置 itemId 和 method 到对应的下拉列表
  String _parseAlertTimeDynamicMethodValue(String? alertTimeDynamicJson) {
    if (alertTimeDynamicJson == null || alertTimeDynamicJson.isEmpty) {
      return '';
    }

    try {
      final Map<String, dynamic> alertTimeDynamic = json.decode(alertTimeDynamicJson);

      // 解析 itemId 并设置到 alertTimeType
      final itemId = alertTimeDynamic['itemId']?.toString() ?? '';
      if (itemId.isNotEmpty) {
        alertTimeType.value = itemId;
      }

      // 解析 method 并设置到 repeatType
      final method = alertTimeDynamic['method']?.toString() ?? '';
      if (method.isNotEmpty) {
        repeatType.value = method;
      }

      // 返回 methodValue
      return alertTimeDynamic['methodValue']?.toString() ?? '';
    } catch (e) {
      LogUtil.e('Error parsing alertTimeDynamic JSON: $e');
      return '';
    }
  }

  /// 构建 alertTimeDynamic JSON 字符串
  String _buildAlertTimeDynamicJson(String methodValue) {
    // 使用当前下拉列表的值构建 JSON
    final alertTimeDynamic = <String, dynamic>{
      'itemId': alertTimeType.value.isNotEmpty ? alertTimeType.value : 'common_2',
      'method': repeatType.value.isNotEmpty ? repeatType.value : '1',
      'methodValue': methodValue,
    };

    try {
      return json.encode(alertTimeDynamic);
    } catch (e) {
      LogUtil.e('Error encoding alertTimeDynamic JSON: $e');
      return '{"itemId": "${alertTimeType.value.isNotEmpty ? alertTimeType.value : 'common_2'}", "method": "${repeatType.value.isNotEmpty ? repeatType.value : '1'}", "methodValue": "$methodValue"}';
    }
  }

  /// 更新 alertTimeDynamic 临时数据
  void _updateAlertTimeDynamic(String methodValue) {
    _tempAlertTimeDynamic.value = _buildAlertTimeDynamicJson(methodValue);
  }

  /// 解析繰返し通知JSON数据
  void _parseReturnNoticeData(String? returnNoticeJson) {
    if (returnNoticeJson == null || returnNoticeJson.isEmpty) {
      return;
    }

    try {
      final Map<String, dynamic> returnNoticeData = json.decode(returnNoticeJson);

      // 解析 method 并设置到 returnNoticeRepeatType
      final method = returnNoticeData['method']?.toString() ?? '1';
      returnNoticeRepeatType.value = method;

      // 解析 cycleValue 并设置到间隔文本框
      final cycleValue = returnNoticeData['cycleValue']?.toString() ?? '';
      repeatIntervalController.text = cycleValue;

      // 解析 methodValue 并设置到次数文本框
      final methodValue = returnNoticeData['methodValue']?.toString() ?? '';
      repeatCountController.text = methodValue;
    } catch (e) {
      LogUtil.e('Error parsing return notice JSON: $e');
    }
  }

  /// 构建繰返し通知JSON字符串
  String _buildReturnNoticeJson() {
    final returnNoticeData = <String, dynamic>{
      'method': returnNoticeRepeatType.value,
      'cycleValue': repeatIntervalController.text,
      'methodValue': repeatCountController.text,
    };

    try {
      return json.encode(returnNoticeData);
    } catch (e) {
      LogUtil.e('Error encoding return notice JSON: $e');
      return '{"method": "${returnNoticeRepeatType.value}", "cycleValue": "${repeatIntervalController.text}", "methodValue": "${repeatCountController.text}"}';
    }
  }

  /// 更新繰返し通知临时数据
  void _updateReturnNoticeData() {
    _tempReturnNoticeData.value = _buildReturnNoticeJson();
  }

  @override
  void onReady() {
    super.onReady();
    _prepareData();
  }

  @override
  void onClose() {
    // 释放资源
    timeValueController.removeListener(_onTimeValueTextChanged);
    timeValueController.dispose();
    timeValueFocusNode.dispose();

    nameController.removeListener(_onNameTextChanged);
    nameController.dispose();
    nameFocusNode.dispose();

    textController.removeListener(_onTextContentChanged);
    textController.dispose();
    textFocusNode.dispose();

    repeatIntervalController.removeListener(_onRepeatIntervalTextChanged);
    repeatIntervalController.dispose();
    repeatIntervalFocusNode.dispose();

    repeatCountController.removeListener(_onRepeatCountTextChanged);
    repeatCountController.dispose();
    repeatCountFocusNode.dispose();

    super.onClose();
  }

  /// 返回
  void goBack() {
    Get.back();
  }

  /// 保存
  void save() {
    // 确保alertData存在
    if (arguments.appointment?.alertData == null) {
      arguments.appointment?.alertData = <String, dynamic>{};
    }

    final alertData = arguments.appointment!.alertData!;

    // 保存通知类型
    alertData['noticeType'] = _tempNoticeType.value;

    // 保存通知先
    alertData['noticeUserIds'] = noticeUserIds.value;
    alertData['noticeGroupIds'] = noticeGroupIds.value;

    // 保存件名
    alertData['noticeTitle'] = _tempNoticeTitle.value;

    // 保存文本内容
    alertData['noticeMessage'] = _tempNoticeMessage.value;

    // 保存 alertTimeDynamic 数据
    if (_tempAlertTimeDynamic.value.isNotEmpty) {
      alertData['alertTimeDynamic'] = _tempAlertTimeDynamic.value;
    }

    // 保存其他相关数据
    alertData['effectiveFlg'] = enable.value ? '1' : '0';

    // 保存繰返し通知状态
    alertData['noticeCycleActiveFlg'] = returnNotice.value ? '1' : '0';

    // 保存繰返し通知JSON数据
    if (returnNotice.value && _tempReturnNoticeData.value.isNotEmpty) {
      alertData['returnNoticeData'] = _tempReturnNoticeData.value;
    }

    // 保存完成后关闭页面
    Get.back();
  }

  /// 启用状态切换
  void enableChange(bool? value) {
    enable.value = value ?? false;
  }

  /// 邮件通知状态切换
  void emailEventChange(bool? value) {
    emailEvent.value = value ?? false;
    _changeNotifyState('mail');
  }

  /// 推送通知状态切换
  void pushEventChange(bool? value) {
    pushEvent.value = value ?? false;
    _changeNotifyState('push');
  }

  /// 通知状态变更处理（参考ionic逻辑）
  void _changeNotifyState(String type) {
    // 更新临时通知类型数据，不直接修改alertData
    final currentNoticeType = _tempNoticeType.value;

    if (currentNoticeType.isNotEmpty) {
      final split = currentNoticeType.split(',');

      if (split.contains(type)) {
        // 如果包含该类型，则移除
        split.remove(type);
        _tempNoticeType.value = split.join(',');
      } else {
        // 如果不包含该类型，则添加
        split.add(type);
        _tempNoticeType.value = split.join(',');
      }
    } else {
      // 如果noticeType不存在，直接设置
      _tempNoticeType.value = type;
    }
  }

  /// 繰返し通知状态切换
  void returnNoticeChange(bool? value) {
    returnNotice.value = value ?? false;
  }

  /// 重复类型变更处理（アラート時刻用）
  void onRepeatTypeChanged(String? value) {
    if (value != null) {
      repeatType.value = value;
      // 更新 alertTimeDynamic JSON 数据
      _updateAlertTimeDynamic(timeValueController.text);
    }
  }

  /// 繰返し通知重复类型变更处理
  void onReturnNoticeRepeatTypeChanged(String? value) {
    if (value != null) {
      returnNoticeRepeatType.value = value;
      // 更新繰返し通知JSON数据
      _updateReturnNoticeData();
    }
  }

  /// 根据repeatType获取对应的文本
  String get repeatTypeText {
    switch (repeatType.value) {
      case '1':
        return '時間前';
      case '2':
        return '日前';
      case '3':
        return '週間前';
      case '4':
        return 'カ月前';
      default:
        return '時間前';
    }
  }

  /// 时间类型变更处理
  void onAlertTimeTypeChanged(String? value) {
    if (value != null && timeIds.contains(value)) {
      alertTimeType.value = value;
      // 更新 alertTimeDynamic JSON 数据
      _updateAlertTimeDynamic(timeValueController.text);
    }
  }

  /// 时间数值确认按钮点击处理
  void onTimeValueConfirm() {
    timeValueFocusNode.unfocus();
  }

  /// 件名确认按钮点击处理
  void onNameConfirm() {
    nameFocusNode.unfocus();
  }

  /// 文本确认按钮点击处理
  void onTextConfirm() {
    textFocusNode.unfocus();
  }

  /// 繰返し通知间隔确认按钮点击处理
  void onRepeatIntervalConfirm() {
    repeatIntervalFocusNode.unfocus();
  }

  /// 繰返し通知次数确认按钮点击处理
  void onRepeatCountConfirm() {
    repeatCountFocusNode.unfocus();
  }

  /// 通知先点击处理
  Future<void> onNoticeToTap() async {
    final result = await navigationService.navigateTo(
      AutoRoutes.userGroupSelector,
      arguments: UserGroupSelectorParams(
        title: '通知先',
        fromPage: 'assetAlert',
        selectionMode: UserGroupSelectorConstants.selectionModeMultiple,
        displayType: UserGroupSelectorConstants.displayTypeBoth,
        // 传递当前选中的用户和组ID
        initialSelectedUserIds: _parseSelectedUserIds(),
        initialSelectedGroupIds: _parseSelectedGroupIds(),
      ),
    );

    // 处理选择结果
    LogUtil.d('[AssetAlert] 通知先选择结果原始数据: $result');
    LogUtil.d('[AssetAlert] 结果数据类型: ${result.runtimeType}');

    if (result != null && result is Map<String, dynamic>) {
      LogUtil.d('[AssetAlert] 开始处理通知先选择结果');
      _handleNoticeSelectionResult(result);
    } else if (result != null) {
      LogUtil.w('[AssetAlert] 收到非预期的结果类型: ${result.runtimeType}, 数据: $result');
    } else {
      LogUtil.d('[AssetAlert] 用户取消选择或未选择任何项目');
    }
  }

  /// 解析当前选中的用户ID列表
  List<int> _parseSelectedUserIds() {
    if (noticeUserIds.value.isEmpty) return [];

    return noticeUserIds.value
        .split(',')
        .where((id) => id.trim().isNotEmpty)
        .map((id) => int.tryParse(id.trim()))
        .whereType<int>()
        .toList();
  }

  /// 解析当前选中的组ID列表
  List<int> _parseSelectedGroupIds() {
    if (noticeGroupIds.value.isEmpty) return [];

    return noticeGroupIds.value
        .split(',')
        .where((id) => id.trim().isNotEmpty)
        .map((id) => int.tryParse(id.trim()))
        .whereType<int>()
        .toList();
  }

  /// 处理通知先选择结果
  void _handleNoticeSelectionResult(Map<String, dynamic> result) {
    LogUtil.d('[AssetAlert] 处理通知先选择结果: $result');

    // 处理选中的用户 - 使用正确的JSON键名
    final usersJson = result[UserGroupSelectorConstants.jsonKeyUsers] as List?;
    if (usersJson != null) {
      final selectedUsers = usersJson
          .map((userJson) => SharedUserModel.fromJson(userJson as Map<String, dynamic>))
          .toList();
      final userIds = selectedUsers.where((user) => user.userId != null).map((user) => user.userId.toString()).toList();
      noticeUserIds.value = userIds.join(',');
      LogUtil.d('[AssetAlert] 更新选中用户: ${userIds.length}个用户');
    } else {
      // 如果没有用户数据，清空用户ID
      noticeUserIds.value = '';
      LogUtil.d('[AssetAlert] 清空用户选择');
    }

    // 处理选中的组 - 使用正确的JSON键名
    final groupsJson = result[UserGroupSelectorConstants.jsonKeyGroups] as List?;
    if (groupsJson != null) {
      final selectedGroups = groupsJson
          .map((groupJson) => SharedRoleModel.fromJson(groupJson as Map<String, dynamic>))
          .toList();
      final groupIds = selectedGroups
          .where((group) => group.roleId != null)
          .map((group) => group.roleId.toString())
          .toList();
      noticeGroupIds.value = groupIds.join(',');
      LogUtil.d('[AssetAlert] 更新选中组: ${groupIds.length}个组');
    } else {
      // 如果没有组数据，清空组ID
      noticeGroupIds.value = '';
      LogUtil.d('[AssetAlert] 清空组选择');
    }

    LogUtil.d('[AssetAlert] 更新通知先完成 - 用户: ${noticeUserIds.value}, 组: ${noticeGroupIds.value}');
  }

  /// 准备数据
  Future<void> _prepareData() async {
    try {
      // 清空现有数据
      _clearExistingData();

      await showLoading();

      // 并行获取用户列表和角色列表以提高性能
      await _loadUserAndRoleData();

      // 处理时间选项数据
      _processTimeOptions();
    } catch (e, stackTrace) {
      LogUtil.e('准备数据时发生错误: $e', stackTrace: stackTrace);
      // 可以在这里添加用户友好的错误提示
    } finally {
      hideLoading();
    }
  }

  /// 清空现有数据
  void _clearExistingData() {
    timeIds.clear();
    timeOptions.clear();
  }

  /// 并行加载用户和角色数据
  Future<void> _loadUserAndRoleData() async {
    const String logPrefix = '[AssetAlert]';

    // 并行执行两个网络请求以提高性能
    await Future.wait([_loadUserList(), _loadRoleList()]);

    LogUtil.d('$logPrefix 数据加载完成 - 用户: ${userList.length}个, 角色: ${roleList.length}个');
  }

  /// 加载用户列表
  Future<void> _loadUserList() async {
    try {
      final users = await getUserListUseCase(const NoParams());
      userList.clear();
      userList.assignAll(users);
      LogUtil.d('[AssetAlert] 成功获取用户列表: ${users.length}个用户');
    } catch (e, stackTrace) {
      LogUtil.e('[AssetAlert] 获取用户列表失败: $e', stackTrace: stackTrace);
      // 确保列表为空状态
      userList.clear();
      rethrow;
    }
  }

  /// 加载角色列表
  Future<void> _loadRoleList() async {
    try {
      final roles = await getRoleListUseCase(const NoParams());
      roleList.clear();
      roleList.assignAll(roles);
      LogUtil.d('[AssetAlert] 成功获取角色列表: ${roles.length}个角色');
    } catch (e, stackTrace) {
      LogUtil.e('[AssetAlert] 获取角色列表失败: $e', stackTrace: stackTrace);
      // 确保列表为空状态
      roleList.clear();
      rethrow;
    }
  }

  /// 处理时间选项数据
  void _processTimeOptions() {
    // 从预约项目通用列表中提取时间选项
    _extractTimeOptionsFromReservationItems();

    // 从事件类型列表中提取时间选项
    _extractTimeOptionsFromEventTypes();

    LogUtil.d('[AssetAlert] 时间选项处理完成 - 共${timeOptions.length}个选项');
  }

  /// 从预约项目通用列表中提取时间选项
  void _extractTimeOptionsFromReservationItems() {
    const String dateItemType = 'date';
    const String commonIdPrefix = 'common_';

    arguments.reservationItemCommonList?.whereType<ReservationItemCommon>().forEach((element) {
      if (element.itemType == dateItemType) {
        final id = '$commonIdPrefix${element.itemId}';
        _addTimeOption(id, element.itemLabel);
      }
    });
  }

  /// 从事件类型列表中提取时间选项
  void _extractTimeOptionsFromEventTypes() {
    final eventResult = _findMatchingEventType();

    if (eventResult != null) {
      eventResult.itemList?.forEach((element) {
        if (element?.itemType == SharedItemTypeEnum.date.value) {
          final id = element?.itemId?.toString() ?? '';
          if (id.isNotEmpty) {
            _addTimeOption(id, element?.itemLabel);
          }
        }
      });
    }
  }

  /// 查找匹配的事件类型
  EventType? _findMatchingEventType() {
    final eventTypeId = arguments.appointment?.eventTypeId?.toString();
    if (eventTypeId == null) return null;

    return arguments.eventTypeList?.whereType<EventType>().where((element) => element.key == eventTypeId).firstOrNull;
  }

  /// 添加时间选项（避免重复）
  void _addTimeOption(String id, String? label) {
    if (id.isNotEmpty && !timeIds.contains(id)) {
      timeIds.add(id);
      timeOptions.add(label ?? '');

      // 如果当前没有选中的时间类型，设置第一个为默认选中
      if (alertTimeType.value.isEmpty) {
        alertTimeType.value = id;
      }
    }
  }

  /// 初始化参数
  void _initParams() {
    final args = Get.arguments;
    if (args is Map<String, dynamic>) {
      arguments = AssetAlertArguments.fromMap(args);
    } else if (args is AssetAlertArguments) {
      arguments = args;
    } else {
      throw ArgumentError('Invalid arguments type for AssetAlertController');
    }

    if (!arguments.isValid) {
      LogUtil.e('AssetAlertArguments is invalid: $arguments');
    }

    LogUtil.d('AssetAlertArguments: $arguments');

    // 初始化保存按钮显隐状态
    isSaveButtonVisible.value = arguments.isSaveButtonVisible ?? false;

    // 初始化启用状态
    final appointment = arguments.appointment;
    final alertData = appointment?.alertData;
    final effectiveFlg = alertData?['effectiveFlg'];
    enable.value = effectiveFlg == null ? true : effectiveFlg == '1';

    // 初始化通知设定的选中状态和临时数据
    final noticeType = alertData?['noticeType']?.toString() ?? '';
    _tempNoticeType.value = noticeType;
    emailEvent.value = noticeType.contains('mail');
    pushEvent.value = noticeType.contains('push');

    // 初始化通知先
    noticeUserIds.value = alertData?['noticeUserIds']?.toString() ?? '';
    noticeGroupIds.value = alertData?['noticeGroupIds']?.toString() ?? '';

    // 初始化件名和临时数据
    final noticeTitle = alertData?['noticeTitle']?.toString() ?? '';
    _tempNoticeTitle.value = noticeTitle;
    nameController.text = noticeTitle;

    // 初始化文本内容和临时数据
    final noticeMessage = getNoticeMessage();
    _tempNoticeMessage.value = noticeMessage;
    textController.text = noticeMessage;

    // 初始化 alertTimeDynamic 数据和临时数据
    final alertTimeDynamicJson = alertData?['alertTimeDynamic']?.toString() ?? '';
    _tempAlertTimeDynamic.value = alertTimeDynamicJson;

    // 从 alertTimeDynamic JSON 中解析 methodValue 并设置到文本框
    final alertTime = _parseAlertTimeDynamicMethodValue(alertTimeDynamicJson);
    timeValueController.text = alertTime;

    // 繰返し通知
    final noticeCycleActiveFlg = alertData?['noticeCycleActiveFlg']?.toString() ?? '';
    returnNotice.value = noticeCycleActiveFlg == '1';

    // 初始化繰返し通知JSON数据和临时数据
    final returnNoticeDataJson = alertData?['noticeCycle']?.toString() ?? '';
    _tempReturnNoticeData.value = returnNoticeDataJson;

    // 解析繰返し通知JSON数据并设置到对应控件
    _parseReturnNoticeData(returnNoticeDataJson);
  }
}
