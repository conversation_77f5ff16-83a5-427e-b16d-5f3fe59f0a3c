import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_tile.dart';
import 'package:asset_force_mobile_v2/features/asset_alert/presentation/bindings/asset_alert_binding.dart';
import 'package:asset_force_mobile_v2/features/asset_alert/presentation/controllers/asset_alert_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/asset_alert', binding: AssetAlertBinding)
class AssetAlertPage extends GetView<AssetAlertController> {
  const AssetAlertPage({super.key});

  // 常量定义
  static const int _titleFlex = 40;
  static const int _contentFlex = 60;
  static const TextStyle _dropdownTextStyle = TextStyle(fontSize: 14.5, color: AppTheme.darkShadesGrayColor);
  static const TextStyle _normalTextStyle = TextStyle(fontSize: 14);
  static const EdgeInsets _dropdownPadding = EdgeInsets.symmetric(horizontal: 12, vertical: 0);
  static const SizedBox _verticalSpacing = SizedBox(height: 8);
  static const SizedBox _horizontalSpacing = SizedBox(width: 8);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('アラート設定'),
        leading: IconButton(icon: const Icon(Icons.arrow_back_ios_new, size: 20), onPressed: controller.goBack),
        actions: [
          Obx(() {
            // 根据控制器的显隐状态控制保存按钮
            if (controller.isSaveButtonVisible.value) {
              return TextButton(
                onPressed: controller.save,
                child: const Text('保存', style: TextStyle(color: Colors.white)),
              );
            } else {
              return const SizedBox.shrink(); // 隐藏时返回空组件
            }
          }),
        ],
      ),
      body: SingleChildScrollView(
        child: Container(
          width: double.infinity,
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.fromLTRB(16, 0, 10, 10),
          decoration: const BoxDecoration(
            color: AppTheme.white85Color,
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          child: Column(
            children: <Widget>[
              // 启用
              _buildEnableSwitch(),
              _buildSplitLine(),
              // 通知
              _buildNotice(),
              _buildSplitLine(),
              // 通知先
              _buildNoticeTo(),
              _buildSplitLine(),
              // 件名
              _buildName(),
              _buildSplitLine(),
              // 文本
              _buildText(),
              _buildSplitLine(),
              // 时间
              _buildTime(),
              _buildSplitLine(),
              // 返回通知
              _buildReturnNotice(),
              _buildSplitLine(),
              _buildTopic(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建通用的Checkbox组件（参考AfCustomizeCheckboxItemView的实现）
  Widget _buildCheckbox({bool value = true, ValueChanged<bool?>? onChanged, String? text}) {
    final checkbox = SizedBox(
      height: 24,
      width: 24,
      child: Checkbox(
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.darkBlueColor,
        checkColor: AppTheme.whiteColor,
        side: const BorderSide(width: 1, color: AppTheme.grayColor),
      ),
    );

    if (text != null) {
      // 根据onChanged是否为null判断是否禁用
      final isDisabled = onChanged == null;
      final textColor = isDisabled ? Colors.grey : Colors.black;

      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          checkbox,
          const SizedBox(width: 8),
          Text(text, style: TextStyle(fontSize: 14, color: textColor)),
        ],
      );
    }

    return checkbox;
  }

  /// 构建checkbox行组件（用于多个checkbox场景）
  Widget _buildCheckboxRow(String text, RxBool value, Function(bool?)? onChanged) {
    // 根据onChanged是否为null判断是否禁用
    final isDisabled = onChanged == null;
    final textColor = isDisabled ? Colors.grey : Colors.black;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          SizedBox(
            height: 20,
            width: 20,
            child: Obx(
              () => Checkbox(
                value: value.value,
                onChanged: onChanged,
                activeColor: AppTheme.darkBlueColor,
                checkColor: AppTheme.whiteColor,
                side: const BorderSide(width: 1, color: AppTheme.grayColor),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(text, style: TextStyle(fontSize: 14, color: textColor)),
        ],
      ),
    );
  }

  /// 构建通用的下拉框组件
  Widget _buildDropdown<T>({
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?> onChanged,
    double height = 40,
    bool enabled = true,
    EdgeInsets padding = _dropdownPadding,
  }) {
    return Container(
      height: height,
      width: double.infinity,
      padding: padding,
      decoration: BoxDecoration(
        color: enabled ? Colors.white : Colors.grey[100],
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
      ),
      child: DropdownButton<T>(
        value: value,
        isExpanded: true,
        underline: const SizedBox(),
        icon: const SizedBox(),
        items: items,
        onChanged: enabled ? onChanged : null,
      ),
    );
  }

  /// 构建通用的输入框组件
  Widget _buildInputField({
    required TextEditingController textController,
    required FocusNode focusNode,
    required RxBool isFocused,
    VoidCallback? onConfirm,
    double? width,
    double height = 40,
    int? maxLines,
    int? minLines,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    TextInputAction textInputAction = TextInputAction.done,
    bool showBackground = true,
    bool showHint = false,
    bool enabled = true,
    EdgeInsets padding = const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
  }) {
    final isMultiline = maxLines == null || maxLines > 1;

    return Container(
      width: width,
      height: isMultiline ? null : height,
      constraints: isMultiline ? const BoxConstraints(minHeight: 80, maxHeight: 200) : null,
      padding: padding,
      decoration: showBackground
          ? BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge))
          : null,
      child: Obx(() {
        final hasFocus = isFocused.value;
        return Row(
          crossAxisAlignment: isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center,
          children: [
            Expanded(
              child: TextField(
                controller: textController,
                focusNode: focusNode,
                enabled: enabled,
                style: TextStyle(fontSize: 14.5, color: enabled ? AppTheme.darkShadesGrayColor : Colors.grey),
                maxLines: maxLines,
                minLines: minLines,
                keyboardType: keyboardType,
                inputFormatters: inputFormatters,
                textInputAction: textInputAction,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(vertical: isMultiline ? 8 : 4, horizontal: 0),
                  hintText: showHint ? '内容を入力してください...' : null,
                  hintStyle: showHint ? const TextStyle(fontSize: 14.5, color: Colors.grey) : null,
                ),
                textAlignVertical: isMultiline ? TextAlignVertical.top : TextAlignVertical.center,
                onEditingComplete: () => focusNode.unfocus(),
                textCapitalization: TextCapitalization.none,
                expands: false,
              ),
            ),
            if (hasFocus)
              Container(
                width: 30,
                alignment: isMultiline ? Alignment.topCenter : Alignment.center,
                padding: EdgeInsets.only(top: isMultiline ? 8 : 0),
                child: IconButton(
                  icon: const Icon(Icons.check, color: AppTheme.darkBlueColor),
                  onPressed: onConfirm ?? () => focusNode.unfocus(),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  iconSize: 20,
                ),
              ),
          ],
        );
      }),
    );
  }

  /// 构建开关组件
  Widget _buildEnableSwitch() {
    return AfCustomizeTile(
      title: '有効',
      isRequired: false,
      content: Align(
        alignment: Alignment.centerLeft,
        child: Obx(() {
          return _buildCheckbox(
            value: controller.enable.value,
            // 根据保存按钮可见性控制是否可编辑
            onChanged: controller.isSaveButtonVisible.value ? controller.enableChange : null,
          );
        }),
      ),
      titleFlex: _titleFlex,
      contentFlex: _contentFlex,
    );
  }

  /// 构建通知组件
  Widget _buildNotice() {
    return AfCustomizeTile(
      title: '通知設定',
      requiredMark: '※',
      isRequired: true,
      content: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCheckboxRow(
              'メール',
              controller.emailEvent,
              // 根据保存按钮可见性控制是否可编辑
              controller.isSaveButtonVisible.value ? controller.emailEventChange : null,
            ),
            _buildCheckboxRow('プッシュ通知', controller.pushEvent, null), // push通知始终禁用
          ],
        );
      }),
      titleFlex: _titleFlex,
      contentFlex: _contentFlex,
    );
  }

  /// 构建通知先组件
  Widget _buildNoticeTo() {
    return Obx(() {
      return GestureDetector(
        // 根据保存按钮可见性控制是否可点击
        onTap: controller.isSaveButtonVisible.value ? controller.onNoticeToTap : null,
        child: AfCustomizeTile(
          title: '通知先',
          isRequired: true,
          requiredMark: '※',
          trailingIcon: controller.isSaveButtonVisible.value ? Icons.arrow_forward_ios : Icons.lock_outline,
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('グループ', style: TextStyle(color: controller.isSaveButtonVisible.value ? Colors.black : Colors.grey)),
              Obx(() {
                return _buildSelectedGroupTags();
              }),
              const SizedBox(height: 10),
              Text('担当者', style: TextStyle(color: controller.isSaveButtonVisible.value ? Colors.black : Colors.grey)),
              Obx(() {
                return _buildSelectedUserTags();
              }),
            ],
          ),
          titleFlex: _titleFlex,
          contentFlex: _contentFlex,
        ),
      );
    });
  }

  /// 构建通知先标签
  Widget _buildNoticeTag(String text, {Color color = const Color(0xff0b3e86), Color fontColor = Colors.white}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(12)),
      child: Text(
        text,
        style: TextStyle(fontSize: 12, color: fontColor, fontWeight: FontWeight.w500),
      ),
    );
  }

  /// 构建选中的组标签列表
  Widget _buildSelectedGroupTags() {
    // 获取选中的组ID列表
    final selectedGroupIds = _getSelectedGroupIds();

    if (selectedGroupIds.isEmpty) {
      return const SizedBox.shrink();
    }

    // 从角色列表中筛选出选中的组
    final selectedGroups = controller.roleList
        .where((role) => role.roleId != null && selectedGroupIds.contains(role.roleId.toString()))
        .toList();

    return Wrap(
      spacing: 8.0, // 水平间距
      runSpacing: 4.0, // 垂直间距
      children: selectedGroups.map((role) {
        return _buildNoticeTag(role.roleName);
      }).toList(),
    );
  }

  /// 获取选中的组ID列表
  Set<String> _getSelectedGroupIds() {
    if (controller.noticeGroupIds.value.isEmpty) {
      return <String>{};
    }

    return controller.noticeGroupIds.value
        .split(',')
        .where((id) => id.trim().isNotEmpty)
        .map((id) => id.trim())
        .toSet();
  }

  /// 构建选中的用户标签列表
  Widget _buildSelectedUserTags() {
    // 获取选中的用户ID列表
    final selectedUserIds = _getSelectedUserIds();

    if (selectedUserIds.isEmpty) {
      return const SizedBox.shrink();
    }

    // 从用户列表中筛选出选中的用户
    final selectedUsers = controller.userList
        .where((user) => user.userId != null && selectedUserIds.contains(user.userId.toString()))
        .toList();

    return Wrap(
      spacing: 8.0, // 水平间距
      runSpacing: 4.0, // 垂直间距
      children: selectedUsers.map((user) {
        // 构建用户显示名称：优先使用姓名组合，其次是用户名
        final displayName = _buildUserDisplayName(user);
        return _buildNoticeTag(displayName);
      }).toList(),
    );
  }

  /// 获取选中的用户ID列表
  Set<String> _getSelectedUserIds() {
    if (controller.noticeUserIds.value.isEmpty) {
      return <String>{};
    }

    return controller.noticeUserIds.value.split(',').where((id) => id.trim().isNotEmpty).map((id) => id.trim()).toSet();
  }

  /// 构建用户显示名称
  String _buildUserDisplayName(SharedUserModel user) {
    // 优先使用姓名组合
    if (user.lastName?.isNotEmpty == true || user.firstName?.isNotEmpty == true) {
      final lastName = user.lastName ?? '';
      final firstName = user.firstName ?? '';
      return '$lastName$firstName'.trim();
    }

    // 其次使用用户名
    if (user.userName?.isNotEmpty == true) {
      return user.userName!;
    }

    // 最后使用用户ID作为备选
    return 'User ${user.userId ?? ''}';
  }

  /// 构建分割线组件
  _buildSplitLine() {
    return const Divider(height: 0.5, thickness: 0.5, indent: 0, endIndent: 5, color: AppTheme.grayColor);
  }

  /// 构建件名
  Widget _buildName() {
    return AfCustomizeTile(
      title: '件名',
      requiredMark: '※',
      isRequired: true,
      content: Align(
        alignment: Alignment.centerLeft,
        child: Obx(() {
          return _buildInputField(
            textController: controller.nameController,
            focusNode: controller.nameFocusNode,
            isFocused: controller.isNameFocused,
            padding: EdgeInsets.zero,
            onConfirm: controller.onNameConfirm,
            maxLines: 1,
            showBackground: false,
            // 根据保存按钮可见性控制是否可编辑
            enabled: controller.isSaveButtonVisible.value,
          );
        }),
      ),
      titleFlex: _titleFlex,
      contentFlex: _contentFlex,
    );
  }

  /// 文本
  Widget _buildText() {
    return AfCustomizeTile(
      title: '文本',
      isRequired: false,
      content: Align(
        alignment: Alignment.centerLeft,
        child: Obx(() {
          return _buildInputField(
            padding: EdgeInsets.zero,
            textController: controller.textController,
            focusNode: controller.textFocusNode,
            isFocused: controller.isTextFocused,
            onConfirm: controller.onTextConfirm,
            maxLines: null,
            minLines: 3,
            showBackground: false,
            showHint: true,
            // 根据保存按钮可见性控制是否可编辑
            enabled: controller.isSaveButtonVisible.value,
          );
        }),
      ),
      titleFlex: _titleFlex,
      contentFlex: _contentFlex,
    );
  }

  /// アラート時刻
  Widget _buildTime() {
    return AfCustomizeTile(
      title: 'アラート時刻',
      requiredMark: '※',
      isRequired: true,
      content: Obx(() {
        final isEnabled = controller.isSaveButtonVisible.value;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 确保 timeIds 和 timeOptions 长度一致
            if (controller.timeIds.length != controller.timeOptions.length)
              Container(
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                child: const Text('データエラー'),
              )
            else
              // 检查当前选中的值是否在列表中
              Builder(
                builder: (context) {
                  final String? currentValue = controller.alertTimeType.value.isEmpty
                      ? null
                      : controller.timeIds.contains(controller.alertTimeType.value)
                      ? controller.alertTimeType.value
                      : null;

                  return _buildDropdown<String>(
                    value: currentValue,
                    enabled: isEnabled,
                    items: controller.timeIds.isEmpty
                        ? [const DropdownMenuItem<String>(value: null, child: Text('選択肢がありません'))]
                        : List.generate(controller.timeOptions.length, (index) {
                            return DropdownMenuItem<String>(
                              value: controller.timeIds[index],
                              child: Text(controller.timeOptions[index], style: _dropdownTextStyle),
                            );
                          }),
                    onChanged: controller.onAlertTimeTypeChanged,
                  );
                },
              ),
            _verticalSpacing,
            _buildDropdown<String>(
              value: controller.repeatType.value,
              enabled: isEnabled,
              items: const [
                DropdownMenuItem(
                  value: '1',
                  child: Text('〇時間前', style: _dropdownTextStyle),
                ),
                DropdownMenuItem(
                  value: '2',
                  child: Text('〇日前', style: _dropdownTextStyle),
                ),
                DropdownMenuItem(
                  value: '3',
                  child: Text('〇週間前', style: _dropdownTextStyle),
                ),
                DropdownMenuItem(
                  value: '4',
                  child: Text('〇カ月前', style: _dropdownTextStyle),
                ),
              ],
              onChanged: controller.onRepeatTypeChanged,
            ),
            _verticalSpacing,
            Container(
              width: double.infinity,
              child: Row(
                children: [
                  Expanded(
                    child: _buildInputField(
                      padding: _dropdownPadding,
                      textController: controller.timeValueController,
                      focusNode: controller.timeValueFocusNode,
                      isFocused: controller.isTimeValueFocused,
                      onConfirm: controller.onTimeValueConfirm,
                      maxLines: 1,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      enabled: isEnabled,
                    ),
                    flex: 1,
                  ),
                  _horizontalSpacing,
                  Expanded(
                    child: Text(
                      controller.repeatTypeText,
                      style: TextStyle(color: isEnabled ? Colors.black : Colors.grey),
                    ),
                    flex: 1,
                  ),
                ],
              ),
            ),
          ],
        );
      }),
      titleFlex: _titleFlex,
      contentFlex: _contentFlex,
    );
  }

  /// 繰返し通知
  Widget _buildReturnNotice() {
    return AfCustomizeTile(
      title: '繰返し通知',
      isRequired: false,
      content: Obx(() {
        final isEnabled = controller.isSaveButtonVisible.value;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: _buildCheckbox(
                value: controller.returnNotice.value,
                onChanged: isEnabled ? controller.returnNoticeChange : null,
              ),
            ),
            if (controller.returnNotice.value) ...[
              _verticalSpacing,
              Text('初回通知後の', style: TextStyle(color: isEnabled ? Colors.black : Colors.grey)),
              _verticalSpacing,
              _buildInputField(
                textController: controller.repeatIntervalController,
                focusNode: controller.repeatIntervalFocusNode,
                isFocused: controller.isRepeatIntervalFocused,
                onConfirm: controller.onRepeatIntervalConfirm,
                width: 90,
                maxLines: 1,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                enabled: isEnabled,
              ),
              _verticalSpacing,
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 90,
                    child: _buildDropdown<String>(
                      value: controller.returnNoticeRepeatType.value,
                      enabled: isEnabled,
                      items: const [
                        DropdownMenuItem(value: '1', child: Text('時間')),
                        DropdownMenuItem(value: '2', child: Text('日')),
                        DropdownMenuItem(value: '3', child: Text('週間')),
                        DropdownMenuItem(value: '4', child: Text('カ月')),
                      ],
                      onChanged: controller.onReturnNoticeRepeatTypeChanged,
                    ),
                  ),
                  _horizontalSpacing,
                  Expanded(
                    child: Text(
                      'ごと 繰返し',
                      style: _normalTextStyle.copyWith(color: isEnabled ? Colors.black : Colors.grey),
                      softWrap: true,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                ],
              ),
              _verticalSpacing,
              Row(
                children: [
                  Text(
                    '回数',
                    style: _normalTextStyle.copyWith(color: isEnabled ? Colors.black : Colors.grey),
                    softWrap: true,
                    overflow: TextOverflow.visible,
                  ),
                  _horizontalSpacing,
                  _buildInputField(
                    textController: controller.repeatCountController,
                    focusNode: controller.repeatCountFocusNode,
                    isFocused: controller.isRepeatCountFocused,
                    onConfirm: controller.onRepeatCountConfirm,
                    width: 90,
                    maxLines: 1,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    enabled: isEnabled,
                  ),
                  _horizontalSpacing,
                  Text(
                    '回',
                    style: _normalTextStyle.copyWith(color: isEnabled ? Colors.black : Colors.grey),
                    softWrap: true,
                    overflow: TextOverflow.visible,
                  ),
                ],
              ),
            ],
          ],
        );
      }),
      titleFlex: _titleFlex,
      contentFlex: _contentFlex,
    );
  }

  /// トピック
  _buildTopic() {
    return const Column(
      children: <Widget>[
        SizedBox(height: 8),
        Text('※設定された担当者はアラートに添付された資産の閲覧が可能となります', style: TextStyle(fontSize: 14.5, color: AppTheme.grayColor)),
      ],
    );
  }
}
