import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/asset_alert/presentation/constants/notification_select_constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 可展开的头部组件
class ExpandableHeaderWidget extends StatelessWidget {
  final String title;
  final VoidCallback? onTap;
  final RxBool isExpanded;

  const ExpandableHeaderWidget({super.key, required this.title, required this.onTap, required this.isExpanded});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: NotificationSelectConstants.headerHeight,
        margin: const EdgeInsets.only(
          left: NotificationSelectConstants.headerMarginHorizontal,
          right: NotificationSelectConstants.headerMarginHorizontal,
          top: NotificationSelectConstants.headerMarginTop,
        ),
        padding: const EdgeInsets.symmetric(horizontal: NotificationSelectConstants.headerPaddingHorizontal),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(NotificationSelectConstants.headerBorderRadius)),
        ),
        child: Row(
          children: <Widget>[
            Container(
              width: NotificationSelectConstants.indicatorWidth,
              height: NotificationSelectConstants.indicatorHeight,
              decoration: BoxDecoration(
                color: AppTheme.darkBlueColor,
                borderRadius: BorderRadius.circular(NotificationSelectConstants.indicatorBorderRadius),
              ),
            ),
            const SizedBox(width: NotificationSelectConstants.spacerWidth),
            Text(
              title,
              style: const TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.bold,
                fontSize: NotificationSelectConstants.titleFontSize,
              ),
            ),
            const Spacer(),
            _buildExpandableIcon(),
          ],
        ),
      ),
    );
  }

  /// 构建可展开的图标
  Widget _buildExpandableIcon() {
    return Obx(
      () => Icon(
        isExpanded.value ? Icons.remove_circle_outline : Icons.add_circle_outline,
        size: NotificationSelectConstants.iconSize,
        color: Colors.grey,
      ),
    );
  }
}
