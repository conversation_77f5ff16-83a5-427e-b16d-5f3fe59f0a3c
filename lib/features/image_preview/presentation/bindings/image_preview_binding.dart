import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/image_preview/presentation/controller/image_preview_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:get/get.dart';

class ImagePreviewBinding extends Bindings {
  @override
  void dependencies() {
    // 注入 GetTurlUseCase，用于获取图片临时URL
    Get.lazyPut(() => GetTurlUseCase(s3Repository: Get.find<S3Repository>()));

    Get.lazyPut(() => ImagePreviewController(getTurlUseCase: Get.find<GetTurlUseCase>()));
  }
}
