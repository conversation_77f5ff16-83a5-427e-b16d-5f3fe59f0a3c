import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/image_preview_result.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_image_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/image_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/image_preview/presentation/pages/image_preview_page.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_mixin.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 图片预览控制器
///
/// 用于控制图片预览页面的显示和交互
///
/// [imageList] 需要预览的图片列表
/// 每个图片对象包含:
/// - url: 图片地址
/// - name: 图片名称
/// - size: 图片大小
/// - createTime: 创建时间
/// - updateTime: 更新时间
class ImagePreviewController extends GetxController with OverlayMixin {
  final GetTurlUseCase getTurlUseCase;

  ImagePreviewController({required this.getTurlUseCase});

  /// 图片列表
  final RxList<RxImageModel> imageList = <RxImageModel>[].obs;

  /// 当前显示的图片索引
  int currentIndex = 0;

  /// 页面标题
  final pageTitle = ''.obs;

  /// 页面控制器
  var pageController = PageController();

  /// 当前是否是主图像
  var isCurrentHomeImage = false.obs;

  /// 是否显示设置主图像按钮
  var showSetHomeImageBtn = false.obs;

  /// 被删除的图片URL列表
  ///
  /// 记录在预览过程中被用户删除的图片原始URL
  /// 用于在返回编辑页面时传递给S3清理功能
  final List<String> deletedImageUrls = [];

  /// 初始化图片预览控制器
  @override
  void onInit() {
    super.onInit();
    try {
      final arguments = Get.arguments as ImagePreviewPageArguments;
      pageTitle.value = arguments.pageTitle;
      showSetHomeImageBtn.value = arguments.isShowSetHomeImageBtn;
      // 使用safeIndex方法安全地获取索引，支持String和int类型
      currentIndex = arguments.safeIndex;
      // 确保索引在有效范围内
      if (currentIndex < 0) currentIndex = 0;

      imageList.value = arguments.imageModels;

      // 再次检查索引范围，避免数组越界
      if (currentIndex >= imageList.length) {
        currentIndex = imageList.length > 0 ? imageList.length - 1 : 0;
        LogUtil.w('Adjusted currentIndex to $currentIndex because it exceeded imageList length ${imageList.length}');
      }

      pageController = PageController(initialPage: currentIndex);
      pageController.addListener(() {
        final pageIndex = pageController.page?.toInt() ?? 0;
        // 确保索引在有效范围内
        if (pageIndex >= 0 && pageIndex < imageList.length) {
          currentIndex = pageIndex;
          _updateCurrentHomeImageStatus(currentIndex);
        }
      });
      _updateCurrentHomeImageStatus(currentIndex);
    } catch (e) {
      LogUtil.e('Error in ImagePreviewController onInit: $e');
      // 设置默认值以防初始化失败
      pageTitle.value = '画像プレビュー';
      currentIndex = 0;
      imageList.value = [];
      pageController = PageController(initialPage: 0);
    }
  }

  /// 更新当前图片是否为主图像状态
  ///
  /// 参数:
  /// * [index] - 当前图片在列表中的索引
  ///
  /// 说明:
  /// 1. 根据传入的索引获取对应图片
  /// 2. 获取该图片的 isHomeImage 属性值
  /// 3. 更新 isCurrentHomeImage 响应式变量的值
  void _updateCurrentHomeImageStatus(int index) {
    // 添加边界检查，防止数组越界
    if (index >= 0 && index < imageList.length) {
      isCurrentHomeImage.value = imageList[index].isHomeImage.value;
    } else {
      isCurrentHomeImage.value = false;
      // 记录边界错误但不抛出异常
      LogUtil.w('Invalid index $index for imageList of length ${imageList.length}');
    }
  }

  /// 设置当前显示的图片索引
  void setCurrentIndex(int index) {
    currentIndex = index;
  }

  /// 获取当前图片
  RxImageModel getCurrentImage() {
    // 添加边界检查，防止数组越界
    if (currentIndex >= 0 && currentIndex < imageList.length) {
      return imageList[currentIndex];
    }
    // 如果索引无效，记录错误并返回一个默认的空图片模型
    LogUtil.w('Invalid currentIndex $currentIndex for imageList of length ${imageList.length}');
    return RxImageModel.fromImageModel(ImageModel(url: '', fileName: '', isHomeImage: false, loaded: false));
  }

  /// 获取当前显示的图片索引
  int getCurrentIndex() {
    return currentIndex;
  }

  /// 获取图片列表
  List<RxImageModel> getImageList() {
    return imageList;
  }

  /// 删除指定索引的图片
  ///
  /// 从图片列表中删除指定索引位置的图片
  /// 删除后将图片URL记录到删除跟踪列表中，用于后续S3清理
  ///
  /// 功能特点：
  /// - 显示确认对话框，确保用户意图
  /// - 记录被删除图片的原始URL用于S3清理
  /// - 删除后自动调整当前索引
  /// - 如果删除后列表为空，自动返回上一页面
  ///
  /// 示例:
  /// ```dart
  /// controller.deleteImageWithIndex(); // 删除当前显示的图片
  /// ```
  void deleteImageWithIndex() {
    CommonDialog.show(
      type: DialogType.error,
      content: '削除しますか？',
      onConfirm: () {
        // 获取要删除的图片对象
        final imageToDelete = imageList[currentIndex];

        // 记录被删除图片的原始URL（非临时URL）
        final originalUrl = imageToDelete.url.value;
        if (originalUrl.isNotEmpty) {
          deletedImageUrls.add(originalUrl);
          LogUtil.d('记录删除的图片URL: $originalUrl，当前删除列表长度: ${deletedImageUrls.length}');
        }

        // 从列表中删除图片
        imageList.removeAt(currentIndex);

        // 如果删除后列表为空，返回上一页面
        if (imageList.isEmpty) {
          onBackBtnClick();
        } else {
          // 调整当前索引，确保不超出范围
          if (currentIndex >= imageList.length) {
            currentIndex = imageList.length - 1;
            pageController.animateToPage(
              currentIndex,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
          // 更新当前图片的首页状态
          _updateCurrentHomeImageStatus(currentIndex);
        }
      },
      cancelText: 'キャンセル',
      confirmText: 'OK',
    );
  }

  /// 将当前图片设置为主图像或取消主图像设置
  ///
  /// 通过弹出确认对话框让用户确认是否要更改当前图片的主图像状态。
  /// 如果用户确认,则:
  /// 1. 切换当前图片的主图像状态
  /// 2. 显示操作成功的提示信息
  /// 3. 返回到上一页面,并携带更新后的图片列表数据
  ///
  /// 参数说明:
  /// - 无需传入参数,使用控制器内部的 currentIndex 获取当前图片
  ///
  /// 返回值:
  /// - 通过 Get.back() 返回 ImagePreviewResult 对象,包含:
  ///   - imageModels: 更新后的图片列表
  ///   - isHomeImageSet: 是否设置了主图像
  ///   - selectedIndex: 当前选中的图片索引
  void setHomeImageWithIndex() {
    // 添加边界检查，防止数组越界
    if (currentIndex < 0 || currentIndex >= imageList.length) {
      LogUtil.e('Invalid currentIndex $currentIndex for imageList of length ${imageList.length}');
      CommonDialog.show(content: '画像の設定に失敗しました', type: DialogType.error);
      return;
    }

    final isCurrentHomeImage = imageList[currentIndex].isHomeImage.value;
    CommonDialog.show(
      type: DialogType.confirm,
      content: isCurrentHomeImage ? 'メイン画像設定を解除しますか？' : 'この画像をメイン画像に設定しますか？',
      onConfirm: () {
        // 再次检查边界，防止在对话框显示期间发生变化
        if (currentIndex >= 0 && currentIndex < imageList.length) {
          imageList[currentIndex].isHomeImage.value = !isCurrentHomeImage;
          CommonDialog.showCustomToast(isCurrentHomeImage ? 'メイン画像設定を解除しました' : 'メイン画像を設定しました');
          Get.back(
            result: ImagePreviewResult(
              imageModels: imageList,
              isHomeImageSet: !isCurrentHomeImage,
              selectedIndex: currentIndex,
            ),
          );
        } else {
          LogUtil.e('Index changed during dialog display: $currentIndex');
          CommonDialog.show(content: '画像の設定に失敗しました', type: DialogType.error);
        }
      },
      cancelText: 'キャンセル',
      confirmText: 'OK',
    );
  }

  @override
  void onClose() {
    super.onClose();
    pageController.dispose();
  }

  /// 返回上一页面
  ///
  /// 将图片列表和删除的URL列表一起返回给编辑页面
  /// 用于数据同步和S3文件清理
  void onBackBtnClick() {
    LogUtil.d('图片预览页面返回，删除的图片URL数量: ${deletedImageUrls.length}');
    Get.back(
      result: ImagePreviewResult(
        imageModels: imageList,
        deletedImageUrls: deletedImageUrls.isNotEmpty ? deletedImageUrls : null,
      ),
    );
  }

  /// 获取图片临时URL
  ///
  /// 通过UseCase获取图片的临时URL
  ///
  /// [url] 原始图片URL
  /// 返回临时URL，失败时返回原始URL
  Future<String> getTurl(String url) async {
    try {
      return await getTurlUseCase(url);
    } catch (e) {
      LogUtil.e('获取临时URL失败: $e');
      // 如果获取临时URL失败，返回原始URL
      return url;
    }
  }

  /// 异步获取图片URL
  ///
  /// 符合Clean Architecture原则的异步URL获取方法
  /// 所有业务逻辑都在控制器层处理，UI层只负责显示
  ///
  /// [imageObj] 图片模型对象
  void asyncGetImageUrl(RxImageModel imageObj) {
    // 避免重复请求：检查是否正在重试或正在获取临时URL
    if (imageObj.isRetrying.value || imageObj.isGettingTurl) {
      return;
    }

    // 设置获取状态，防止并发请求
    imageObj.isGettingTurl = true;

    Future.microtask(() async {
      try {
        final String originalUrl = imageObj.url.value;
        if (originalUrl.isEmpty || imageObj.turl.value.isNotEmpty) {
          return;
        }

        LogUtil.d('🌐 图片预览页面异步获取临时URL，原始URL: $originalUrl');

        // 通过UseCase获取临时URL
        final String newTurl = await getTurl(originalUrl);
        if (newTurl.isNotEmpty && newTurl != originalUrl) {
          // 更新临时URL，响应式属性会自动触发UI更新
          imageObj.turl.value = newTurl;
          LogUtil.d('✅ 图片预览页面异步更新临时URL成功: $newTurl');
        }
      } catch (e) {
        LogUtil.w('❌ 图片预览页面异步获取临时URL失败: $e');
      } finally {
        // 重置获取状态
        imageObj.isGettingTurl = false;
      }
    });
  }

  /// 重试获取图片URL
  ///
  /// 当图片加载失败时调用此方法进行重试，重新获取临时URL并更新图片对象
  ///
  /// [imageObj] 需要重试的图片模型对象
  /// 返回重试是否成功
  Future<bool> retryGetImageUrl(RxImageModel imageObj) async {
    // 检查重试次数限制，避免无限重试
    if (imageObj.retryCount >= 1) {
      LogUtil.w('🚫 图片预览页面重试次数已达上限，不再重试: ${imageObj.url.value}');
      return false;
    }

    // 获取原始URL
    final String? originalUrl = imageObj.url.value;
    if (originalUrl == null || originalUrl.isEmpty) {
      LogUtil.w('⚠️ 图片预览页面原始URL为空，无法进行重试');
      return false;
    }

    // 检查是否已在重试中，避免重复重试
    if (imageObj.isRetrying.value) {
      LogUtil.d('🔄 图片预览页面图片已在重试中，跳过重复重试');
      return false;
    }

    try {
      // 设置重试状态，触发UI更新显示加载指示器
      imageObj.isRetrying.value = true;
      imageObj.retryCount++;

      LogUtil.d('🔄 图片预览页面开始重试获取图片临时URL，重试次数: ${imageObj.retryCount}, 原始URL: $originalUrl');

      // 添加适当的延迟，避免立即重试造成服务器压力
      await Future.delayed(const Duration(milliseconds: 500));

      // 备份当前的turl，以防重试失败时可以恢复
      final String backupTurl = imageObj.turl.value;

      // 清空现有的turl缓存，强制重新获取
      imageObj.turl.value = '';

      // 调用 getTurl 获取新的临时URL
      final String newTurl = await getTurl(originalUrl);

      // 验证新获取的URL是否有效
      if (newTurl.isNotEmpty) {
        imageObj.turl.value = newTurl;
        LogUtil.d('✅ 图片预览页面重试成功，获取到新的临时URL: $newTurl');
        return true;
      } else {
        // 如果新URL无效，恢复备份的URL
        imageObj.turl.value = backupTurl;
        LogUtil.w('⚠️ 图片预览页面重试获取的URL无效，恢复原有URL');
        return false;
      }
    } catch (e) {
      // 重试失败的错误处理
      LogUtil.e('❌ 图片预览页面重试获取临时URL失败: $e');
      return false;
    } finally {
      // 无论成功失败，都要重置重试状态
      imageObj.isRetrying.value = false;
    }
  }

  /// 处理图片加载错误
  ///
  /// 符合Clean Architecture原则的错误处理方法
  /// 将重试逻辑封装在控制器层，UI层只负责调用和显示结果
  ///
  /// [imageObj] 图片模型对象
  /// [size] 组件尺寸
  /// 返回错误处理后的Widget
  Widget handleImageLoadError(RxImageModel imageObj, double size) {
    // 检查是否可以重试（未达到重试次数限制且未在重试中）
    if (imageObj.retryCount < 1 && !imageObj.isRetrying.value) {
      LogUtil.d('🔄 图片预览页面开始自动重试图片加载');

      // 异步执行重试逻辑，避免阻塞UI
      Future.microtask(() async {
        final bool retrySuccess = await retryGetImageUrl(imageObj);

        if (retrySuccess) {
          LogUtil.d('✅ 图片预览页面重试成功，图片URL已更新');
          // 重试成功后，通过更新响应式变量触发UI重建
          imageObj.loaded.value = !imageObj.loaded.value;
          imageObj.loaded.value = !imageObj.loaded.value;
        } else {
          LogUtil.w('❌ 图片预览页面重试失败，显示错误占位符');
        }
      });

      // 在重试过程中显示加载指示器
      return _buildLoadingWidget(size);
    } else {
      // 已达到重试次数限制或正在重试中，显示错误占位符
      LogUtil.w('🚫 图片预览页面已达到重试次数限制或正在重试中，显示错误占位符');
      return _buildErrorWidget(size);
    }
  }

  /// 构建加载指示器Widget
  ///
  /// 私有方法，用于构建统一的加载指示器
  ///
  /// [size] 组件尺寸
  Widget _buildLoadingWidget(double size) {
    return Container(
      color: AppTheme.white85Color,
      child: const Center(child: CupertinoActivityIndicator()),
    );
  }

  /// 构建错误占位符Widget
  ///
  /// 私有方法，用于构建统一的错误占位符
  ///
  /// [size] 组件尺寸
  Widget _buildErrorWidget(double size) {
    return Container(
      color: AppTheme.white85Color,
      child: const Center(child: Icon(Icons.error_outline, color: AppTheme.grayColor, size: 40.0)),
    );
  }
}
