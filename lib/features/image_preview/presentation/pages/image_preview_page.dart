import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/entities/rx_image_model.dart';
import 'package:asset_force_mobile_v2/features/image_preview/presentation/bindings/image_preview_binding.dart';
import 'package:asset_force_mobile_v2/features/image_preview/presentation/controller/image_preview_controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

/// 图片预览页面
///
/// 用于显示和预览图片列表
///
/// 继承自 GetView<ImagePreviewController>，使用 GetX 状态管理
///
/// 主要功能：
/// - 显示图片列表
/// - 支持图片切换
/// - 显示图片信息
///
/// 使用示例：
/// ```dart
/// Get.to(
///   ImagePreviewPage(),
///   binding: BindingsBuilder.put(() => ImagePreviewController(imageList: imageList)),
/// );
/// ```
@GetRoutePage('/image_preview', binding: ImagePreviewBinding)
class ImagePreviewPage extends GetView<ImagePreviewController> {
  const ImagePreviewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBody: true,
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        centerTitle: true,
        shadowColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        title: Obx(() {
          return Text('${controller.pageTitle.value}');
        }),
        foregroundColor: Colors.white,
        leadingWidth: 50,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, size: 20),
          onPressed: () {
            controller.onBackBtnClick();
          },
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: Obx(
              () => PageView.builder(
                controller: controller.pageController,
                itemCount: controller.imageList.length,
                itemBuilder: (context, index) {
                  // 添加边界检查，防止数组越界
                  if (index < 0 || index >= controller.imageList.length) {
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 16),
                      decoration: BoxDecoration(color: AppTheme.white85Color, borderRadius: BorderRadius.circular(6)),
                      child: const Center(
                        child: Text('画像エラー', style: TextStyle(color: AppTheme.blackColor, fontSize: 14)),
                      ),
                    );
                  }

                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 16),
                    decoration: BoxDecoration(color: AppTheme.white85Color, borderRadius: BorderRadius.circular(6)),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: _buildOptimizedImageContent(controller.imageList[index], index),
                    ),
                  );
                },
              ),
            ),
          ),
          _buildBottomBar(),
        ],
      ),
    );
  }

  /// 构建优化的图片内容
  ///
  /// 参考 af_customize_image_item_view.dart 的实现，支持自动重试机制
  ///
  /// 主要优化：
  /// - 使用稳定的缓存键策略
  /// - 智能的URL获取逻辑，避免不必要的网络请求
  /// - 改进的错误处理和重试机制
  /// - 更好的缓存管理，确保图片不会重复下载
  ///
  /// [imageObj] 图片模型对象，包含URL和重试状态
  /// [index] 图片在列表中的索引
  Widget _buildOptimizedImageContent(RxImageModel imageObj, int index) {
    return Obx(() {
      // 检查是否正在重试，优先显示重试状态
      if (imageObj.isRetrying.value) {
        return buildCupertinoLoadingWidget(size: 100);
      }

      // 智能获取显示URL：优先使用已缓存的turl，否则使用原始URL
      final String displayUrl = imageObj.getDisplayUrl();

      // 如果没有临时URL，通过控制器异步获取但不阻塞当前渲染
      if (imageObj.turl.value.isEmpty && imageObj.url.value.isNotEmpty && !imageObj.isRetrying.value) {
        controller.asyncGetImageUrl(imageObj);
      }

      // 如果没有有效的URL，显示错误占位符
      if (displayUrl.isEmpty) {
        return buildErrorWidget(size: 100, backgroundColor: AppTheme.white85Color);
      }

      // 使用原始URL作为稳定的标识符，避免因turl变化导致的重建
      final String stableKey = imageObj.url.value;

      // 构建优化的CachedNetworkImage
      return CachedNetworkImage(
        // 使用原始URL作为稳定的key，避免因turl变化导致的重建
        key: Key(stableKey),
        // 使用原始URL作为缓存键，确保相同图片使用同一缓存
        cacheKey: stableKey,
        // 使用实际的显示URL进行加载
        imageUrl: displayUrl,
        fit: BoxFit.contain,
        // 启用旧图片保持，在URL变化时保持显示
        useOldImageOnUrlChange: true,
        // 占位符
        placeholder: (_, __) => buildCupertinoLoadingWidget(size: 100),
        // 优化的错误处理：通过控制器处理重试逻辑
        errorWidget: (context, url, error) {
          LogUtil.w('图片预览页面图片加载失败，URL: $url, 错误: $error');

          // 通过控制器处理重试逻辑，符合Clean Architecture原则
          final Widget errorWidget = controller.handleImageLoadError(imageObj, 100);
          return errorWidget;
        },
        // 错误监听器
        errorListener: (exception) {
          LogUtil.e('图片预览页面CachedNetworkImage错误监听器触发: $exception');
        },
      );
    });
  }

  /// 构建Cupertino风格的加载指示器组件
  ///
  /// [size] 容器大小
  /// [backgroundColor] 背景颜色，默认为灰色
  ///
  /// 返回一个包含CupertinoActivityIndicator的容器组件
  Widget buildCupertinoLoadingWidget({required double size, Color? backgroundColor}) {
    return Container(
      color: backgroundColor ?? Colors.grey[200],
      child: const Center(child: CupertinoActivityIndicator()),
    );
  }

  /// 构建错误提示组件
  ///
  /// 当图片加载失败时显示的错误提示组件
  ///
  /// 参数:
  /// - [size] - 容器大小,必填参数
  /// - [backgroundColor] - 背景颜色,可选参数,默认为灰色[200]
  /// - [iconSize] - 错误图标大小,可选参数,默认为40
  ///
  /// 返回:
  /// - [Container] - 包含错误图标的容器组件
  ///
  /// 示例:
  /// ```dart
  /// buildErrorWidget(
  ///   size: 100,
  ///   backgroundColor: Colors.white,
  ///   iconSize: 30
  /// )
  /// ```
  Widget buildErrorWidget({required double size, Color? backgroundColor, double iconSize = 40}) {
    return Container(
      color: backgroundColor ?? Colors.grey[200],
      child: Icon(Icons.error_outline, size: iconSize, color: AppTheme.grayColor),
    );
  }

  /// 构建底部工具栏
  ///
  /// 用于显示图片操作按钮，包括删除和设为主图像
  Widget _buildBottomBar() {
    return Container(
      color: AppTheme.whiteColor,
      padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 16),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () {
                controller.deleteImageWithIndex();
              },
              style: TextButton.styleFrom(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                  side: const BorderSide(color: AppTheme.delIconColor, width: 1),
                ),
              ),
              child: const Text('削除', style: TextStyle(color: Color(0xFFE6004D), fontSize: 16)),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: Obx(() {
              return _buildSetHomeImageBtn();
            }),
          ),
        ],
      ),
    );
  }

  /// 构建设为主图像按钮
  Widget _buildSetHomeImageBtn() {
    if (!controller.showSetHomeImageBtn.value) {
      return const SizedBox.shrink();
    }

    return TextButton(
      onPressed: () {
        controller.setHomeImageWithIndex();
      },
      style: TextButton.styleFrom(
        backgroundColor: AppTheme.footerBlueBtnColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),
      child: Obx(
        () => Text(
          controller.isCurrentHomeImage.value ? 'メイン画像設定を解除' : 'メイン画像に設定',
          style: const TextStyle(color: AppTheme.whiteColor, fontSize: 16),
        ),
      ),
    );
  }
}

class ImagePreviewPageArguments {
  final String pageTitle;
  final dynamic index; // 改为dynamic以支持不同的数据类型
  final List<RxImageModel> imageModels;
  final bool isShowSetHomeImageBtn;

  ImagePreviewPageArguments({
    required this.pageTitle,
    required this.index,
    required this.imageModels,
    required this.isShowSetHomeImageBtn,
  });

  /// 安全地获取索引值，支持String和int类型
  /// 如果是String类型，尝试解析为int
  /// 如果解析失败，返回0
  int get safeIndex {
    try {
      if (index is int) {
        return index as int;
      } else if (index is String) {
        final stringIndex = index as String;
        final parsedIndex = int.tryParse(stringIndex);
        if (parsedIndex != null) {
          return parsedIndex;
        }
        // 如果字符串不能解析为数字，记录错误并返回0
        debugPrint('Warning: Cannot parse index "$stringIndex" to int, using 0 instead');
        return 0;
      } else if (index is num) {
        // 处理其他数字类型（double等）
        return (index as num).round();
      } else {
        // 处理其他类型
        debugPrint('Warning: Unexpected index type ${index.runtimeType}, using 0 instead');
        return 0;
      }
    } catch (e) {
      debugPrint('Error in safeIndex getter: $e, using 0 instead');
      return 0;
    }
  }
}
