import 'package:asset_force_mobile_v2/features/list_selector/domain/models/list_selector_params.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_mixin.dart';
import 'package:get/get.dart';

/// 列表选择器控制器
///
/// 负责管理列表选择器页面的状态和业务逻辑
class ListSelectorController extends GetxController with OverlayMixin {
  /// 页面标题
  late final String title;

  /// 所有列表项
  final RxList<String> items = <String>[].obs;

  /// 过滤后的列表项
  final RxList<String> filteredItems = <String>[].obs;

  /// 选中项的索引
  final RxInt selectedIndex = (-1).obs;

  /// 原始列表项（用于重置过滤）
  List<String> _originalItems = [];

  @override
  void onInit() {
    super.onInit();
    _loadItems();
  }

  /// 加载列表项数据
  void _loadItems() {
    final args = Get.arguments;
    if (args is ListSelectorParams) {
      title = args.title;
      _originalItems = args.items;
      items.value = _originalItems;
      filteredItems.value = _originalItems;

      // 设置初始选中项
      final initialSelectedItem = args.selectedItem;
      if (initialSelectedItem != null && initialSelectedItem.isNotEmpty) {
        selectedIndex.value = items.indexOf(initialSelectedItem);
      }
    }
  }

  /// 过滤列表项
  void filterItems(String query) {
    if (query.isEmpty) {
      filteredItems.value = _originalItems;
      return;
    }

    final lowercaseQuery = query.toLowerCase();
    filteredItems.value = _originalItems.where((item) => item.toLowerCase().contains(lowercaseQuery)).toList();
  }

  /// 选择项目
  void selectItem(int index) {
    if (index < 0 || index >= filteredItems.length) return;

    selectedIndex.value = index;
    Get.back(result: filteredItems[index]);
  }

  /// 清除选择
  void onClearBtnClick() {
    Get.back(result: '');
  }
}
