import 'package:asset_force_mobile_v2/features/list_selector/presentation/bindings/list_selector_bindings.dart';
import 'package:asset_force_mobile_v2/features/list_selector/presentation/controllers/list_selector_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/selector_page_scaffold.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/selectable_list_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

/// 列表选择器页面
///
/// 用于显示一个可选择的列表页面,用户可以从中选择一个项目
///
/// 参数:
/// - [title] 页面标题
/// - [selectedItem] 当前选中的项目(可选)
/// - [items] 要显示的列表项
/// - [onSelected] 选择项目时的回调函数
@GetRoutePage('/list_selector', bindings: [ListSelectorBindings])
class ListSelectorPage extends GetView<ListSelectorController> {
  const ListSelectorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SelectorPageScaffold(
      title: controller.title,
      onClearBtnClick: controller.onClearBtnClick,
      onSearchChanged: controller.filterItems,
      body: Obx(
        () => SelectableListView(
          items: controller.filteredItems,
          selectedIndex: controller.selectedIndex.value,
          onItemTap: controller.selectItem,
          getDisplayText: (String item) => item,
        ),
      ),
    );
  }
}
