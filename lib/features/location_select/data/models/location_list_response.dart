import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'location_list_response.g.dart'; // 自动生成的文件名

@JsonSerializable()
class LocationListResponse extends BaseResponse {
  @JsonKey(name: 'locationList')
  List<String>? locationList;

  LocationListResponse({this.locationList, required super.code, required super.msg});

  factory LocationListResponse.fromJson(Map<String, dynamic> json) => _$LocationListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LocationListResponseToJson(this);
}
