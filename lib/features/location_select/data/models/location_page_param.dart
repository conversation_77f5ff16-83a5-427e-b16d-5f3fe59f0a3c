class LocationPageParam {
  /// 是否从用户设置页面过来，
  /// true: 从用户设置页面过来， 此时不需要保存设置的值
  /// false: 从资产一览页面过来， 此时需要保存设置的值
  final bool isFromUserSettings;

  /// 用户选择的地点
  final String location;

  LocationPageParam({required this.isFromUserSettings, required this.location});

  @override
  String toString() {
    return 'LocationPageParam{isFromUserSettings: $isFromUserSettings, location: $location}';
  }
}
