import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/controllers/login_add_new_sms_controller.dart';
import 'package:get/get.dart';

class LoginAddNewSmsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<LoginAddNewSmsController>(
      () => LoginAddNewSmsController(
        navigationService: Get.find<NavigationService>(),
        dialogService: Get.find<DialogService>(),
      ),
    );
  }
}
