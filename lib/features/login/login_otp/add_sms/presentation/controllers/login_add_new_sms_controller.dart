import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/utils/ipn_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/state/login_add_new_sms_state.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class LoginAddNewSmsController extends BaseController {
  final TextEditingController textEditingController = TextEditingController(); // 内部创建
  final IpnService _ipnSerView = IpnService();
  final NavigationService navigationService;
  final DialogService dialogService;
  final LoginAddNewSmsState state = LoginAddNewSmsState();

  LoginAddNewSmsController({required this.navigationService, required this.dialogService});

  @override
  void onReady() async {
    super.onReady();
    final param = Get.arguments;
    await showLoading();
    if (param is RePhoneAndIpnCodeModel) {
      final String callingCode = _ipnSerView.loadDefaultJPData.callingCode;
      final String phoneNum = param.phoneNum;
      state.nationCode.value = callingCode;
      if (callingCode.isEmpty || phoneNum.isEmpty) {
        state.inputPhoneNumber.value = '';
      } else {
        state.inputPhoneNumber.value = phoneNum;
      }
      textEditingController.text = state.inputPhoneNumber.value;
    }
    hideLoading();
  }

  /// 获取国旗图片（由于法律原因，只可以显示日本国旗，其他国家国旗不可以选择不可以显示）
  getIpnImage() => _ipnSerView.getImage();

  /// 用户输入的内容
  void onChangePhoneInput(String search) {
    _updateTextController(search);
  }

  /// 清空输入内容
  clearInput() {
    textEditingController.text = '';
    state.inputPhoneNumber.value = '';
  }

  /// 保留光标位置
  void _updateTextController(String text) {
    textEditingController.value = textEditingController.value.copyWith(
      text: text,
      selection: TextSelection.collapsed(offset: text.length),
    );
    state.inputPhoneNumber.value = text;
  }

  /// 隐藏键盘
  void hideKeyboard() {
    FocusScope.of(Get.context!).requestFocus(FocusNode()); // 让焦点离开当前输入框
    SystemChannels.textInput.invokeMethod('TextInput.hide'); // 隐藏键盘
  }

  /// 确定按钮
  sureBtnOnClick() async {
    final String nCode = state.nationCode.value;
    final String phoneNumber = state.inputPhoneNumber.value;
    try {
      _ipnSerView.judgePhoneNumber(phoneNum: phoneNumber, nCode: nCode);
    } catch (e, stackTrace) {
      LogUtil.e('出现错误 ===》 ', error: e, stackTrace: stackTrace);
      if (e is BusinessException) {
        await dialogService.show(content: e.message, type: DialogType.error);
        return;
      }
      await handleException(SystemException());
      return;
    }
    navigationService.goBack(
      result: RePhoneAndIpnCodeModel(phoneNum: phoneNumber, nationCode: nCode),
    );
  }

  /// 返回按钮
  void goBackPage() {
    navigationService.goBack();
  }

  @override
  void onClose() {
    textEditingController.dispose(); // 释放资源
    super.onClose();
  }
}

class RePhoneAndIpnCodeModel {
  final String phoneNum;
  final String nationCode;
  RePhoneAndIpnCodeModel({required this.phoneNum, required this.nationCode});
}
