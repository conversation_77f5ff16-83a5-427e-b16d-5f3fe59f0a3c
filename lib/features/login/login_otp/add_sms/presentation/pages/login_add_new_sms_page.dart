import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/bindings/login_add_new_sms_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/add_sms/presentation/controllers/login_add_new_sms_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/login/opt/add_new_sms', binding: LoginAddNewSmsBinding)
class LoginAddNewSMSPage extends GetView<LoginAddNewSmsController> {
  const LoginAddNewSMSPage({super.key});

  @override
  Widget build(BuildContext context) {
    final double bottomSafeAreaHeight = MediaQuery.of(context).padding.bottom + 10;
    return GestureDetector(
      onTap: () => controller.hideKeyboard(),
      child: Scaffold(
        appBar: AppBar(
          leading: Icon<PERSON>utton(
            icon: const Icon(Icons.chevron_left, color: Colors.white, size: 35),
            onPressed: () => controller.goBackPage(),
          ),
          title: const Text('携帯電話番号', style: TextStyle(fontSize: 18)),
        ),
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(15.0),
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, bottom: 10),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      height: 45,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          const Text(
                            '国番号',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w300,
                              color: Colors.black54, // 设置字体颜色为白色
                            ),
                          ),
                          const SizedBox(width: 28),
                          Obx(() {
                            final String getIpnCallingCode = controller.state.nationCode.value;
                            if (getIpnCallingCode.isEmpty) {
                              return const SizedBox.shrink();
                            }
                            final String getIpnImage = controller.getIpnImage();
                            return Row(
                              children: [
                                Container(
                                  width: 40, // 设置固定宽度
                                  height: 30, // 设置固定高度
                                  child: Image.asset(
                                    getIpnImage, // 替换为你的图片路径
                                    fit: BoxFit.cover, // 图片铺满容器，裁剪图片，确保没有留边
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Text(
                                  '+$getIpnCallingCode',
                                  style: const TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w300,
                                    color: Colors.black, // 设置字体颜色为白色
                                  ),
                                ),
                              ],
                            );
                          }),
                        ],
                      ),
                    ),
                    const SizedBox(
                      width: double.infinity, // 竖线宽度
                      height: 1,
                      child: DecoratedBox(
                        decoration: BoxDecoration(
                          color: Colors.black, // 竖线的颜色
                        ),
                      ),
                    ),
                    Row(
                      children: [
                        const Text(
                          '携帯電話番号',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w300,
                            color: Colors.black54, // 设置字体颜色为白色
                          ),
                        ),
                        const SizedBox(width: 28),
                        Expanded(
                          child: TextField(
                            style: const TextStyle(
                              color: AppTheme.darkShadesGrayColor,
                              fontSize: 15,
                              fontWeight: FontWeight.w100,
                            ),
                            onChanged: (phone) => controller.onChangePhoneInput(phone),
                            textInputAction: TextInputAction.next,
                            keyboardType: TextInputType.number,
                            controller: controller.textEditingController,
                            decoration: const InputDecoration(
                              hintText: '未設定',
                              hintStyle: TextStyle(color: Colors.black54, fontSize: 15),
                              border: InputBorder.none,
                            ),
                          ),
                        ),
                        // 清除按钮，只有在输入框有内容时才显示
                        Obx(() {
                          if (controller.state.inputPhoneNumber.value.isNotEmpty) {
                            final double minSize = 15.0;
                            return IconButton(
                              constraints: BoxConstraints(minHeight: minSize, minWidth: minSize),
                              icon: const Icon(Icons.cancel, color: Colors.black45),
                              iconSize: minSize,
                              padding: const EdgeInsets.only(top: 1),
                              onPressed: () => controller.clearInput(),
                            );
                          }
                          return const SizedBox.shrink();
                        }),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        bottomNavigationBar: Container(
          decoration: const BoxDecoration(color: Colors.white),
          padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: bottomSafeAreaHeight),
          child: OverflowBar(
            alignment: MainAxisAlignment.spaceBetween,
            children: [
              FractionallySizedBox(
                widthFactor: 0.35,
                child: ElevatedButton(
                  onPressed: () => controller.goBackPage(),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    backgroundColor: Colors.white,
                    side: BorderSide(color: AppTheme.lightTheme.primaryColor),
                  ),
                  child: Text('キャンセル', style: TextStyle(color: AppTheme.lightTheme.primaryColor)),
                ),
              ),
              FractionallySizedBox(
                widthFactor: 0.35,
                child: ElevatedButton(
                  onPressed: () => controller.sureBtnOnClick(),
                  child: const Text('確定', style: TextStyle(color: Colors.white)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
