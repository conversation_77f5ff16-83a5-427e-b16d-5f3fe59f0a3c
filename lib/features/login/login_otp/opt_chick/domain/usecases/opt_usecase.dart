import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/presentation/controllers/login_opt_controller.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';

class OptUsecase extends UserInfoHandleBaseUseCase {
  OptUsecase({
    required super.tenantRepository,
    required super.userRepository,
    required super.storageUtils,
    super.envHelper,
  });

  @override
  Future<GetTokenResultModel> getBasicInfo(SelectTenantModel params) async {
    final OptTenantModel? optTenant = params.optTenant;
    final String? ticket = optTenant?.ticket;
    if (ticket == null || ticket.isEmpty) {
      throw SystemException();
    }
    final String? tel = optTenant?.tel;
    if (tel == null || tel.isEmpty) {
      throw SystemException();
    }
    final String? nationCode = optTenant?.nationCode;
    if (nationCode == null || nationCode.isEmpty) {
      throw SystemException();
    }
    final int? reCode = optTenant?.reCode;
    if (reCode == null) {
      throw SystemException();
    }
    final String? tenantId = optTenant?.tenantId;
    if (tenantId == null || tenantId.isEmpty) {
      throw SystemException();
    }
    final int? userId = optTenant?.userId;
    if (userId == null) {
      throw SystemException();
    }
    final String? userName = optTenant?.userName;
    if (userName == null || userName.isEmpty) {
      throw SystemException();
    }
    final String? password = optTenant?.password;
    if (password == null || password.isEmpty) {
      throw SystemException();
    }
    final String? code = optTenant?.code;
    if (code == null || code.isEmpty) {
      throw SystemException();
    }
    // 普通登录之后tenant选择后
    final GetTokenResultModel result = await tenantRepository.getUserBindPhone(
      ticket: ticket,
      countryCode: nationCode,
      phone: tel,
      userName: userName,
      password: password,
      code: code,
      tenantId: tenantId,
      userId: userId,
    );
    return result;
  }

  sendSMSCall({required SelectTenantModel params, required ShowOptWidgetEnum? showOptWidget}) async {
    final OptTenantModel? optTenant = params.optTenant;
    final String? ticket = optTenant?.ticket;
    if (ticket == null || ticket.isEmpty) {
      throw SystemException();
    }
    GetTokenResultModel result;
    if (showOptWidget == ShowOptWidgetEnum.email) {
      final String? email = optTenant?.email;
      if (email == null || email.isEmpty) {
        throw SystemException();
      }
      // 邮箱发送验证码
      result = await tenantRepository.getSendMfaMailVerifyCode(ticket: ticket, email: email);
    } else {
      final String? tel = optTenant?.tel;
      if (tel == null || tel.isEmpty) {
        throw SystemException();
      }
      final String? nationCode = optTenant?.nationCode;
      if (nationCode == null || nationCode.isEmpty) {
        throw SystemException();
      }
      // 手机发送验证码
      result = await tenantRepository.getVerificationCode(ticket: ticket, countryCode: nationCode, phone: tel);
    }
    if (result.code != 0) {
      String? signInResult = result.signInResult;
      if (signInResult == null || signInResult.isEmpty) {
        signInResult = '認証コード送信失敗しました。';
      }
      throw BusinessException(signInResult);
    }
  }
}
