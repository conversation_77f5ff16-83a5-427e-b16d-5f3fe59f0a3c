import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/presentation/bindings/login_opt_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_otp/opt_chick/presentation/controllers/login_opt_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/login/opt', binding: LoginOptBinding)
class LoginOptPage extends GetView<LoginOptController> {
  const LoginOptPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.chevron_left, color: Colors.white, size: 35),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text('2段階認証', style: TextStyle(fontSize: 18)),
      ),
      body: GestureDetector(
        onTap: () {
          // 点击其他区域时关闭键盘
          FocusScope.of(context).unfocus();
        },
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Obx(() {
                final showOptWidget = controller.state.showOptWidget.value;
                if (showOptWidget == ShowOptWidgetEnum.sms) {
                  return _sendSMS();
                }
                if (showOptWidget == ShowOptWidgetEnum.email) {
                  return _sendEmail();
                }
                if (showOptWidget == ShowOptWidgetEnum.newSms) {
                  return _newAddSMS();
                }
                return const SizedBox.shrink();
              }),
            ),
          ),
        ),
      ),
    );
  }

  /// 新绑定手机号
  Widget _newAddSMS() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(height: 30),
        const Text(
          '2段階認証を行います。',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: Colors.white, // 设置字体颜色为白色
          ),
        ),
        const Text(
          '電話番号をご入力の上、「認証コードを送信」ボタンをタップしてください。',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: Colors.white, // 设置字体颜色为白色
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 30),
        Container(
          padding: const EdgeInsets.all(38.0),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Obx(() {
            String showNumber;
            final String nationCode = controller.state.nationCode.value;

            final bool isPhoneNumberEmpty = controller.state.showPhoneNumber.value.isEmpty;
            Color buttonColor;
            if (isPhoneNumberEmpty) {
              showNumber = '';
              buttonColor = AppTheme.lightTheme.primaryColor.withValues(alpha: 0.5);
            } else {
              showNumber = controller.hiddenPhoneNumber(phoneNo: controller.state.showPhoneNumber.value);
              buttonColor = AppTheme.lightTheme.primaryColor;
            }

            return Column(
              children: [
                ElevatedButton(
                  onPressed: () => controller.settingCellPhoneNumberOnClick(),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        '電話番号',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w300,
                          color: Colors.black54, // 设置字体颜色为白色
                        ),
                      ),
                      isPhoneNumberEmpty
                          ? const Text(
                              '電話番号を入力',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w300,
                                color: Colors.black38, // 设置字体颜色为白色
                              ),
                            )
                          : Row(
                              children: [
                                Text(
                                  '+$nationCode',
                                  style: const TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w300,
                                    color: Colors.black, // 设置字体颜色为白色
                                  ),
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  showNumber,
                                  style: const TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w300,
                                    color: Colors.black, // 设置字体颜色为白色
                                  ),
                                ),
                              ],
                            ),
                      const Icon(Icons.chevron_right, color: Colors.black, size: 25),
                    ],
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.zero,
                    backgroundColor: Colors.transparent,
                    side: BorderSide.none,
                    shadowColor: Colors.transparent,
                    shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
                    splashFactory: NoSplash.splashFactory,
                    overlayColor: Colors.transparent,
                  ),
                ),
                const SizedBox(
                  width: double.infinity, // 竖线宽度
                  height: 1,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      color: Colors.black, // 竖线的颜色
                    ),
                  ),
                ),
                const SizedBox(height: 25),
                ElevatedButton(
                  onPressed: () => controller.addNewSMS(),
                  child: Text(
                    '認証コードを送信',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: buttonColor, // 设置字体颜色为白色
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.zero,
                    backgroundColor: Colors.transparent,
                    side: BorderSide(color: buttonColor, width: 1),
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                  ),
                ),
              ],
            );
          }),
        ),
      ],
    );
  }

  /// 发送短信
  Widget _sendSMS() {
    return Obx(() {
      if (controller.state.autoStart.value) {
        // 使用addPostFrameCallback确保在build完成后启动
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.state.autoStart.value = false;
          controller.sendSMS();
        });
      }
      final String nationCode = controller.state.nationCode.value;
      final String phoneNumber = controller.hiddenPhoneNumber(phoneNo: controller.state.showPhoneNumber.value);
      final buttonColor = AppTheme.lightTheme.primaryColor;
      final isCountingDown = controller.state.isCountingDown.value;
      final bool isUserUnCode = controller.state.userUnCode.value == 4;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 30),
          const Text(
            '2段階認証用の認証コードを',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.white, // 设置字体颜色为白色
            ),
          ),
          Text(
            '「+$nationCode $phoneNumber」に送信しました。',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.white, // 设置字体颜色为白色
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          Container(
            padding: const EdgeInsets.all(38.0),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Column(
              children: [
                TextField(
                  textAlign: TextAlign.center,
                  keyboardType: TextInputType.number,
                  autofillHints: [AutofillHints.postalCode],
                  // 告诉输入类型
                  style: const TextStyle(color: Colors.black, fontSize: 25),
                  textInputAction: TextInputAction.send,
                  // 设置回车为“下一个”
                  onEditingComplete: () => controller.login(),
                  controller: controller.controllerNCode,
                  decoration: InputDecoration(
                    hintText: '認証コードを入力',
                    filled: true,
                    fillColor: Colors.white70,
                    border: _inputBorderStyle(),
                    focusedBorder: _inputBorderStyle(),
                    enabledBorder: _inputBorderStyle(),
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(vertical: 5, horizontal: 13),
                  ),
                ),
                const SizedBox(height: 15),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () => controller.login(),
                    style: OutlinedButton.styleFrom(
                      fixedSize: const Size(double.infinity, 45),
                      backgroundColor: AppTheme.darkBlueColor,
                      side: const BorderSide(color: Colors.white, width: 1.5),
                      // 边框颜色和宽度
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      // 圆角
                      padding: EdgeInsets.zero,
                    ),
                    child: const Text(
                      'ログイン',
                      style: TextStyle(fontSize: 16, color: Colors.white, fontWeight: FontWeight.w800),
                    ),
                  ),
                ),
                const SizedBox(height: 30),
                ElevatedButton(
                  onPressed: () => isUserUnCode ? () => {} : controller.settingCellPhoneNumberOnClick(),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        '電話番号',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w300,
                          color: Colors.black54, // 设置字体颜色为白色
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            '+$nationCode',
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w300,
                              color: Colors.black, // 设置字体颜色为白色
                            ),
                          ),
                          const SizedBox(width: 5),
                          Text(
                            phoneNumber,
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w300,
                              color: Colors.black, // 设置字体颜色为白色
                            ),
                          ),
                        ],
                      ),
                      Icon(
                        isUserUnCode ? Icons.lock_outlined : Icons.chevron_right,
                        color: Colors.black,
                        size: isUserUnCode ? 15 : 25,
                      ),
                    ],
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.zero,
                    backgroundColor: Colors.transparent,
                    side: BorderSide.none,
                    shadowColor: Colors.transparent,
                    shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
                    splashFactory: NoSplash.splashFactory,
                    overlayColor: Colors.transparent,
                  ),
                ),
                const SizedBox(
                  width: double.infinity, // 竖线宽度
                  height: 1,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      color: Colors.black, // 竖线的颜色
                    ),
                  ),
                ),
                const SizedBox(height: 25),
                _sendSMSButton(buttonColor: buttonColor, isCountingDown: isCountingDown),
              ],
            ),
          ),
        ],
      );
    });
  }

  /// 发送邮件
  Widget _sendEmail() {
    return Obx(() {
      if (controller.state.autoStart.value) {
        // 使用addPostFrameCallback确保在build完成后启动
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.state.autoStart.value = false;
          controller.sendEmail();
        });
      }
      final String userEmail = controller.state.userEmail.value;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 30),
          const Text(
            '2段階認証用の認証コードを',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.white, // 设置字体颜色为白色
            ),
          ),
          Text(
            '「$userEmail」に送信しました。',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.white, // 设置字体颜色为白色
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          Container(
            padding: const EdgeInsets.all(38.0),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Column(
              children: [
                TextField(
                  textAlign: TextAlign.center,
                  keyboardType: TextInputType.number,
                  autofillHints: [AutofillHints.postalCode],
                  // 告诉输入类型
                  style: const TextStyle(color: Colors.black, fontSize: 25),
                  textInputAction: TextInputAction.send,
                  // 设置回车为“下一个”
                  onEditingComplete: () => controller.login(),
                  controller: controller.controllerNCode,
                  decoration: InputDecoration(
                    hintText: '認証コードを入力',
                    filled: true,
                    fillColor: Colors.white70,
                    border: _inputBorderStyle(),
                    focusedBorder: _inputBorderStyle(),
                    enabledBorder: _inputBorderStyle(),
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(vertical: 5, horizontal: 13),
                  ),
                ),
                const SizedBox(height: 15),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () => controller.login(),
                    style: OutlinedButton.styleFrom(
                      fixedSize: const Size(double.infinity, 45),
                      backgroundColor: AppTheme.darkBlueColor,
                      side: const BorderSide(color: Colors.white, width: 1.5),
                      // 边框颜色和宽度
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      // 圆角
                      padding: EdgeInsets.zero,
                    ),
                    child: const Text(
                      'ログイン',
                      style: TextStyle(fontSize: 16, color: Colors.white, fontWeight: FontWeight.w800),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  /// 提取输入框的边框样式
  InputBorder _inputBorderStyle() {
    return OutlineInputBorder(borderRadius: BorderRadius.circular(8.0), borderSide: BorderSide.none);
  }

  /// 发送短信按钮样式
  Widget _sendSMSButton({required Color buttonColor, required bool isCountingDown}) {
    final Color defaultButtonColor = isCountingDown ? buttonColor.withValues(alpha: 0.5) : buttonColor;
    final String defaultTitle = isCountingDown
        ? '%s秒後に再送信できます'.replaceAll('%s', controller.state.countdownSeconds.value.toString())
        : '再送信する';

    return ElevatedButton(
      onPressed: isCountingDown ? () => {} : controller.sendSMS,
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.zero,
        backgroundColor: Colors.transparent,
        disabledBackgroundColor: Colors.transparent,
        side: BorderSide(color: defaultButtonColor, width: 1),
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        splashFactory: isCountingDown ? NoSplash.splashFactory : InkSplash.splashFactory,
        overlayColor: isCountingDown ? Colors.transparent : defaultButtonColor,
      ),
      child: Text(
        defaultTitle,
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: defaultButtonColor),
      ),
    );
  }
}
