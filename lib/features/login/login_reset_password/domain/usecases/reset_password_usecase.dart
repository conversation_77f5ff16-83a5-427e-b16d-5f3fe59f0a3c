import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/data/models/reset_password_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/domain/repositories/reset_password_repository.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/password_policy_model.dart';

/// 密码规则model
class PasswordVerificationRulesModel {
  String regexText;
  List<String> mustContainArray;
  bool signFlg;
  int passwordLength;
  PasswordVerificationRulesModel({
    required this.regexText,
    required this.mustContainArray,
    required this.signFlg,
    required this.passwordLength,
  });
}

/// 本地校验结果model
class PasswordVerificationResult {
  String newPasswordTip;
  bool hasNewPasswordError;
  PasswordVerificationResult({required this.newPasswordTip, required this.hasNewPasswordError});
}

/// 本地二次校验结果model
class ConfirmPasswordVerificationResult {
  String passwordConfirmTip;
  bool hasConfirmPasswordError;
  ConfirmPasswordVerificationResult({required this.passwordConfirmTip, required this.hasConfirmPasswordError});
}

class ResetPasswordArgumentsModel {
  /// 是否开启生物认证
  bool isBiometrics;

  /// 区域
  String zoneId;

  /// 旧密码
  String oldPassword;

  /// 新密码
  String? newPassword;

  /// 用户邮箱
  String userName;

  /// 组
  String tenantId;

  /// 组名
  String tenantName;

  /// 后台传来的密码校验规则
  PasswordPolicyModel policy;

  ResetPasswordArgumentsModel({
    required this.isBiometrics,
    required this.zoneId,
    required this.oldPassword,
    this.newPassword,
    required this.userName,
    required this.tenantId,
    required this.tenantName,
    required this.policy,
  });
}

class ResetPasswordUsecase implements UseCase<ResetPasswordResultModel, ResetPasswordArgumentsModel> {
  final ResetPasswordRepository resetPasswordRepository;

  ResetPasswordUsecase({required this.resetPasswordRepository});

  @override
  Future<ResetPasswordResultModel> call(ResetPasswordArgumentsModel params) async {
    final String userName = params.userName;
    final String oldPassword = params.oldPassword;
    final String? newPassword = params.newPassword;
    if (newPassword == null || newPassword.isEmpty) {
      throw SystemException();
    }
    final String tenantId = params.tenantId;
    final String zoneId = params.zoneId;
    final result = await resetPasswordRepository.setUpdatePassword(
      userName: userName,
      oldPassword: oldPassword,
      newPassword: newPassword,
      tenantId: tenantId,
      zoneId: zoneId,
    );
    return result;
  }

  /// 初始化密码规则
  PasswordVerificationRulesModel initPasswordRules({required String mustContain, required int passwordLength}) {
    // 所有密码规则定义，与后台相互对应
    final passwordRegex = [
      {'key': 'upper', 'value': '英大文字', 'display': '英大文字を1文字以上含む(A-Z)', 'regex': '(?=.*[A-Z])'},
      {'key': 'lower', 'value': '英小文字', 'display': '英小文字を1文字以上含む(a-z)', 'regex': '(?=.*[a-z])'},
      {'key': 'number', 'value': '数字', 'display': '数字を1文字以上含む(0-9)', 'regex': '(?=.*[0-9])'},
      {
        'key': 'sign',
        'value': '記号',
        'display': '记号を1文字以上含む（^\$*.[]{}()?-"!@#%&/,<>\':;|_~`）',
        'regex': '^\$*.[]{}()?-"!@#%&/\\,><\':;|_~`',
      },
    ];
    // 生成规则显示文本和正则表达式
    String regexText = '^';
    final List<String> mustContainArray = [];
    bool signFlg = false;

    for (var item in passwordRegex) {
      if (item['key'] != 'sign' && mustContain.contains(item['key']!)) {
        mustContainArray.add(item['display']!);
        regexText += item['regex']!;
      } else if (item['key'] == 'sign' && mustContain.contains(item['key']!)) {
        mustContainArray.add(item['display']!);
        signFlg = true;
      }
    }
    regexText += '{${passwordLength},}';
    return PasswordVerificationRulesModel(
      regexText: regexText,
      mustContainArray: mustContainArray,
      signFlg: signFlg,
      passwordLength: passwordLength,
    );
  }

  /// 密码合法性检查
  PasswordVerificationResult passwordLegitimacyCheck({
    required PasswordVerificationRulesModel pvr,
    required String password,
  }) {
    // 空格检查
    final blankSpaceCheck = RegExp(r'\s').hasMatch(password);

    // 根据需要输入的字符类型生成正则表达式
    RegExp? passwordRegExp;
    if (pvr.regexText != '{${pvr.passwordLength},}') {
      passwordRegExp = RegExp(pvr.regexText);
    }

    bool hasNewPasswordError = false;
    String newPasswordTip = '';
    if (password.isEmpty) {
      // 密码为空
      hasNewPasswordError = true;
      newPasswordTip = '新しいパスワードは必須項目です';
    } else if (blankSpaceCheck ||
        password.length < pvr.passwordLength ||
        (passwordRegExp != null && !passwordRegExp.hasMatch(password))) {
      // 密码不符合规则
      hasNewPasswordError = true;
      newPasswordTip = '以下のパスワード条件を満たすパスワードを設定してください';
    } else if (pvr.signFlg) {
      // 特殊字符检查
      const specialSymbols = '^\$*.[]{}()?-"!@#%&/\\,><\':;|_~`';
      final specialCheck = password.split('').any((char) => specialSymbols.contains(char));

      if (!specialCheck) {
        hasNewPasswordError = true;
        newPasswordTip = '以下のパスワード条件を満たすパスワードを設定してください';
      } else {
        hasNewPasswordError = false;
        newPasswordTip = '';
      }
    } else {
      hasNewPasswordError = false;
      newPasswordTip = '';
    }

    return PasswordVerificationResult(newPasswordTip: newPasswordTip, hasNewPasswordError: hasNewPasswordError);
  }

  /// 密码一致性检查
  ConfirmPasswordVerificationResult passwordConsistenceCheck({
    required String confirmPassword,
    required String newPassword,
  }) {
    bool hasConfirmPasswordError = false;
    String passwordConfirmTip = '';
    if (confirmPassword.isEmpty) {
      // 确认密码为空
      hasConfirmPasswordError = true;
      passwordConfirmTip = '新しいパスワード（確認用）は必須項目です';
    } else if (confirmPassword != newPassword) {
      // 两次输入的密码不一致
      hasConfirmPasswordError = true;
      passwordConfirmTip = '入力したパスワードが一致しません';
    } else {
      hasConfirmPasswordError = false;
      passwordConfirmTip = '';
    }
    return ConfirmPasswordVerificationResult(
      passwordConfirmTip: passwordConfirmTip,
      hasConfirmPasswordError: hasConfirmPasswordError,
    );
  }
}
