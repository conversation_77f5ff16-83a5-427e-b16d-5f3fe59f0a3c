import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/bindings/login_reset_password_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/controllers/login_reset_password_controllers.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/pages/reset_password_bottom_widget.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/pages/reset_password_view_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

/// 密码重置页面
/// 用于用户重新设置密码的界面
@GetRoutePage('/login/reset_password', binding: LoginResetPasswordBinding)
class LoginResetPasswordPage extends GetView<LoginResetPasswordControllers> {
  const LoginResetPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 顶部应用栏
      appBar: AppBar(
        backgroundColor: AppTheme.darkBlueColor,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.chevron_left, color: Colors.white, size: 30),
          onPressed: () => controller.onCancel(),
        ),
        title: const Text(
          'パスワード再設定',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white),
        ),
      ),
      body: const SingleChildScrollView(child: ResetPasswordViewWidget()),
      bottomNavigationBar: const ResetPasswordBottomWidget(),
    );
  }
}
