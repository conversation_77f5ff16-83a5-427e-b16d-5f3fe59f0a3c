import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/controllers/login_reset_password_controllers.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

/// 按钮高度
const double buttonHeight = 38;

/// 按钮之间的上下间距
const double buttonsTopAndBottomBetween = 5;

/// 按钮距离边缘间距
const double distanceEdgeSize = 16;

/// 重置密码底部区域View
class ResetPasswordBottomWidget extends GetView<LoginResetPasswordControllers> {
  const ResetPasswordBottomWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // 修复按钮宽度计算，确保两个按钮不会溢出
    // 使用更保守的计算：屏幕宽度的40%，最大不超过160px
    final double screenWidth = MediaQuery.of(context).size.width;
    final double buttonWidth = (screenWidth * 0.5).clamp(120.0, 160.0);

    return Container(
      color: AppTheme.whiteColor,
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Obx(() {
              final isReturning = controller.state.isReturning.value;
              Widget buttonChild = const Text('キャンセル', style: TextStyle(fontWeight: FontWeight.w700));
              if (isReturning) {
                buttonChild = const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.darkBlueColor),
                  ),
                );
              }
              return _buildButton(
                onPressed: () => controller.onCancel(),
                buttonWidth: buttonWidth,
                childW: buttonChild,
                bgColor: AppTheme.transparentColor,
                textColor: AppTheme.darkBlueColor,
              );
            }),
            Obx(() {
              final isUpdating = controller.state.isUpdating.value;
              Widget buttonChild = const Text('設定', style: TextStyle(fontWeight: FontWeight.w700));
              if (isUpdating) {
                buttonChild = const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.whiteColor),
                  ),
                );
              }
              return _buildButton(
                onPressed: () => controller.updatePassword(),
                buttonWidth: buttonWidth,
                childW: buttonChild,
                bgColor: AppTheme.darkBlueColor,
                textColor: AppTheme.whiteColor,
              );
            }),
          ],
        ),
      ),
    );
  }

  /// 构建按钮组件
  ///
  /// [onPressed] - 按钮点击回调
  /// [edgePadding] - 按钮与边缘的间距
  Widget _buildButton({
    required VoidCallback onPressed,
    required Widget childW,
    required double buttonWidth,
    required Color bgColor,
    required Color textColor,
  }) {
    return SizedBox(
      width: buttonWidth,
      child: Padding(
        padding: const EdgeInsets.only(
          top: buttonsTopAndBottomBetween,
          bottom: buttonsTopAndBottomBetween,
          right: distanceEdgeSize,
          left: distanceEdgeSize,
        ),
        child: OutlinedButton(
          onPressed: onPressed,
          style: OutlinedButton.styleFrom(
            backgroundColor: bgColor,
            foregroundColor: textColor,
            side: BorderSide(color: textColor, width: 1),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
          child: childW,
        ),
      ),
    );
  }
}
