import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/login_reset_password/presentation/controllers/login_reset_password_controllers.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

const double leftViewMargin = 15;
const double radiusSize = 4;

/// 重置密码主体View展示
class ResetPasswordViewWidget extends GetView<LoginResetPasswordControllers> {
  const ResetPasswordViewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        children: [
          _userInfoViewWidget(),
          const SizedBox(height: 20),
          _resetPasswordInfoViewWidget(),
          const SizedBox(height: 20),
          Obx(() => controller.state.isBiometrics.value ? _buildBiometricWarning() : const SizedBox.shrink()),
        ],
      ),
    );
  }

  _userInfoViewWidget() {
    return IntrinsicHeight(
      child: Column(
        children: [
          _headerTitleViewWidget(title: '基本情報'),
          Container(
            padding: const EdgeInsets.only(top: 8, bottom: 8),
            decoration: BoxDecoration(
              color: AppTheme.whiteTranslucentColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(radiusSize),
                bottomRight: Radius.circular(radiusSize),
              ), // 设定圆角半径
            ),
            child: Column(
              children: [
                Obx(() => _userInfoViewTestWidget(title: 'テナント', content: controller.state.tenantName.value)),
                Obx(() => _userInfoViewTestWidget(title: 'メールアドレス', content: controller.state.userEmail.value)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _userInfoViewTestWidget({required String title, required String content}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(width: leftViewMargin),
        SizedBox(
          width: 110.0,
          child: Text(
            title,
            softWrap: true,
            textAlign: TextAlign.left,
            style: const TextStyle(fontSize: 14.0, color: AppTheme.black54Color),
          ),
        ),
        const Text(':', style: TextStyle(fontSize: 14.0, color: AppTheme.blackColor)),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(
            content,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(fontSize: 14.0, color: AppTheme.blackColor),
          ),
        ),
      ],
    );
  }

  _resetPasswordInfoViewWidget() {
    return IntrinsicHeight(
      child: Column(
        children: [
          _headerTitleViewWidget(title: '新しいパスワードの作成'),
          Container(
            padding: const EdgeInsets.only(top: 20, bottom: 20, left: 25, right: 25),
            decoration: BoxDecoration(
              color: AppTheme.whiteTranslucentColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(radiusSize),
                bottomRight: Radius.circular(radiusSize),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _resetPasswordInputViewWidget(
                  title: '新しいパスワード',
                  contInfo: controller.newPasswordController,
                  key: const Key('new_password_field'),
                ),
                // 新密码错误提示
                Obx(
                  () => controller.state.hasNewPasswordError.value
                      ? Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            controller.state.newPasswordTip.value,
                            textAlign: TextAlign.left,
                            style: const TextStyle(color: Colors.red, fontSize: 12),
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
                const SizedBox(height: 16),
                _resetPasswordInputViewWidget(
                  title: '新しいパスワード（確認用）',
                  contInfo: controller.newPasswordConfirmController,
                  key: const Key('confirm_password_field'),
                ),
                // 新密码错误提示
                Obx(
                  () => controller.state.hasConfirmPasswordError.value
                      ? Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            controller.state.passwordConfirmTip.value,
                            textAlign: TextAlign.left,
                            style: const TextStyle(color: Colors.red, fontSize: 12),
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
                const SizedBox(height: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'パスワードの条件',
                      style: TextStyle(color: AppTheme.blackColor, fontSize: 16, fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 8),
                    Obx(() {
                      final mustContainArray = controller.state.mustContainArray;
                      final int length = controller.state.passwordLength.value;
                      return Column(
                        children: [
                          _buildPasswordRule('$length文字以上'),
                          ...mustContainArray.map((item) => _buildPasswordRule(item)).toList(),
                        ],
                      );
                    }),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 输入框共通组件
  _resetPasswordInputViewWidget({required String title, required TextEditingController contInfo, Key? key}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(color: AppTheme.blackColor, fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        // 新密码输入框
        SizedBox(
          height: 40,
          child: TextField(
            key: key,
            controller: contInfo,
            obscureText: true,
            keyboardType: TextInputType.visiblePassword,
            autofillHints: [AutofillHints.password],
            style: const TextStyle(
              fontSize: 16, // 设置输入文字大小
            ),
            decoration: InputDecoration(
              hintText: 'パスワード規則に従ってください',
              hintStyle: const TextStyle(color: Colors.black26),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8), borderSide: BorderSide.none),
              contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
            ),
          ),
        ),
      ],
    );
  }

  /// 头部文字共通组件
  _headerTitleViewWidget({required String title}) {
    return Container(
      height: 40,
      decoration: const BoxDecoration(
        color: AppTheme.whiteColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(radiusSize),
          topRight: Radius.circular(radiusSize),
        ), // 设定圆角半径
      ),
      child: Row(
        children: [
          const SizedBox(width: leftViewMargin),
          Container(
            width: 4, // 宽度
            height: 16, // 高度
            decoration: BoxDecoration(
              color: AppTheme.darkBlueColor,
              borderRadius: BorderRadius.circular(10), // 设定圆角半径
            ),
            child: const SizedBox(),
          ),
          const SizedBox(width: 8),
          // 资产种类名字 以及这个资产种类有多少个资产
          Text(
            title,
            style: const TextStyle(color: AppTheme.blackColor, fontSize: 14, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  /// 构建密码规则项
  Widget _buildPasswordRule(String rule) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        children: [
          const Icon(Icons.circle, size: 6, color: AppTheme.blackColor),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              rule,
              overflow: TextOverflow.visible,
              style: const TextStyle(color: AppTheme.black54Color),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建生物识别警告区块
  Widget _buildBiometricWarning() {
    final double borderRadiusBiometricSize = 8;
    return IntrinsicHeight(
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.whiteTranslucentColor,
          borderRadius: BorderRadius.all(Radius.circular(borderRadiusBiometricSize)),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 警告图标
            Container(
              width: 10,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Colors.amber,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(borderRadiusBiometricSize),
                  bottomLeft: Radius.circular(borderRadiusBiometricSize),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Container(
              padding: const EdgeInsets.all(0), // 给图标添加内边距
              decoration: BoxDecoration(
                color: Colors.white, // 背景色为白色
                borderRadius: BorderRadius.circular(50), // 可以调整圆角半径
              ),
              child: const Icon(Icons.error, color: Colors.amber, size: 30),
            ),
            const SizedBox(width: 8),
            // 警告文本
            const Expanded(
              child: Padding(
                padding: EdgeInsets.only(left: 8, top: 8, right: 15, bottom: 8),
                child: Text(
                  'パスワードを再設定すると、生体認証情報がクリアされます。引き続き生体認証をご利用される場合は、マイページからもう一度生体認証を設定してください。',
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
