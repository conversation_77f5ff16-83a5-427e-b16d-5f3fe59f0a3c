import 'dart:convert';

import 'package:asset_force_mobile_v2/core/deeplink/deeplink_service_impl.dart';
import 'package:asset_force_mobile_v2/core/deeplink/i_deeplink_service.dart';
import 'package:asset_force_mobile_v2/core/env/env_helper_impl.dart';
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart';
import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/services/sso_scan_plugin_impl.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/i_sso_scan_plugin.dart';

/// 用户SSO输入错误，这是一组错误显示，数组中第一位是标题，数组中第二位是正文
const loginSsoInputUrlError = ['入力された内容に不備があります', '表示されたメッセージをご確認の上、もう一度設定してください。'];

/// 用于处理登录逻辑的 UseCase
class LoginSsoUseCase implements UseCase<dynamic, String> {
  final ISsoScanPlugin ssoScanPlugin;
  final IDeeplinkService deeplinkService;
  final IStorageUtils storageUtils;
  final IEnvHelper envHelper;

  LoginSsoUseCase({
    ISsoScanPlugin? ssoScanPlugin,
    IDeeplinkService? deeplinkService,
    required this.storageUtils,
    IEnvHelper? envHelper,
  }) : ssoScanPlugin = ssoScanPlugin ?? SsoScanPluginImpl(),
       deeplinkService = deeplinkService ?? DeeplinkServiceImpl(),
       envHelper = envHelper ?? EnvHelperImpl();

  @override
  Future call(pragma) async {
    // 校验正则
    final regex = RegExp(
      r'^https://(?:dev\.|qa\.|stg\.)?asset-force\.com/pages/third-party-sso\.html\?tenantId=[^&]+&z-id=[^&]+$',
    );

    final String ssoUrlValue = pragma;
    if (ssoUrlValue.isEmpty || !regex.hasMatch(ssoUrlValue)) {
      throw BusinessException(loginSsoInputUrlError.first);
    }

    _openSsoInit(ssoUrl: ssoUrlValue);
  }

  /// 跳转到原生并由原生触发浏览器加载sso页面
  _openSsoInit({bool? isCleanSso, required String ssoUrl, bool? isExternalBrowser}) async {
    // 采用第三方浏览器加载sso true 或 采用原生webview加载sso false
    final isEB = isExternalBrowser ?? true;
    // 是否要清理sso的cookie
    final isCS = isCleanSso ?? envHelper.isAndroid() ? true : false;
    // ssoUrl
    final su = ssoUrl;
    final suPam = Uri.parse(su);
    final qp = suPam.queryParameters;
    final String tenantId = qp['tenantId']!;
    final String z_id = qp['z-id']!;
    final String appModel = envHelper.getEnvironment();
    // 为下一次用户进入登录页面后，直接变为sso二次登陆作为依据判断
    await storageUtils.setValue<String>(appModel + StorageKeys.ssoTenantUrl + tenantId + z_id, ssoUrl);
    // 传给原生的参数
    final Map<String, dynamic> ssoData = {'ssoUrl': su, 'isExternalBrowser': isEB};

    if (envHelper.isAndroid()) {
      // 安卓默认采用原生的webview启动sso页面
      ssoData['isExternalBrowser'] = false;
    }

    if (isCS) {
      // 原生清理cookiePlugin
      await ssoScanPlugin.toClearSsoLoginCache();
    }
    // 安卓系触会有下面的返回值
    final deepLinkData = await ssoScanPlugin.toSsoSetting(data: {'value': ssoData});
    final String? deepLinkUrl = deepLinkData.deepLinkUrl;
    if (deepLinkUrl != null && deepLinkUrl.isNotEmpty) {
      // 一般进入到这个逻辑均为安卓机型
      await deeplinkService.processLink(Uri.parse(deepLinkUrl));
    }
  }

  /// 启动原生扫描
  Future<String?> openScanBarcode() async {
    final barcodeData = await ssoScanPlugin.openBarcodeScan();

    final String? ssoUrl = barcodeData.QRCode;
    return ssoUrl;
  }

  /// 将 JSON 字符串转换为 List<TenantSsoModel>
  List<TenantSsoModel> fromJsonList({required String? jsonString}) {
    if (jsonString == null || jsonString.isEmpty) {
      return [];
    }
    final List<dynamic> jsonList = jsonDecode(jsonString); // 解析 JSON 字符串为 List
    return jsonList.map((json) => TenantSsoModel.fromJson(json)).toList(); // 转换为 List<TenantSsoModel>
  }

  /// 普通转换Sso信息tenants
  String processTenants({required List<TenantSsoModel> tenants}) {
    // 将处理后的结果转换为 JSON 字符串
    final List<Map<String, dynamic>> jsonList = tenants.map((tenant) => tenant.toJson()).toList();
    return jsonEncode(jsonList);
  }

  /// 带有排序的转换Sso信息tenants
  String processOrderTenants({required List<TenantSsoModel> tenants}) {
    final seenTenantIds = <String>{};
    final uniqueTenants = <TenantSsoModel>[];

    for (var tenant in tenants) {
      if (seenTenantIds.contains(tenant.tenantId)) {
        // 首先删除已经存在的 tenant
        uniqueTenants.removeWhere((item) => item.tenantId == tenant.tenantId);
        // 如果 tenantId 已经存在，将其移到第一位
        uniqueTenants.insert(0, tenant); // 插入到数组的第一位
      } else {
        // 否则，添加到数组的末尾
        uniqueTenants.add(tenant);
        seenTenantIds.add(tenant.tenantId);
      }
    }

    // 将处理后的结果转换为 JSON 字符串
    final List<Map<String, dynamic>> jsonList = uniqueTenants.map((tenant) => tenant.toJson()).toList();
    return jsonEncode(jsonList);
  }
}
