import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/controllers/login_sso_controller.dart';
import 'package:get/get.dart';

class LoginSsoBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<LoginSsoUseCase>(() => LoginSsoUseCase(storageUtils: Get.find<IStorageUtils>()));

    Get.lazyPut<LoginSsoController>(
      () => LoginSsoController(
        loginSsoUseCase: Get.find<LoginSsoUseCase>(),
        dialogService: Get.find<DialogService>(),
        navigationService: Get.find<NavigationService>(),
      ),
    );
  }
}
