import 'package:asset_force_mobile_v2/core/env/env_helper_impl.dart';
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart';
import 'package:get/get.dart';

class LoginSsoListController extends BaseController {
  final LoginSsoUseCase loginSsoUseCase;
  final NavigationService navigationService;
  final DialogService dialogService;
  final IEnvHelper envHelper;
  final IStorageUtils storageUtils;

  LoginSsoListController({
    required this.navigationService,
    required this.loginSsoUseCase,
    required this.dialogService,
    required this.storageUtils,
    IEnvHelper? envHelper,
  }) : envHelper = envHelper ?? EnvHelperImpl();

  RxList<TenantSsoModel> ssoItemList = <TenantSsoModel>[].obs;
  RxBool isSsoListEdit = false.obs;
  late final String _appModel;

  @override
  void onInit() async {
    super.onInit();

    _appModel = envHelper.getEnvironment();
    final String? ssoObjStr = storageUtils.getValue<String>(_appModel + StorageKeys.ssoTenantUrl);
    final List<TenantSsoModel> dataList = loginSsoUseCase.fromJsonList(jsonString: ssoObjStr);
    ssoItemList.value = dataList;
  }

  /// sso编辑按钮
  ssoListEditOnClick() {
    isSsoListEdit.value = !isSsoListEdit.value;
  }

  /// 删除特定的元素
  void ssoListDeleteOnClick({required TenantSsoModel ssoItem}) async {
    await dialogService.show(
      content: 'SMFLのシングルサインオン(SSO)設定を削除します',
      cancelText: 'キャンセル',
      onConfirm: () async {
        // 使用 remove() 删除特定的元素
        ssoItemList.remove(ssoItem); // 根据对象直接删除
        await storageUtils.setValue<String>(
          _appModel + StorageKeys.ssoTenantUrl,
          loginSsoUseCase.processTenants(tenants: ssoItemList),
        );
        dialogService.showToast('SSO設定を削除しました');
      },
    );
  }

  /// 跳转添加页面
  void ssoListAddOnClick() {
    navigationService.navigateTo(AutoRoutes.loginSso);
  }

  /// 跳转sso验证界面页面
  void ssoListGoToWebViewOnClick({required TenantSsoModel ssoItem}) {
    loginSsoUseCase.call(ssoItem.ssoUrl);
  }

  goBackPage() {
    navigationService.goBack();
  }
}
