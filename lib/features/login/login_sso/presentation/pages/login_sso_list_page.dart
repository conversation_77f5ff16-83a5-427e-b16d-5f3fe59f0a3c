import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/bindings/login_sso_list_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/controllers/login_sso_list_controller.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/login_sso/list', binding: LoginSsoListBinding)
class LoginSsoListPage extends GetView<LoginSsoListController> {
  const LoginSsoListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // backgroundColor: defaultColor,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.chevron_left, color: AppTheme.whiteColor, size: 30),
          onPressed: () => controller.goBackPage(),
        ),
        title: const Text(
          'SSOテナント選択',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: AppTheme.whiteColor),
        ),
        actions: [
          Obx(() {
            final bool isSsoListEdit = controller.isSsoListEdit.value;
            return TextButton(
              onPressed: () => controller.ssoListEditOnClick(),
              child: Text(isSsoListEdit ? 'キャンセル' : '編集', style: const TextStyle(color: AppTheme.whiteColor)),
            );
          }),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Obx(() {
            final bool isSsoListEdit = controller.isSsoListEdit.value;
            return ListView.builder(
              itemCount: controller.ssoItemList.length + 1,
              itemBuilder: (context, index) {
                if (index == controller.ssoItemList.length) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 30.0),
                    child: TextButton(
                      onPressed: () => controller.ssoListAddOnClick(),
                      child: const Text(
                        'テナントを追加',
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: AppTheme.whiteColor),
                      ),
                    ),
                  );
                }
                final TenantSsoModel ssoItem = controller.ssoItemList[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Container(
                    decoration: BoxDecoration(color: AppTheme.white85Color, borderRadius: BorderRadius.circular(8.0)),
                    child: isSsoListEdit ? _ssoEditView(ssoItem: ssoItem) : _ssoView(ssoItem: ssoItem),
                  ),
                );
              },
            );
          }),
        ),
      ),
    );
  }

  Widget _ssoView({required TenantSsoModel ssoItem}) {
    return InkWell(
      onTap: () => controller.ssoListGoToWebViewOnClick(ssoItem: ssoItem),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                ssoItem.tenantName,
                maxLines: 3, // 不限制行数
                overflow: TextOverflow.ellipsis, // 确保文本不会被截断
                softWrap: true, // 自动换行
                style: const TextStyle(fontSize: 16.0, fontWeight: FontWeight.w500, color: AppTheme.blackColor),
              ),
            ),
            const Icon(Icons.chevron_right, color: AppTheme.blackColor),
          ],
        ),
      ),
    );
  }

  Widget _ssoEditView({required TenantSsoModel ssoItem}) {
    final List<Map<String, String>> ssoLayoutItemList = [
      {'titleName': 'テナント名', 'valueName': ssoItem.tenantName},
      {'titleName': 'テナントURL', 'valueName': ssoItem.ssoUrl},
    ];

    return IntrinsicHeight(
      child: Row(
        children: [
          // 显示内容
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: ssoLayoutItemList.map((displayItem) {
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 88.0,
                        child: Text(
                          displayItem['titleName']!,
                          softWrap: true,
                          textAlign: TextAlign.left,
                          style: const TextStyle(fontSize: 14.0, color: AppTheme.blackColor),
                        ),
                      ),
                      const Text(':', style: TextStyle(fontSize: 14.0, color: AppTheme.blackColor)),
                      const SizedBox(width: 5.0),
                      Expanded(
                        child: Text(
                          displayItem['valueName']!,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          softWrap: true,
                          style: const TextStyle(fontSize: 16.0, color: AppTheme.blackColor),
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
          // 竖线
          const SizedBox(
            width: 1, // 竖线宽度
            height: double.infinity,
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: AppTheme.black12Color, // 竖线的颜色
              ),
            ),
          ),
          // 删除按钮
          Container(
            width: 45,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.zero,
                backgroundColor: AppTheme.transparentColor,
                side: BorderSide.none,
                shadowColor: AppTheme.transparentColor,
                shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
              ),
              onPressed: () => controller.ssoListDeleteOnClick(ssoItem: ssoItem),
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.delete, size: 20, color: AppTheme.blackColor),
                  Text('削除', style: TextStyle(color: AppTheme.blackColor, fontSize: 13)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
