import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/bindings/login_sso_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/controllers/login_sso_controller.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/pages/sso_bottom_buttons_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/login/sso', binding: LoginSsoBinding)
class LoginSsoPage extends GetView<LoginSsoController> {
  const LoginSsoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          key: const Key('back_button'),
          icon: const Icon(Icons.chevron_left, color: AppTheme.whiteColor, size: 35),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text('シングルサインオン(SSO)設定', style: TextStyle(fontSize: 18)),
        actions: [
          IconButton(
            key: const Key('qr_scan_button'),
            icon: const Icon(Icons.qr_code_scanner_outlined, color: AppTheme.whiteColor, size: 24),
            onPressed: controller.openScanBarcodeOnClick,
          ),
        ],
      ),
      body: IntrinsicHeight(
        child: Container(
          padding: const EdgeInsets.only(left: 5, right: 5, top: 18),
          child: Card(
            elevation: 0,
            color: AppTheme.whiteTranslucentColor,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('テナントURL', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  TextField(
                    key: const Key('sso_url_input_field'),
                    textInputAction: TextInputAction.go,
                    onChanged: (String url) => controller.onChangeSsoUrlInput(ssoUrl: url),
                    onSubmitted: (_) => controller.onClickSsoGotoThirdParty(),
                    controller: controller.textEditingController,
                    style: const TextStyle(color: AppTheme.darkShadesGrayColor, fontSize: 15),
                    decoration: InputDecoration(
                      filled: true, // 使背景颜色可见
                      fillColor: AppTheme.whiteColor,
                      contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8), borderSide: BorderSide.none),
                      suffixIcon: Obx(() {
                        final hasNotInput = controller.state.ssoUrlInput.value.isEmpty;
                        if (hasNotInput) {
                          return const SizedBox.shrink();
                        }
                        return SizedBox(
                          width: 32,
                          child: IconButton(
                            key: const Key('clear_button'),
                            icon: const Icon(Icons.clear, color: AppTheme.blackColor, size: 16),
                            onPressed: controller.onClickClearSsoUrlInput,
                          ),
                        );
                      }),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _customTextRow(text: 'SSO機能が有効な場合、マイアカウントまたは設定＞テナントから取得することができます。 テナント管理者にご確認ください。'),
                  const SizedBox(height: 4),
                  _customTextRow(text: '画面右上のQRコードのアイコンをタップし、上記QRコードを読み取るとURLが反映されます。'),
                ],
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: const SsoBottomButtonsWidget(key: Key('bottom_buttons')),
    );
  }

  Widget _customTextRow({required String text}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('※', style: TextStyle(fontSize: 14)),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(fontSize: 14, color: AppTheme.black54Color),
            softWrap: true, // 启用软换行
            overflow: TextOverflow.visible, // 保证不会截断文本
          ),
        ),
      ],
    );
  }
}
