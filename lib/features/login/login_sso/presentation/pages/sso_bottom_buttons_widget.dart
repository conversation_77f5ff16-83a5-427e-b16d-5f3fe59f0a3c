import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/presentation/controllers/login_sso_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

/// 按钮高度
const double buttonHeight = 38;

/// 按钮之间的上下间距
const double buttonsTopAndBottomBetween = 5;

const double distanceEdgeSize = 16;

class SsoBottomButtonsWidget extends GetView<LoginSsoController> {
  const SsoBottomButtonsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppTheme.whiteColor,
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: _buildButton(
                onPressed: controller.goBackPage,
                titleStr: 'キャンセル',
                bgColor: AppTheme.transparentColor,
                textColor: AppTheme.darkBlueColor,
              ),
            ),
            Expanded(
              child: _buildButton(
                onPressed: controller.onClickSsoGotoThirdParty,
                titleStr: 'ログイン',
                bgColor: AppTheme.darkBlueColor,
                textColor: AppTheme.whiteColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建按钮组件
  ///
  /// [onPressed] - 按钮点击回调
  /// [titleStr] - 按钮文本
  /// [bgColor] - 背景颜色
  /// [textColor] - 文本颜色
  Widget _buildButton({
    required VoidCallback onPressed,
    required String titleStr,
    required Color bgColor,
    required Color textColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(
        top: buttonsTopAndBottomBetween,
        bottom: buttonsTopAndBottomBetween,
        right: distanceEdgeSize,
        left: distanceEdgeSize,
      ),
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: bgColor,
          foregroundColor: textColor,
          side: BorderSide(color: textColor, width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Text(titleStr, style: const TextStyle(fontWeight: FontWeight.w700)),
      ),
    );
  }
}
