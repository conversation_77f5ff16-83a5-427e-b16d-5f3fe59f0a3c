import 'package:json_annotation/json_annotation.dart';

part 'dpp_info_model.g.dart';

@JsonSerializable()
class DppInfoModel {
  /// DPP ID
  @JsonKey(name: 'dppId')
  String? dppId;

  /// Mo<PERSON>les
  @Json<PERSON>ey(name: 'modules')
  List<ModuleModel>? modules;

  /// Eunomia ID
  @JsonKey(name: 'eunomiaID')
  String? eunomiaID;

  /// DPP Access Key
  @JsonKey(name: 'dppAccessKey')
  String? dppAccessKey;

  /// Eunomia Access Key
  @JsonKey(name: 'eunomiaAccessKey')
  String? eunomiaAccessKey;

  /// Eunomia Upload Result URL
  @JsonKey(name: 'eunomiaUploadResult')
  String? eunomiaUploadResult;

  /// Eunomia Get Model Version URL
  @JsonKey(name: 'eunomiaGetModelVersion')
  String? eunomiaGetModelVersion;

  /// Eunomia Get Model Download URL
  @JsonKey(name: 'eunomiaGetModelDownloadURL')
  String? eunomiaGetModelDownloadURL;

  DppInfoModel({
    this.dppId,
    this.modules,
    this.eunomiaID,
    this.dppAccessKey,
    this.eunomiaAccessKey,
    this.eunomiaUploadResult,
    this.eunomiaGetModelVersion,
    this.eunomiaGetModelDownloadURL,
  });

  factory DppInfoModel.fromJson(Map<String, dynamic> json) => _$DppInfoModelFromJson(json);
  Map<String, dynamic> toJson() => _$DppInfoModelToJson(this);

  @override
  String toString() {
    return 'DppInfoModel{dppId=$dppId, modules=$modules, eunomiaID=$eunomiaID, dppAccessKey=$dppAccessKey, eunomiaAccessKey=$eunomiaAccessKey, eunomiaUploadResult=$eunomiaUploadResult, eunomiaGetModelVersion=$eunomiaGetModelVersion, eunomiaGetModelDownloadURL=$eunomiaGetModelDownloadURL}';
  }
}

@JsonSerializable()
class ModuleModel {
  /// Tenants
  @JsonKey(name: 'tenants')
  List<String>? tenants;

  /// Serial Number
  @JsonKey(name: 'serialNo')
  String? serialNo;

  /// Module URL
  @JsonKey(name: 'moduleUrl')
  String? moduleUrl;

  /// Module Type
  @JsonKey(name: 'moduleType')
  String? moduleType;

  ModuleModel({this.tenants, this.serialNo, this.moduleUrl, this.moduleType});

  factory ModuleModel.fromJson(Map<String, dynamic> json) => _$ModuleModelFromJson(json);
  Map<String, dynamic> toJson() => _$ModuleModelToJson(this);

  @override
  String toString() {
    return 'ModuleModel{tenants=$tenants, serialNo=$serialNo, moduleUrl=$moduleUrl, moduleType=$moduleType}';
  }
}
