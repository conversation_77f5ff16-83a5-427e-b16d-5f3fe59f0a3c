import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/dpp_info_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/map_token_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/password_policy_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/user_role_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_use_status_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'get_token_result_model.g.dart';

@JsonSerializable(explicitToJson: true)
class GetTokenResultModel extends BaseResponse {
  /// 有効期限チェック (パスワード有効期限を切れました（パスワードポリシーを戻る、パスワード変換画面切替）)
  @JsonKey(name: 'policy', includeIfNull: false)
  PasswordPolicyModel? policy;

  /// ユーザークラス
  @JsonKey(name: 'user', includeIfNull: false)
  SharedUserModel? user;

  /// ユーザークラス
  @JsonKey(name: 'mapToken', includeIfNull: false)
  MapTokenModel? mapToken;

  /// プラン(0：ベーシックプラン　1：プレミアムプラン)
  @JsonKey(name: 'membership', includeIfNull: false)
  int? membership;

  /// プリンタ機能利用可否フラグ（0：プリント不可　1：プリント可能）」
  @JsonKey(name: 'printEnableFlg', includeIfNull: false)
  String? printEnableFlg;

  /// 多要素認証
  @JsonKey(name: 'tenantEnableTwoStep', includeIfNull: false)
  bool? tenantEnableTwoStep;

  /// barcode 条件で、スクリプト情報を取得
  @JsonKey(name: 'barcodeExtraction', includeIfNull: false)
  String? barcodeExtraction;

  /// marketplace遷移のURL
  @JsonKey(name: 'hasAssetforce', includeIfNull: false)
  bool? hasAssetforce;

  /// モバイル側のみでDatadogのオープンステータスを取得
  /// * 1: ユーザー操作記録を有効化。
  /// * 2: ユーザー操作記録と画面記録を有効化。
  /// * 3: ユーザー操作記録、画面記録、コード内のログ収集記録を有効化。
  @JsonKey(name: 'datadogOpenType', includeIfNull: false)
  int? datadogOpenType;

  /// 別のシステムへ遷移のURL
  @JsonKey(name: 'systemUrl', includeIfNull: false)
  String? systemUrl;

  /// デフォルトシステム
  @JsonKey(name: 'defaultSystem', includeIfNull: false)
  String? defaultSystem;

  /// グループクラス
  @JsonKey(name: 'userRoleList', includeIfNull: false)
  List<UserRoleModel>? userRoleList;

  /// IP
  @JsonKey(name: 'hostInfo', includeIfNull: false)
  String? hostInfo;

  /// テナント利用状況クラス
  @JsonKey(name: 'tenantUseStatesList', includeIfNull: false)
  List<SharedTenantUseStatusModel>? tenantUseStatesList;

  /// DPPインフォ
  @JsonKey(name: 'dppInfo', includeIfNull: false)
  DppInfoModel? dppInfo;

  /// zone domain key
  @JsonKey(name: 'zoneDomainKey', includeIfNull: false)
  String? zoneDomainKey;

  /// tenant key
  @JsonKey(name: 'tenant', includeIfNull: false)
  SharedTenantModel? tenant;

  @JsonKey(name: 'signInResult', includeIfNull: false)
  String? signInResult;

  GetTokenResultModel({
    this.policy,
    this.user,
    this.mapToken,
    this.membership,
    this.printEnableFlg,
    this.tenantEnableTwoStep,
    this.barcodeExtraction,
    this.hasAssetforce,
    this.datadogOpenType,
    this.systemUrl,
    this.defaultSystem,
    this.userRoleList,
    this.hostInfo,
    this.tenantUseStatesList,
    this.dppInfo,
    this.zoneDomainKey,
    this.signInResult,
    required super.code,
    required super.msg,
  });

  factory GetTokenResultModel.fromJson(Map<String, dynamic> json) => _$GetTokenResultModelFromJson(json);
  Map<String, dynamic> toJson() => _$GetTokenResultModelToJson(this);
}
