import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/map_token_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'login_result_model.g.dart';

/// ログイン結果クラス
@JsonSerializable()
class LoginResultModel {
  /// テナントリスト
  @JsonKey(name: 'tenants')
  List<SharedTenantModel>? tenants;

  /// トークン情報など
  @JsonKey(name: 'mapToken')
  MapTokenModel? mapToken;

  LoginResultModel({this.tenants, this.mapToken});

  /// 从 JSON 中创建 LoginResult 对象
  factory LoginResultModel.fromJson(Map<String, dynamic> json) => _$LoginResultModelFromJson(json);

  /// 将 LoginResult 对象转换为 JSON
  Map<String, dynamic> toJson() => _$LoginResultModelToJson(this);
}
