import 'package:json_annotation/json_annotation.dart';

part 'map_token_model.g.dart';

/// MapTokenModel
@JsonSerializable()
class MapTokenModel {
  /// getTokenに使われるticket情報
  @J<PERSON><PERSON><PERSON>(name: 'ticket', includeIfNull: false)
  String? ticket;

  /// token 登录身份唯一标识
  @Json<PERSON><PERSON>(name: 'accessToken', includeIfNull: false)
  String? accessToken;

  /// 1 == 超级管理员登录, 0 == 普通
  @JsonKey(name: 'tenantAdmin', includeIfNull: false)
  String? tenantAdmin;

  /// ユーザーID
  @JsonKey(name: 'userId', includeIfNull: false)
  int? userId;

  /// tokenリフレッシュ
  @JsonKey(name: 'refreshToken', includeIfNull: false)
  String? refreshToken;

  MapTokenModel({this.ticket, this.accessToken, this.tenantAdmin, this.userId, this.refreshToken});

  /// 从 JSON 中创建 Tenant 对象
  factory MapTokenModel.fromJson(Map<String, dynamic> json) => _$MapTokenModelFromJson(json);

  /// 将 Tenant 对象转换为 JSON
  Map<String, dynamic> toJson() => _$MapTokenModelToJson(this);
}
