import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/login_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/system_mobile_deploy_manage_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/repositories/login_traditional_repository.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';

class LoginTraditionalRepositoryImpl with RepositoryErrorHandler implements LoginTraditionalRepository {
  final DioUtil dioUtil;

  LoginTraditionalRepositoryImpl({required this.dioUtil});

  @override
  Future<LoginResultModel> login({required String userName, required String password}) async {
    return executeRepositoryTask<LoginResultModel>(() async {
      final Map<String, dynamic> requestData = {'userName': userName.trim(), 'password': password.trim()};

      LogUtil.d('Login API Request: $requestData');

      final response = await dioUtil.post(GlobalVariable.secureSignIn, data: requestData, useFormUrlEncoded: true);

      if (response.isSuccess()) {
        return LoginResultModel.fromJson(response.data);
      } else {
        LogUtil.e('Login Failed: ${response.statusCode}');
        final data = response.data as Map<String, dynamic>?;
        final message = data?['message'] as String?;
        if (message == userApiAccountExpiration) {
          throw BusinessException(userShowAccountExpiration);
        }
        throw BusinessException('Login failed due to API response status: ${response.statusCode}');
      }
    }, 'Login failed');
  }

  @override
  Future<SystemMobileDeployManageResultModel> getMobileDeployManageApiHostVersion({required int appClientType}) async {
    assert(appClientType == 0 || appClientType == 1 || appClientType == 2, '参数必须是0, 1或2');

    return executeRepositoryTask<SystemMobileDeployManageResultModel>(() async {
      final response = await dioUtil.get(
        GlobalVariable.secureGetLastedApi,
        queryParams: {'appClientType': appClientType},
      );
      if (response.isSuccess()) {
        return SystemMobileDeployManageResultModel.fromJson(response.data);
      } else {
        LogUtil.e('getMobileDeployManageApiHostVersion Failed: ${response.statusCode}');
        throw BusinessException(
          'Failed to get mobile deploy manage API host version due to API status: ${response.statusCode}',
        );
      }
    }, 'Failed to get mobile deploy manage API host version');
  }

  @override
  Future<SystemMobileDeployManageResultModel?> getMobileDeployMobileVersion({
    required String mobileVersion,
    required int appClientType,
  }) async {
    assert(appClientType == 0 || appClientType == 1 || appClientType == 2, '参数必须是0, 1或2');

    return executeRepositoryTask<SystemMobileDeployManageResultModel?>(() async {
      final response = await dioUtil.get(
        GlobalVariable.secureGetLastedApi,
        queryParams: {'appClientType': appClientType},
      );
      if (response.isSuccess()) {
        if (response.data == null) {
          return null;
        }
        return SystemMobileDeployManageResultModel.fromJson(response.data);
      } else {
        LogUtil.e('getMobileDeployMobileVersion Failed: ${response.statusCode}');
        throw BusinessException('Failed to get mobile deploy mobile version due to API status: ${response.statusCode}');
      }
    }, 'Failed to get mobile deploy mobile version');
  }
}
