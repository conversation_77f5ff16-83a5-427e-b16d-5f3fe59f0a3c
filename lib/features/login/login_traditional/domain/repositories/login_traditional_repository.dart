import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/login_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/system_mobile_deploy_manage_result_model.dart';

/// 定义登录相关的 Repository 接口
abstract class LoginTraditionalRepository {
  /// 调用登录 API
  ///
  /// [userName]: 用户名
  /// [password]: 密码
  /// [isBiometrics]: 是否使用生物识别
  ///
  /// 返回:
  /// - 登录响应对象
  Future<LoginResultModel> login({required String userName, required String password});

  /// リリース済の最新のモバイルバージョンを取得する
  /// * [appClientType] バージョン管理端末(0:iOSとAndroid両方,1:iOSのみ,2:Androidのみ)
  /// * 返回:
  ///   - 登录响应对象
  Future<SystemMobileDeployManageResultModel> getMobileDeployManageApiHostVersion({required int appClientType});

  /// 指定のモバイルバージョンより、そのapiHostを取得する
  /// * [appClientType] バージョン管理端末(0:iOSとAndroid両方,1:iOSのみ,2:Androidのみ)
  /// * [mobileVersion] モバイルバージョン
  /// * 返回:
  ///   - 登录响应对象
  Future<SystemMobileDeployManageResultModel?> getMobileDeployMobileVersion({
    required String mobileVersion,
    required int appClientType,
  });
}
