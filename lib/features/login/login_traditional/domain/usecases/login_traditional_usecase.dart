import 'package:asset_force_mobile_v2/core/env/env_helper_impl.dart';
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart';
import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/login_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/map_token_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/system_mobile_deploy_manage_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/repositories/login_traditional_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart';

/// 参数类，用于传递用户名、密码和生物识别参数
class LoginParams {
  final String userName;
  final String password;

  LoginParams({required this.userName, required this.password});
}

/// 用于处理登录逻辑的 UseCase
class LoginTraditionalUseCase implements UseCase<LoginResultModel, LoginParams> {
  final LoginTraditionalRepository loginRepository;
  final IEnvHelper envHelper;

  LoginTraditionalUseCase({required this.loginRepository, IEnvHelper? envHelper})
    : envHelper = envHelper ?? EnvHelperImpl();

  @override
  Future<LoginResultModel> call(LoginParams params) async {
    // 调用登录接口
    final LoginResultModel response = await loginRepository.login(userName: params.userName, password: params.password);
    final List<SharedTenantModel>? tenantList = response.tenants;
    if (tenantList == null) {
      return LoginResultModel();
    }

    if (tenantList.isEmpty) {
      return LoginResultModel();
    }
    final MapTokenModel? mapToken = response.mapToken;

    if (mapToken == null) {
      return LoginResultModel();
    }
    final String? ticket = mapToken.ticket;

    if (ticket == null) {
      return LoginResultModel();
    }

    return response;
  }

  Future<void> versionCheck() async {
    // 注意：这些方法不在接口中，需要保留静态调用
    await envHelper.updateAuthHost();

    if (envHelper.isWindows() || envHelper.isLinux() || envHelper.isMacOS()) {
      await envHelper.updateServerEnv();
      return;
    } else if (envHelper.isIOS() || envHelper.isAndroid()) {
      await _deployMobileVersion();
      return;
    }
  }

  /// 处理移动端启动所需要的校验
  Future<void> _deployMobileVersion() async {
    final int clientType = envHelper.isIOS() ? 1 : 2;
    final String getAppVersion = envHelper.getAppVersion();

    final SystemMobileDeployManageResultModel data = await loginRepository.getMobileDeployManageApiHostVersion(
      appClientType: clientType,
    );
    final String apiMobileVersion = data.mobileVersion ?? '';

    if (_compareVersionNumber(apiMobileVersion, getAppVersion) == 1) {
      throw BusinessException('お使いになられているアプリのバージョンが古い為、バージョンアップをお願い致します。', 109);
    }
    final SystemMobileDeployManageResultModel? releaseInfo = await loginRepository.getMobileDeployMobileVersion(
      mobileVersion: getAppVersion,
      appClientType: clientType,
    );
    if (releaseInfo == null) {
      throw BusinessException('現在メンテナンス中です。通常1～2時間程度で作業完了しますので、それまでお待ちください。ご迷惑をお掛けして申し訳ございません。', 108);
    }
    if (releaseInfo.appDeployStatus == '2') {
      throw BusinessException('現在メンテナンス中です。通常1～2時間程度で作業完了しますので、それまでお待ちください。ご迷惑をお掛けして申し訳ございません。', 108);
    }
    final String apiEnv = releaseInfo.env ?? '';
    if (apiEnv.isEmpty) {
      return;
    }
    final bool isDev = envHelper.isDev();
    if (isDev) {
      envHelper.environment = envHelper.getEnvironment();
    } else {
      envHelper.environment = apiEnv;
    }
    await envHelper.updateServerEnv();
  }

  int _compareVersionNumber(String a, String b) {
    // 如果两个版本号相同
    if (a == b) {
      return 0;
    }

    // 将版本号按 '.' 分割成数组
    final List<String> aComponents = a.split('.');
    final List<String> bComponents = b.split('.');

    // 获取两个数组中较小的长度
    final int len = (aComponents.length < bComponents.length) ? aComponents.length : bComponents.length;

    // 比较各部分的数字
    for (int i = 0; i < len; i++) {
      final int aPart = int.parse(aComponents[i]);
      final int bPart = int.parse(bComponents[i]);

      // 如果 a 部分大于 b 部分
      if (aPart > bPart) {
        return 1;
      }
      // 如果 a 部分小于 b 部分
      if (aPart < bPart) {
        return -1;
      }
    }

    // 如果一个版本号是另一个的前缀，较长的版本号更大
    if (aComponents.length > bComponents.length) {
      return 1;
    }
    if (aComponents.length < bComponents.length) {
      return -1;
    }

    // 否则，版本号相同
    return 0;
  }
}
