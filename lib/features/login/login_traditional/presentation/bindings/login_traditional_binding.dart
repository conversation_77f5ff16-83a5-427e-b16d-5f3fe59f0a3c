import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/utils/biometrics_switch_util.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/repositories/login_traditional_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/repositories/login_traditional_repository.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/domain/usecases/login_traditional_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/presentation/controllers/login_traditional_controller.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/data/repositories/tenant_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:get/get.dart';

class LoginTraditionalBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<LoginTraditionalRepository>(() => LoginTraditionalRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    Get.lazyPut<LoginTraditionalUseCase>(
      () => LoginTraditionalUseCase(loginRepository: Get.find<LoginTraditionalRepository>()),
    );
    Get.lazyPut<TenantRepository>(() => TenantRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    Get.lazyPut<TenantUseCase>(
      () => TenantUseCase(
        tenantRepository: Get.find<TenantRepository>(),
        userRepository: Get.find<UserRepository>(),
        storageUtils: Get.find<IStorageUtils>(),
      ),
    );

    Get.lazyPut<LoginSsoUseCase>(() => LoginSsoUseCase(storageUtils: Get.find<IStorageUtils>()));

    Get.lazyPut<BiometricsSwitchUtil>(() => BiometricsSwitchUtil());
    Get.lazyPut<LoginTraditionalController>(
      () => LoginTraditionalController(
        loginUseCase: Get.find<LoginTraditionalUseCase>(),
        tenantUseCase: Get.find<TenantUseCase>(),
        biometricsSwitchUtil: Get.find<BiometricsSwitchUtil>(),
        loginSsoUseCase: Get.find<LoginSsoUseCase>(),
        navigationService: Get.find<NavigationService>(),
        dialogService: Get.find<DialogService>(),
        storageUtils: Get.find<IStorageUtils>(),
      ),
    );
  }
}
