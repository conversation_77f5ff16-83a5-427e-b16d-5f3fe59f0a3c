import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/presentation/bindings/login_traditional_binding.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/presentation/controllers/login_traditional_controller.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/deeplink_usecase.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/login', binding: LoginTraditionalBinding)
class LoginTraditionalPage extends GetView<LoginTraditionalController> {
  const LoginTraditionalPage({super.key});

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final logoWidth = screenWidth * 0.5;
    // 默认元素高度
    const double defaultElementHeight = 45.0;
    // 判断设备是否为平板 true 是平板
    final bool isTablet = screenWidth > 600; // 600px 作为平板的分界线
    final double inputElementHeight = isTablet ? defaultElementHeight * 2 : defaultElementHeight; // 平板上放大元素高度
    final double buttonElementHeight = isTablet ? defaultElementHeight * 1.4 : defaultElementHeight; // 平板上放大元素高度
    double bottomPadding = MediaQuery.of(context).padding.bottom;
    // 判断是否是 iPhone SE 或其他小屏幕设备，若是则增加额外底部间距
    if (bottomPadding == 0.0) {
      // iPhone SE 或类似设备, 增加底部间距
      bottomPadding = 20.0;
    }
    return GestureDetector(
      onTap: () {
        // 点击空白区域隐藏键盘
        controller.hideKeyboard();
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          bottom: false,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // logo和登录内容居中显示
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // logo
                    Padding(
                      padding: const EdgeInsets.only(bottom: 25),
                      child: Image.asset('assets/images/login/logo-assetforce.png', width: logoWidth),
                    ),
                    // 账户，密码，登录，sso
                    Obx(() {
                      final bool isSsoLastLoginSso = controller.state.isSsoLastLogin.value;
                      if (isSsoLastLoginSso) {
                        return _buildSsoButtons(screenWidth: screenWidth, buttonElementHeight: buttonElementHeight);
                      }
                      return _buildLoginButton(
                        buttonElementHeight: buttonElementHeight,
                        inputElementHeight: inputElementHeight,
                        screenWidth: screenWidth,
                      );
                    }),
                  ],
                ),
              ),
              // 三井信息，版本号固定在底部
              _buildFooter(bottomPadding: bottomPadding + 10.0),
            ],
          ),
        ),
      ),
    );
  }

  /// 账户，密码，登录，sso方法
  Widget _buildLoginButton({
    required double buttonElementHeight,
    required double inputElementHeight,
    required double screenWidth,
  }) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: screenWidth / 6.0),
          child: Column(
            children: [
              _buildInputField(label: 'メールアドレス', obscureText: false, inputElementHeight: inputElementHeight),
              const SizedBox(height: 16),
              _buildInputField(label: 'パスワード', obscureText: true, inputElementHeight: inputElementHeight),
              const SizedBox(height: 20),
              _buttonCommon(
                onClick: controller.loginTraditional,
                textColor: AppTheme.whiteColor,
                buttonElementHeight: buttonElementHeight,
                buttonText: 'ログイン',
                buttonBgColor: AppTheme.darkBlueColor,
                fontSize: 16,
              ),
            ],
          ),
        ),
        // 生物验证
        Obx(() => _buildBiometricsButton()),
      ],
    );
  }

  /// sso 二次登录后显示内容
  Widget _buildSsoButtons({required double screenWidth, required double buttonElementHeight}) {
    final double ssoButtonElementHeight = buttonElementHeight + 18;
    final double ssoFontSize = 18;

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: screenWidth / 9.0),
          child: Column(
            children: [
              const SizedBox(height: 15),
              Obx(() {
                final TenantSsoModel ssoLastTenantData = controller.state.ssoLastTenantData.value;
                return _buttonCommon(
                  onClick: controller.ssoLoginOpenWebViewOnClick,
                  textColor: AppTheme.whiteColor,
                  buttonElementHeight: ssoButtonElementHeight,
                  buttonText: ssoLastTenantData.tenantName,
                  buttonBgColor: AppTheme.darkBlueColor,
                  fontSize: ssoFontSize,
                );
              }),
              const SizedBox(height: 20),
              _buttonCommon(
                onClick: controller.ssoGoToSsoListPageOnClick,
                textColor: AppTheme.whiteColor,
                buttonElementHeight: ssoButtonElementHeight,
                buttonText: 'SSOテナントを追加・編集',
                buttonBgColor: AppTheme.white85Color,
                fontSize: ssoFontSize,
              ),
            ],
          ),
        ),
        const SizedBox(height: 60),
        // 生物验证
        InkWell(
          onTap: () => {controller.state.isSsoLastLogin.value = false},
          child: const Column(
            children: [
              Text(
                'ID/パスワードで',
                style: TextStyle(color: AppTheme.whiteColor, fontSize: 17, fontWeight: FontWeight.bold),
              ),
              Text(
                '別のテナントにログイン',
                style: TextStyle(color: AppTheme.whiteColor, fontSize: 17, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 底部SSO按钮、三井厂家信息和版本信息
  Widget _buildFooter({required double bottomPadding}) {
    return Column(
      children: [
        // SSO按钮
        InkWell(
          onTap: () => controller.loginSso(),
          child: const Text(
            'シングルサインオン(SSO)を利用する',
            style: TextStyle(color: AppTheme.whiteColor, fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 20),
        const Text(
          '三井住友ファイナンス＆リース株式会社',
          textAlign: TextAlign.center,
          style: TextStyle(color: AppTheme.whiteColor, fontSize: 11, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 6),
        Obx(
          () => Text(
            controller.state.appVersion.value,
            textAlign: TextAlign.center,
            style: const TextStyle(color: AppTheme.whiteColor, fontSize: 11, fontWeight: FontWeight.w600),
          ),
        ),
        SizedBox(height: bottomPadding),
      ],
    );
  }

  /// 输入框构建
  Widget _buildInputField({required String label, required bool obscureText, required double inputElementHeight}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(color: AppTheme.whiteColor, fontSize: 15, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 5),
        SizedBox(
          width: double.infinity, // 宽度设置为父容器宽度
          child: TextField(
            obscureText: obscureText,
            // 是否隐藏输入内容
            focusNode: obscureText ? controller.focusNodePassWord : controller.focusNodeEmail,
            keyboardType: obscureText ? TextInputType.visiblePassword : TextInputType.emailAddress,
            autofillHints: [obscureText ? AutofillHints.password : AutofillHints.email],
            // 告诉输入类型
            style: const TextStyle(color: AppTheme.blackColor, fontSize: 15),
            textInputAction: obscureText ? TextInputAction.send : TextInputAction.next,
            // 设置回车为"下一个"
            onEditingComplete: () => controller.validationUserInput(isPassWordTextField: obscureText),
            controller: obscureText ? controller.controllerPassWord : controller.controllerEmail,
            decoration: InputDecoration(
              filled: true,
              fillColor: AppTheme.whiteColor,
              border: _inputBorderStyle(),
              focusedBorder: _inputBorderStyle(),
              enabledBorder: _inputBorderStyle(),
              isDense: true,
              contentPadding: EdgeInsets.symmetric(vertical: inputElementHeight / 4.5, horizontal: 13), // 使用统一的元素高度
            ),
          ),
        ),
      ],
    );
  }

  /// 生物验证显示
  Widget _buildBiometricsButton() {
    final isBiometrics = controller.isBiometrics.value;
    if (!isBiometrics) {
      return const SizedBox.shrink();
    }

    Widget image;
    if (controller.state.isFaceID.value) {
      image = Image.asset('assets/images/login/face.png', width: 30, height: 30);
    } else {
      image = Image.asset('assets/images/login/fingerprint.png', width: 30, height: 30);
    }
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(height: 30),
        GestureDetector(
          onTap: () {
            controller.onBiometricClick();
          },
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              color: AppTheme.whiteColor,
              borderRadius: BorderRadius.circular(8),
            ),
            alignment: Alignment.center,
            child: image,
          ),
        ),
      ],
    );
  }

  /// 按钮共通组件
  Widget _buttonCommon({
    required double buttonElementHeight,
    required String buttonText,
    required Color buttonBgColor,
    required Color textColor,
    required double fontSize,
    required VoidCallback onClick,
  }) {
    return SizedBox(
      width: double.infinity, // 宽度设置为父容器宽度
      child: OutlinedButton(
        onPressed: () => onClick(),
        style: OutlinedButton.styleFrom(
          fixedSize: Size(double.infinity, buttonElementHeight),
          backgroundColor: buttonBgColor,
          side: const BorderSide(color: AppTheme.whiteColor, width: 1.5),
          // 边框颜色和宽度
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          // 圆角
          padding: EdgeInsets.zero,
        ),
        child: Text(
          buttonText,
          style: TextStyle(fontSize: fontSize, color: textColor, fontWeight: FontWeight.w800),
        ),
      ),
    );
  }

  /// 提取输入框的边框样式
  InputBorder _inputBorderStyle() {
    return OutlineInputBorder(borderRadius: BorderRadius.circular(8.0), borderSide: BorderSide.none);
  }
}
