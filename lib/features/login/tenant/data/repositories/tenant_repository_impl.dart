import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/network/interceptors/response_interceptor.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart';

class TenantRepositoryImpl with RepositoryErrorHandler implements TenantRepository {
  final DioUtil dioUtil;

  TenantRepositoryImpl({required this.dioUtil});

  @override
  Future<GetTokenResultModel> loginTenant({
    required String ticket,
    required String tenantId,
    required bool isBiometrics,
    String? code,
  }) async {
    return executeRepositoryTask<GetTokenResultModel>(() async {
      final qpMap = {'ticket': ticket, 'tenantId': tenantId, 'biometricsFlg': isBiometrics ? '1' : '0'};
      if (code != null && code.isNotEmpty) {
        qpMap['code'] = code;
      }
      ResponseInterceptor.addSkipInterceptorPath(GlobalVariable.secureSignInTenant);
      final response = await dioUtil.get(GlobalVariable.secureSignInTenant, queryParams: qpMap);

      if (response.isSuccess()) {
        return GetTokenResultModel.fromJson(response.data);
      }
      throw SystemException();
    }, 'Failed to fetch loginTenant');
  }

  @override
  Future<GetTokenResultModel> getFullUserInfo() async {
    return executeRepositoryTask<GetTokenResultModel>(() async {
      ResponseInterceptor.addSkipInterceptorPath(GlobalVariable.getFullUserInfo);
      final response = await dioUtil.get(GlobalVariable.getFullUserInfo);

      if (response.isSuccess()) {
        return GetTokenResultModel.fromJson(response.data);
      }
      throw SystemException();
    }, 'Failed to fetch getFullUserInfo');
  }

  @override
  Future<GetTokenResultModel> getVerificationCode({
    required String ticket,
    required String countryCode,
    required String phone,
  }) async {
    return executeRepositoryTask<GetTokenResultModel>(() async {
      final ph = phone.replaceAll('-', '');
      final response = await dioUtil.get(
        GlobalVariable.getVerificationCode,
        queryParams: {'ticket': ticket, 'countryCode': countryCode, 'phone': ph},
      );

      if (response.isSuccess()) {
        return GetTokenResultModel.fromJson(response.data);
      }
      throw SystemException();
    }, 'Failed to fetch getVerificationCode');
  }

  @override
  Future<GetTokenResultModel> getUserBindPhone({
    required String ticket,
    required String countryCode,
    required String phone,
    required String userName,
    required String password,
    required String code,
    required String tenantId,
    required int userId,
  }) async {
    return executeRepositoryTask<GetTokenResultModel>(() async {
      ResponseInterceptor.addSkipInterceptorPath(GlobalVariable.userBindPhone);
      final response = await dioUtil.post(
        GlobalVariable.userBindPhone,
        data: {
          'userName': userName,
          'password': password,
          'code': code,
          'phone': phone,
          'countryCode': countryCode,
          'userId': userId,
          'tenantId': tenantId,
          'ticket': ticket,
        },
        useFormUrlEncoded: true,
      );
      if (response.isSuccess()) {
        return GetTokenResultModel.fromJson(response.data);
      }
      throw SystemException();
    }, 'Failed to fetch getUserBindPhone');
  }

  @override
  Future<GetTokenResultModel> getSendMfaMailVerifyCode({required String ticket, required String email}) async {
    return executeRepositoryTask<GetTokenResultModel>(() async {
      ResponseInterceptor.addSkipInterceptorPath(GlobalVariable.getEmailVerificationCode);

      final response = await dioUtil.get(
        GlobalVariable.getEmailVerificationCode,
        queryParams: {'ticket': ticket, 'userName': email},
      );

      if (response.isSuccess()) {
        return GetTokenResultModel.fromJson(response.data);
      }
      throw SystemException();
    }, 'Failed to fetch getSendMfaMailVerifyCode');
  }
}
