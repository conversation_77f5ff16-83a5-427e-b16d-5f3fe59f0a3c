import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';

abstract class TenantRepository {
  /// 获取token接口
  /// * ticket login 中接口返回参数
  /// * tenantId 用户选中的tenant
  /// * isBiometrics 是否为生物验证方式登录
  ///     - true 是生物验证
  ///     - false 不是生物验证
  Future<GetTokenResultModel> loginTenant({
    required String ticket,
    required String tenantId,
    required bool isBiometrics,
    String? code,
  });

  /// Sso专用
  Future<GetTokenResultModel> getFullUserInfo();

  /// 校验手机号以及发送短信是否可以使用
  /// * [countryCode] 国际区号
  /// * [phone] 手机号
  /// * [ticket] 用户临时标志符
  Future<GetTokenResultModel> getVerificationCode({
    required String ticket,
    required String countryCode,
    required String phone,
  });

  /// 发送email验证码
  /// * [email] 用户邮箱
  /// * [ticket] 用户临时标志符
  Future<GetTokenResultModel> getSendMfaMailVerifyCode({required String ticket, required String email});

  /// sms专用
  /// * [countryCode] 国际区号
  /// * [phone] 手机号
  /// * [ticket] 用户临时标志符
  /// * [userName] 账户
  /// * [password] 密码
  /// * [code] 短信验证吗
  /// * [tenantId] 租户Id
  /// * [userId] 用户Id
  Future<GetTokenResultModel> getUserBindPhone({
    required String ticket,
    required String countryCode,
    required String phone,
    required String userName,
    required String password,
    required String code,
    required String tenantId,
    required int userId,
  });
}
