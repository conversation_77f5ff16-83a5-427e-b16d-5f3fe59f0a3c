import 'package:asset_force_mobile_v2/core/env/env_helper_impl.dart';
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/features/login/login_sso/domain/usecases/login_sso_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/map_token_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';

/// deeplink登录使用的Usecase
class DeepLinkUseCase extends UserInfoHandleBaseUseCase {
  final LoginSsoUseCase? loginSsoUseCase;
  final IEnvHelper envHelper;
  final IStorageUtils storageUtils;

  DeepLinkUseCase({
    required this.loginSsoUseCase,
    required super.tenantRepository,
    required super.userRepository,
    required this.storageUtils,
    IEnvHelper? envHelper,
  }) : envHelper = envHelper ?? EnvHelperImpl(),
       super(storageUtils: storageUtils);

  @override
  Future<GetTokenResultModel> getBasicInfo(SelectTenantModel params) async {
    // DeepLink业务专用
    final DealDeeplinkModel? dealDeeplink = params.dealDeeplink;
    final String? token = dealDeeplink?.token;
    if (token == null || token.isEmpty) {
      throw SystemException();
    }
    await storageUtils.setValue<String>(StorageKeys.token, token);
    // 由于下面的接口需要token所以需要提前存入
    final GetTokenResultModel result = await tenantRepository.getFullUserInfo();
    result.mapToken = MapTokenModel(accessToken: token);
    final String appModel = envHelper.getEnvironment();
    // 为下一次用户进入登录页面后，直接变为sso二次登陆作为依据判断
    final String tenantId = result.tenant?.tenantId ?? '';
    final String tenantName = result.tenant?.tenantName ?? '';
    final String zoneId = params.zoneId;
    // 取出上一次sso登录数据
    final String? ssoUrl = storageUtils.getValue<String>(appModel + StorageKeys.ssoTenantUrl + tenantId + zoneId);
    if (ssoUrl != null && ssoUrl.isNotEmpty) {
      // 取出所有登录过的sso登录数据Map<String, String>
      final String? ssoObjStr = storageUtils.getValue<String>(appModel + StorageKeys.ssoTenantUrl);
      final TenantSsoModel setTenantSso = TenantSsoModel(
        tenantId: tenantId,
        tenantName: tenantName,
        ssoUrl: ssoUrl,
        zoneId: zoneId,
      );

      if (loginSsoUseCase != null) {
        final List<TenantSsoModel> dataList = loginSsoUseCase!.fromJsonList(jsonString: ssoObjStr);
        dataList.add(setTenantSso);
        await storageUtils.setValue<String>(
          appModel + StorageKeys.ssoTenantUrl,
          loginSsoUseCase!.processOrderTenants(tenants: dataList),
        );
      }
    }
    return result;
  }
}

class TenantSsoModel {
  final String tenantId;
  final String tenantName;
  final String ssoUrl;
  final String zoneId;

  TenantSsoModel({required this.tenantId, required this.tenantName, required this.ssoUrl, required this.zoneId});

  // 从 JSON 创建 TenantSsoModel 实例
  factory TenantSsoModel.fromJson(Map<String, dynamic> json) {
    return TenantSsoModel(
      tenantId: json['tenantId'],
      tenantName: json['tenantName'],
      ssoUrl: json['ssoUrl'],
      zoneId: json['zoneId'],
    );
  }

  // 将 TenantSsoModel 转换为 JSON
  Map<String, dynamic> toJson() {
    return {'tenantId': tenantId, 'tenantName': tenantName, 'ssoUrl': ssoUrl, 'zoneId': zoneId};
  }
}
