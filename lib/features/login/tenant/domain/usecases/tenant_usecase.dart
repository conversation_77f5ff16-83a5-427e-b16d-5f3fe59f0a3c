import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/user_info_handle_base_usecase.dart';

class TenantUseCase extends UserInfoHandleBaseUseCase {
  TenantUseCase({
    required super.tenantRepository,
    required super.userRepository,
    required super.storageUtils,
    super.envHelper,
  });

  @override
  Future<GetTokenResultModel> getBasicInfo(SelectTenantModel params) async {
    final DealTenantModel? dealTenant = params.dealTenant;
    if (dealTenant == null) {
      throw SystemException();
    }
    final String ticket = dealTenant.ticket;
    final String tenantId = dealTenant.tenantId;
    final String code = dealTenant.code;
    final bool isBiometrics = dealTenant.isBiometrics;

    // 验证必需参数
    if (ticket.isEmpty) {
      throw SystemException();
    }
    if (tenantId.isEmpty) {
      throw SystemException();
    }

    // 普通登录之后tenant选择后
    final GetTokenResultModel result = await tenantRepository.loginTenant(
      ticket: ticket,
      tenantId: tenantId,
      isBiometrics: isBiometrics,
      code: code,
    );

    return result;
  }
}
