import 'dart:convert';

import 'package:asset_force_mobile_v2/core/env/env_helper_impl.dart';
import 'package:asset_force_mobile_v2/core/env/i_env_helper.dart';
import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_keys.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/google_location_utils.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/dpp_info_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/get_token_result_model.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/map_token_model.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_tenant_use_status_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_user_tenant_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:collection/collection.dart';

/// 账户到期
const userApiAccountExpiration = 'assetforceをご利用いただきありがとうございます。\n有効期限が過ぎているためご利用になれません。\nテナント管理者へご連絡お願いします。';
const userShowAccountExpiration = '有効期限が過ぎました。\nテナント管理者へご連絡お願いします。';

enum LoginCodeStatus {
  /// 正常登录 0
  codeNormal(0),

  /// 二重认证-新规手机号 3
  codeNewSms(3),

  /// 二重认证-手机号 4
  codeSms(4),

  /// 二重认证-邮箱 6
  codeEmail(6),

  /// 重置密码 7158
  codeResetPassword(7158);

  final int value;

  const LoginCodeStatus(this.value);

  bool equals(int? otherValue) {
    return value == otherValue;
  }

  static bool contains({required List<LoginCodeStatus> codeEnumList, required int? value}) {
    return codeEnumList.firstWhereOrNull((t) => t.value == value) != null;
  }
}

/// 选中的租户
class SelectTenantModel {
  final String zoneId;
  final DealDeeplinkModel? dealDeeplink;
  final DealTenantModel? dealTenant;
  final OptTenantModel? optTenant;
  SelectTenantModel({
    required this.zoneId,
    // 如果不需要可以传null
    required this.dealTenant,
    // 如果不需要可以传null
    required this.dealDeeplink,
    // 如果不需要可以传null
    required this.optTenant,
  });
}

class OptTenantModel {
  final String nationCode;
  final String tel;
  final String ticket;
  final int reCode;
  final int userId;
  final String userName;
  final String password;
  final String tenantId;
  final String email;
  String code;
  OptTenantModel({
    required this.nationCode,
    required this.tel,
    required this.ticket,
    required this.reCode,
    required this.userId,
    required this.userName,
    required this.password,
    required this.tenantId,
    this.code = '',
    required this.email,
  });
  Map<String, dynamic> toJson() {
    return {
      'nationCode': nationCode,
      'tel': tel,
      'ticket': ticket,
      'reCode': reCode,
      'userId': userId,
      'userName': userName,
      'password': password,
      'tenantId': tenantId,
      'code': code,
      'email': email,
    };
  }

  factory OptTenantModel.fromJson(Map<String, dynamic> json) {
    return OptTenantModel(
      nationCode: json['nationCode'] ?? '',
      tel: json['tel'] ?? '',
      ticket: json['ticket'] ?? '',
      reCode: json['reCode'] ?? 0,
      userId: json['userId'] ?? 0,
      userName: json['userName'] ?? '',
      password: json['password'] ?? '',
      tenantId: json['tenantId'] ?? '',
      code: json['code'] ?? '',
      email: json['email'] ?? '',
    );
  }
}

class DealDeeplinkModel {
  final String token;
  DealDeeplinkModel({required this.token});
}

class DealTenantModel {
  final String ticket;
  final String tenantId;
  final bool isBiometrics;
  String code;
  DealTenantModel({required this.ticket, required this.tenantId, required this.isBiometrics, this.code = ''});
}

/// 从上一个页面传进来的
class TenantParams {
  List<SharedTenantModel> tenants;
  final bool isBiometrics;
  final String ticket;
  final String password;
  final String userName;
  TenantParams({
    required this.tenants,
    required this.isBiometrics,
    required this.ticket,
    required this.password,
    required this.userName,
  });
}

abstract class UserInfoHandleBaseUseCase implements UseCase<GetTokenResultModel, SelectTenantModel> {
  final TenantRepository tenantRepository;
  final UserRepository userRepository;
  final IEnvHelper envHelper;
  final IStorageUtils storageUtils;

  UserInfoHandleBaseUseCase({
    required this.tenantRepository,
    required this.userRepository,
    required this.storageUtils,
    IEnvHelper? envHelper,
  }) : envHelper = envHelper ?? EnvHelperImpl();

  /// 继承实现
  Future<GetTokenResultModel> getBasicInfo(SelectTenantModel params);

  @override
  Future<GetTokenResultModel> call(SelectTenantModel params) async {
    final String zoneId = params.zoneId;
    await storageUtils.setValue<String>(StorageKeys.zoneId, zoneId);
    await envHelper.setHost(zoneId);
    final GetTokenResultModel result = await getBasicInfo(params);

    switch (result.code) {
      case 0: // 正常登录
        if (result.policy != null) {
          // 密码过期后重置密码后台没有制定特殊的code，移动端特殊制定
          result.code = LoginCodeStatus.codeResetPassword.value;
          return result;
        }
        await _saveStorageUtils(result: result);
        await _getUploadFileSizeMax();
        return result;
      case 3: // SMS 用户的这个tenant开启了双重验证，但是没有绑定双重认证信息
      case 4: // SMS 用户的这个tenant开启了双重验证，开始双重认证校验
      case 6: // Email 用户的这个tenant开启了双重验证，开始双重认证校验
        return result;
      default: // 错误
        final String? signInResult = result.signInResult;
        // 后台code没有传过来0，并且没有传过来错误信息
        if (signInResult == null || signInResult.isEmpty) {
          throw SystemException();
        }
        // 显示后台给出的错误
        if (result.code == 1) {
          throw BusinessException(signInResult);
        }
        // 用户过期
        if (result.signInResult == userApiAccountExpiration) {
          throw BusinessException(userShowAccountExpiration);
        }
        throw SystemException();
    }
  }

  Future<void> _saveStorageUtils({required GetTokenResultModel result}) async {
    final MapTokenModel? mapToken = result.mapToken;

    if (mapToken == null) {
      throw SystemException();
    }
    final String? token = mapToken.accessToken;
    if (token == null || token.isEmpty) {
      throw SystemException();
    }
    final SharedUserModel? user = result.user;
    if (user == null) {
      throw SystemException();
    }

    final String refreshToken = mapToken.refreshToken ?? '';
    final int userId = user.userId ?? 0;
    final String userName = user.userName ?? '';
    final String firstName = user.firstName ?? '';
    final String lastName = user.lastName ?? '';
    final String tel = user.tel ?? '';
    final String nationCode = user.nationCode ?? '';
    final String location = user.location ?? '';
    final String tenantId = user.tenantId ?? '';

    final bool tenantEnableTwoStep = result.tenantEnableTwoStep ?? false;
    final String printEnableFlg = result.printEnableFlg ?? '';
    final String barcodeExtraction = result.barcodeExtraction ?? '';
    if (tenantId.isEmpty) {
      throw SystemException();
    }
    if (tenantId == 'TENANT_ADMIN') {
      throw BusinessException('モバイルアプリでは「テナント管理者用メニュー」にログインすることは出来ません。ウェブブラウザからのログインをお願いいたします。');
    }
    await storageUtils.setValue<String>(StorageKeys.tenantId, tenantId);
    await storageUtils.setValue<String>(StorageKeys.token, token);
    await storageUtils.setValue<String>(StorageKeys.refreshToken, refreshToken);
    await storageUtils.setValue<int>(StorageKeys.userId, userId);
    await storageUtils.setValue<String>(StorageKeys.userName, userName);

    await storageUtils.setValue<String>(StorageKeys.firstName, firstName);
    await storageUtils.setValue<String>(StorageKeys.lastName, lastName);
    await storageUtils.setValue<bool>(StorageKeys.enableTwoStep, tenantEnableTwoStep);

    await storageUtils.setValue<String>(StorageKeys.tel + userName, tel);
    await storageUtils.setValue<String>(StorageKeys.nationCode + userName, nationCode);
    await storageUtils.setValue<String>(StorageKeys.printEnableFlg, printEnableFlg);
    await storageUtils.setValue<String>(StorageKeys.barcodeExtraction, barcodeExtraction);
    final DppInfoModel? dppInfo = result.dppInfo;
    if (dppInfo != null) {
      final String dppInfoStr = jsonEncode(dppInfo.toJson());
      await storageUtils.setValue<String>(StorageKeys.ocrInfo, dppInfoStr);
    }
    // 物理层面获取地理坐标并写入到Storage （获取 方式 StorageUtils.keyLocation）
    GoogleLocationService().toSetLocation();
    // 从后台获取到的场所信息储存 （获取 方式 userName + StorageUtils.keyLocation）
    await storageUtils.setAssetScanLocation(assetLocation: location);
  }

  Future<void> _getUploadFileSizeMax() async {
    final SharedUserTenantModel tenantInfo = await userRepository.getUserTenant();
    final SharedTenantUseStatusModel? tenantUseStates = tenantInfo.tenantUseStatesList?.firstOrNull;
    final String? fieldText = tenantUseStates?.fieldText;
    if (fieldText == null || fieldText.isEmpty) {
      return;
    }
    final Map<String, dynamic> fieldTextObj = jsonDecode(fieldText);
    final int maxFileSizeInM = int.tryParse(fieldTextObj['uploadFileSizeMax']) ?? 0;
    if (maxFileSizeInM > 0) {
      await storageUtils.setValue<int>(StorageKeys.selectFileMaxSize, maxFileSizeInM);
    }
  }
}
