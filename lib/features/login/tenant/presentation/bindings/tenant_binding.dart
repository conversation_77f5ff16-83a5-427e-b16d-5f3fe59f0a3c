import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_interface.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/data/repositories/tenant_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/repositories/tenant_repository.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/domain/usecases/tenant_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/presentation/controllers/tenant_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:get/get.dart';

class TenantBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<TenantRepository>(() => TenantRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    Get.lazyPut<TenantUseCase>(
      () => TenantUseCase(
        tenantRepository: Get.find<TenantRepository>(),
        userRepository: Get.find<UserRepository>(),
        storageUtils: Get.find<IStorageUtils>(),
      ),
    );

    Get.lazyPut(
      () => TenantController(
        tenantUseCase: Get.find<TenantUseCase>(),
        navigationService: Get.find<NavigationService>(),
        dialogService: Get.find<DialogService>(),
      ),
    );
  }
}
