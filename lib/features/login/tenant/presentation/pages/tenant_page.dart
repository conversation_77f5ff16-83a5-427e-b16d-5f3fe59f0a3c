import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/presentation/bindings/tenant_binding.dart';
import 'package:asset_force_mobile_v2/features/login/tenant/presentation/controllers/tenant_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/login/tenant', binding: TenantBinding)
class TenantPage extends GetView<TenantController> {
  const TenantPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main Scaffold
        Scaffold(
          appBar: AppBar(
            title: Obx(
              () => Text(
                controller.tenantTitle.value,
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: AppTheme.whiteColor),
              ),
            ),
            centerTitle: true,
            backgroundColor: AppTheme.darkBlueColor,
            leading: IconButton(
              icon: const Icon(Icons.chevron_left, color: AppTheme.whiteColor),
              onPressed: () => Get.back(),
            ),
          ),
          body: Obx(() {
            return ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
              itemCount: controller.tenantsList.length,
              itemBuilder: (context, index) {
                final tenant = controller.tenantsList[index];
                return GestureDetector(
                  onTap: () => controller.onTenantClick(tenant),
                  child: Container(
                    height: 45,
                    padding: const EdgeInsets.only(left: 15, right: 15),
                    margin: const EdgeInsets.only(bottom: 10),
                    decoration: BoxDecoration(
                      color: AppTheme.whiteTranslucentColor, // Replaced withAlpha (0–255)
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          tenant.tenantName ?? '',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.black87Color,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        const Icon(Icons.arrow_forward_ios, size: 14, color: AppTheme.blackColor),
                      ],
                    ),
                  ),
                );
              },
            );
          }),
        ),
      ],
    );
  }
}
