import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'master_detail_search.g.dart';

@JsonSerializable()
class MasterDetailSearch {
  String? msg;
  int? code;
  List<MasterDetailSearchMasterDetail?>? masterDetail;
  bool? moreThanLimit;

  MasterDetailSearch(this.msg, this.code, this.masterDetail, this.moreThanLimit);

  factory MasterDetailSearch.fromJson(Map<String, dynamic> json) => _$MasterDetailSearchFromJson(json);

  Map<String, dynamic> toJson() => _$MasterDetailSearchToJson(this);
}

@JsonSerializable()
class MasterDetailSearchMasterDetail {
  int? masterId;
  String? tenantId;
  int? masterTypeId;
  String? masterText;
  int? detailSort;
  String? createdById;
  String? createdDate;
  String? modifiedById;
  String? modifiedDate;
  dynamic state;
  dynamic count;
  dynamic hiddenColumn;
  dynamic isHidden;
  dynamic excelLineNum;

  MasterDetailSearchMasterDetail(
    this.masterId,
    this.tenantId,
    this.masterTypeId,
    this.masterText,
    this.detailSort,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.state,
    this.count,
    this.hiddenColumn,
    this.isHidden,
    this.excelLineNum,
  );

  /// 获取masterText的map
  Map<String, dynamic> getMasterTextMap() {
    return jsonDecode(masterText ?? '{}');
  }

  factory MasterDetailSearchMasterDetail.fromJson(Map<String, dynamic> json) =>
      _$MasterDetailSearchMasterDetailFromJson(json);

  Map<String, dynamic> toJson() => _$MasterDetailSearchMasterDetailToJson(this);
}
