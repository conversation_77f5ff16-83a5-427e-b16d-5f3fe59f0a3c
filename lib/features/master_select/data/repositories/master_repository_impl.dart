import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/master_select/data/models/master_detail_search.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/repositories/master_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/master_detail_model.dart';

class MasterRepositoryImpl with RepositoryErrorHandler implements MasterRepository {
  final DioUtil dioUtil;

  MasterRepositoryImpl({required this.dioUtil});

  @override
  Future<List<MasterDetailSearchMasterDetail>> getMasterItems({
    required String masterTypeId,
    bool visibleOnly = true,
  }) async {
    try {
      final response = await dioUtil.post(
        GlobalVariable.getMasterDetailSearch,
        data: {'masterTypeId': masterTypeId, 'visibleOnly': visibleOnly},
      );
      final masterDetailSearch = MasterDetailSearch.fromJson(response.data);
      return masterDetailSearch.masterDetail?.whereType<MasterDetailSearchMasterDetail>().toList() ?? [];
    } catch (e) {
      throw Exception('获取主数据请求异常: $e');
    }
  }

  @override
  Future<List<MasterDetailModel>> getMasterInfoById(int masterTypeId) {
    return executeRepositoryTask<List<MasterDetailModel>>(() async {
      final response = await dioUtil.get(
        GlobalVariable.actionGetMasterInfoById,
        queryParams: {'masterTypeId': masterTypeId},
      );

      // 初始化主数据列表
      var masterInfo = <MasterDetailModel>[];
      final data = response.data;

      // 检查响应数据结构并进行类型安全的转换
      if (data != null && data['masterDetail'] != null) {
        final masterDetailList = data['masterDetail'];

        // 确保 masterDetail 是 List 类型
        if (masterDetailList is List) {
          // 将 List<dynamic> 转换为 List<MasterDetailModel>
          masterInfo = masterDetailList
              .where((item) => item != null) // 过滤空值
              .map((item) => MasterDetailModel.fromJson(item as Map<String, dynamic>))
              .toList();
        }
      }

      return masterInfo;
    }, 'Failed to get master info by id');
  }
}
