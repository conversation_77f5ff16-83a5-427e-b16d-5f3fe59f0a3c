import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/master_select/data/repositories/master_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/models/master_select_params.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/repositories/master_repository.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/usecases/get_master_items_usecase.dart';
import 'package:asset_force_mobile_v2/features/master_select/domain/usecases/get_master_layout_settings_usecase.dart';
import 'package:asset_force_mobile_v2/features/master_select/presentation/controllers/master_select_controller.dart';
import 'package:get/get.dart';

class MasterSelectBinding extends Bindings {
  @override
  void dependencies() {
    // 如果 Dio 实例已经被注册，使用已有实例
    final dio = Get.find<DioUtil>();

    // 注册仓库
    Get.lazyPut<AssetRepository>(() => AssetRepositoryImpl(dioUtil: dio));

    Get.lazyPut<MasterRepository>(() => MasterRepositoryImpl(dioUtil: dio));

    // 注册用例
    Get.lazyPut<GetMasterItemsUseCase>(() => GetMasterItemsUseCase(Get.find<MasterRepository>()));

    Get.lazyPut<GetMasterLayoutSettingsUseCase>(
      () => GetMasterLayoutSettingsUseCase(assetRepository: Get.find<AssetRepository>()),
    );

    final params = Get.arguments as MasterSelectParams;

    // 注册控制器
    Get.lazyPut<MasterSelectController>(
      () => MasterSelectController(
        getMasterItemsUsecase: Get.find<GetMasterItemsUseCase>(),
        getMasterLayoutSettingsUsecase: Get.find<GetMasterLayoutSettingsUseCase>(),
        params: params,
      ),
    );
  }
}
