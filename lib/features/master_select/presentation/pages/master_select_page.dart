import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/master_select/presentation/binding/master_select_binding.dart';
import 'package:asset_force_mobile_v2/features/master_select/presentation/controllers/master_select_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

/// MasterSelectPage 是一个用于选择主数据项的页面组件
///
/// 该页面包含以下功能:
/// - 显示主数据列表
/// - 支持搜索过滤
/// - 点击选择具体项目
/// - 错误重试机制
///
/// 参数通过 Get.arguments 传递，包含:
/// - masterType: 主数据类型
/// - title: 页面标题
/// - autoLoad: 是否自动加载数据(可选)
@GetRoutePage('/master_select', bindings: [MasterSelectBinding])
class MasterSelectPage extends GetWidget<MasterSelectController> {
  const MasterSelectPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: _buildAppBar(), body: _buildBody());
  }

  /// 构建应用栏组件
  ///
  /// 包含:
  /// - 返回按钮
  /// - 页面标题
  /// - 搜索框
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      leadingWidth: 50,
      leading: _BackButton(onPressed: () => Get.back()),
      title: Obx(() => Text(controller.title.value)),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(40),
        child: _SearchField(onChanged: controller.searchMasterItems),
      ),
    );
  }

  /// 构建页面主体内容
  ///
  /// 根据加载状态显示:
  /// - 加载指示器
  /// - 空数据提示
  /// - 主数据列表
  Widget _buildBody() {
    return Column(
      children: [
        Expanded(
          child: Obx(() {
            if (controller.isLoading.value) {
              return const Center(child: CircularProgressIndicator());
            }

            if (controller.displayList.isEmpty) {
              return const _EmptyView();
            }

            return _MasterList(getItemName: controller.getFormattedItemName, onItemTap: controller.onItemSelected);
          }),
        ),
      ],
    );
  }
}

/// 返回按钮组件
///
/// 用于导航返回上一页面
class _BackButton extends StatelessWidget {
  /// 点击回调函数
  final VoidCallback onPressed;

  const _BackButton({required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return IconButton(icon: const Icon(Icons.arrow_back_ios_new, size: 20), onPressed: onPressed);
  }
}

/// 搜索框组件
///
/// 用于过滤主数据列表
class _SearchField extends GetWidget<MasterSelectController> {
  /// 搜索文本变化回调
  final ValueChanged<String> onChanged;

  const _SearchField({required this.onChanged});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        left: MasterSelectController.searchFieldPadding,
        right: MasterSelectController.searchFieldPadding,
        bottom: MasterSelectController.searchFieldPadding,
      ),
      child: Obx(
        () => TextField(
          cursorHeight: MasterSelectController.searchFieldCursorHeight,
          style: const TextStyle(fontSize: MasterSelectController.searchFieldFontSize),
          textAlignVertical: TextAlignVertical.center,
          decoration: InputDecoration(
            hintText: controller.title.value,
            prefixIcon: const Icon(Icons.search, size: MasterSelectController.searchFieldIconSize),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(MasterSelectController.searchFieldBorderRadius),
              borderSide: const BorderSide(color: AppTheme.whiteColor),
            ),
            filled: true,
            fillColor: AppTheme.whiteColor,
            contentPadding: MasterSelectController.searchFieldContentPadding,
            isDense: true,
            prefixIconConstraints: MasterSelectController.searchFieldIconConstraints,
            alignLabelWithHint: true,
          ),
          onChanged: onChanged,
        ),
      ),
    );
  }
}

/// 空数据提示组件
///
/// 当没有数据时显示提示信息
class _EmptyView extends GetWidget<MasterSelectController> {
  const _EmptyView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        MasterSelectController.emptyDataText,
        style: TextStyle(fontSize: MasterSelectController.emptyTextFontSize, color: AppTheme.whiteColor),
      ),
    );
  }
}

/// 主数据列表组件
///
/// 用于显示主数据项的列表视图
class _MasterList extends GetWidget<MasterSelectController> {
  /// 获取项目名称的回调函数
  final String Function(int) getItemName;

  /// 项目点击回调函数
  final Function(int) onItemTap;

  const _MasterList({required this.getItemName, required this.onItemTap});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => ListView.builder(
        key: ValueKey('master_list_${controller.level.value}'),
        itemCount: controller.displayList.length,
        itemBuilder: (context, index) {
          return Obx(() {
            final displayList = controller.displayList[index];
            return _MasterListItem(
              name: displayList.itemDisplayValue,
              isFirst: index == 0,
              isLast: index == controller.displayList.length - 1,
              onTap: () => onItemTap(index),
            );
          });
        },
      ),
    );
  }
}

/// 主数据列表项组件
///
/// 用于显示单个主数据项的UI组件
class _MasterListItem extends GetWidget<MasterSelectController> {
  /// 项目名称
  final String name;

  /// 是否为第一项
  final bool isFirst;

  /// 是否为最后一项
  final bool isLast;

  /// 点击回调函数
  final VoidCallback onTap;

  const _MasterListItem({required this.name, required this.isFirst, required this.isLast, required this.onTap});

  /// 构建列表项容器
  ///
  /// [title] 显示的文本
  /// [onTap] 点击回调
  /// [hasBackButton] 是否显示返回按钮
  /// [onBack] 返回上一级回调
  Widget _buildItemContainer(
    String title,
    VoidCallback onTap, {
    VoidCallback? onBack,
    double top = MasterSelectController.listItemSpacing,
    double bottom = MasterSelectController.listItemSpacing,
  }) {
    return Container(
      margin: EdgeInsets.only(
        left: MasterSelectController.listItemMargin,
        right: MasterSelectController.listItemMargin,
        top: top,
        bottom: bottom,
      ),
      decoration: BoxDecoration(
        color: AppTheme.whiteColor,
        borderRadius: BorderRadius.circular(MasterSelectController.listItemBorderRadius),
      ),
      child: ListTile(
        dense: true,
        visualDensity: VisualDensity.compact,
        title: Text(title, style: const TextStyle(fontSize: MasterSelectController.listItemFontSize)),
        contentPadding: MasterSelectController.listItemPadding,
        style: ListTileStyle.list,
        onTap: title == MasterSelectController.backButtonText ? onBack : onTap,
        horizontalTitleGap: 0,
        leading: title == MasterSelectController.backButtonText
            ? const Icon(Icons.arrow_back_ios, size: MasterSelectController.listItemIconSize)
            : null,
        trailing: title != MasterSelectController.backButtonText
            ? const Icon(Icons.arrow_forward_ios, size: MasterSelectController.listItemIconSize)
            : null,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final hasBackButton = controller.level.value > 0 && isFirst;
      return Column(
        children: [
          if (hasBackButton)
            _buildItemContainer(
              MasterSelectController.backButtonText,
              onTap,
              onBack: () => controller.navigateToPreviousLevel(),
              top: MasterSelectController.listItemFirstSpacing,
              bottom: 0,
            ),
          _buildItemContainer(
            name,
            onTap,
            top: hasBackButton ? MasterSelectController.listItemSpacing : MasterSelectController.listItemFirstSpacing,
            bottom: isLast ? MasterSelectController.listItemSpacing : 0,
          ),
          if (isLast)
            const Padding(
              padding: MasterSelectController.bottomTextPadding,
              child: Text(
                MasterSelectController.allItemsDisplayedText,
                style: TextStyle(fontSize: MasterSelectController.bottomTextFontSize, color: AppTheme.whiteColor),
              ),
            ),
        ],
      );
    });
  }
}
