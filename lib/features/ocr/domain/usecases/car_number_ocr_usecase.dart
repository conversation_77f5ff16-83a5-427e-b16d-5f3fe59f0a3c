import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/ocr/domain/repositories/car_number_ocr_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_get_selected_asset_list_response.dart';

class CarNumberOcrReModel {
  final int? assetActionId;
  final List<String>? ocrIdentityCodeList;
  CarNumberOcrReModel({required this.assetActionId, required this.ocrIdentityCodeList});
}

/// OCR处理结果
class CarNumberOcrResult {
  /// 错误信息映射 (错误类型 -> 错误的资产列表)
  final Map<String, List<String>> errors;

  /// 验证成功的结果
  final List<SharedGetSelectedAssetListResponse> validResults;

  /// 是否有错误
  bool get hasErrors => errors.values.any((list) => list.isNotEmpty);

  /// 是否有可处理的结果
  bool get isAbleToProcess => validResults.isNotEmpty;

  CarNumberOcrResult({required this.errors, required this.validResults});
}

enum AiocrErrorEnum {
  /// 資産が未登録の場合
  errorNotExists('ERROR_NOT_EXISTS', 99, 'unregistered', '未登録の資産があります\n{assets}\n再度スキャンするか、資産番号を編集してください。'),

  /// 権限なしの場合
  errorAuthorityForbidden('ERROR_AUTHORITY_FORBIDDEN', 99, 'authorityError', '以下の資産を閲覧する権限がありません\n{assets}'),

  /// 場所が渡されない、且つbarcodeで複数資産を抽出された場合
  errorMultiAsset('MULTI_ASSET', 99, 'multipleMatched', '以下のナンバープレートが複数存在しています\n{assets}'),

  /// 処理の情報取得
  errorAssetAction('ERROR_ASSET_ACTION', 99, 'assetAction', 'アクション処理でエラーが発生しました\n{assets}'),

  /// 資産種類が異なる場合
  errorAssetType('ERROR_ASSET_TYPE', 99, 'assetType', '資産種類が異なります\n{assets}'),

  /// CONDITIONが異なる場合
  errorCondition('ERROR_CONDITION', 99, 'condition', '条件が異なります\n{assets}'),

  /// パラメータは不正です
  errorInputCarNum('パラメータは不正です', 99, 'inputCarNum', 'パラメータが不正です\n{assets}'),

  /// APIが異なる場合
  errorApiSystem('SYSTEM_ERROR', 99, '', ''),
  errorSystem('システムエラーが発生しました。管理者にご連絡ください。', 1, '', '');

  /// 错误标识符
  final String errorFid;

  /// 错误代码
  final int errorCodeFid;

  /// 错误类型键
  final String errorTypeKey;

  /// 错误消息模板
  final String messageTemplate;

  /// 构造函数
  const AiocrErrorEnum(this.errorFid, this.errorCodeFid, this.errorTypeKey, this.messageTemplate);

  /// 判断是否相等
  bool equals(String? otherValue) => errorFid == otherValue;

  /// 获取所有用户级错误的映射
  static Map<String, String> get userErrorMap {
    return Map.fromEntries(
      AiocrErrorEnum.values.where((e) => e.errorTypeKey.isNotEmpty).map((e) => MapEntry(e.errorFid, e.errorTypeKey)),
    );
  }

  /// 构建错误消息列表
  static List<String> buildErrorMessages(Map<String, List<String>> errors) {
    final errorMessages = <String>[];

    for (final errorEnum in AiocrErrorEnum.values) {
      if (errorEnum.errorTypeKey.isNotEmpty) {
        final assets = errors[errorEnum.errorTypeKey]!;
        if (assets.isNotEmpty) {
          errorMessages.add(errorEnum.messageTemplate.replaceAll('{assets}', assets.join('\n')));
        }
      }
    }

    return errorMessages;
  }
}

class CarNumberOcrUsecase extends UseCase<CarNumberOcrResult, CarNumberOcrReModel> {
  final CarNumberOcrRepository repository;
  CarNumberOcrUsecase({required this.repository});

  @override
  Future<CarNumberOcrResult> call(CarNumberOcrReModel params) async {
    final int? assetActionId = params.assetActionId;
    if (assetActionId == null) {
      throw SystemException();
    }
    final List<String>? ocrIdentityCodeList = params.ocrIdentityCodeList;
    if (ocrIdentityCodeList == null || ocrIdentityCodeList.isEmpty) {
      throw BusinessException(AiocrErrorEnum.errorInputCarNum.errorFid);
    }

    // 检查是否包含空字符串或只有空白字符的字符串
    final bool hasEmptyOrBlankStrings = ocrIdentityCodeList.any((code) => code.trim().isEmpty);
    if (hasEmptyOrBlankStrings) {
      throw BusinessException(AiocrErrorEnum.errorInputCarNum.errorFid);
    }

    // 清理字符串：移除特殊字符
    final regex = RegExp(r'[-ー･・\s]');
    final List<String> cleanedCodes = ocrIdentityCodeList.map((code) => code.replaceAll(regex, '')).toList();

    // 再次校验清理后的字符串是否为空
    final bool hasEmptyAfterCleaning = cleanedCodes.any((code) => code.isEmpty);
    if (hasEmptyAfterCleaning) {
      throw BusinessException(AiocrErrorEnum.errorInputCarNum.errorFid);
    }

    final List<Future<SharedGetSelectedAssetListResponse>> futures = cleanedCodes
        .map<Future<SharedGetSelectedAssetListResponse>>((cleanedCode) {
          return repository
              .getActionAssetVerify(assetActionId: assetActionId, ocrIdentityCode: cleanedCode)
              .then<SharedGetSelectedAssetListResponse>((res) => res);
        })
        .toList();

    final List<SharedGetSelectedAssetListResponse> responses = await Future.wait(futures);

    // 处理OCR结果并返回
    return _processOcrResults(responses, cleanedCodes);
  }

  /// 处理OCR结果并返回分类结果
  CarNumberOcrResult _processOcrResults(
    List<SharedGetSelectedAssetListResponse> responses,
    List<String> ocrIdentityCode,
  ) {
    final errorTypeMap = AiocrErrorEnum.userErrorMap;
    final Map<String, List<String>> errors = {};
    final List<SharedGetSelectedAssetListResponse> validResults = [];

    // 初始化错误列表
    for (String errorType in errorTypeMap.values) {
      errors[errorType] = [];
    }

    for (int i = 0; i < responses.length; i++) {
      final response = responses[i];
      final identityCode = ocrIdentityCode[i];

      // 处理API级别错误（非200状态码等）
      if (response.code != 0) {
        throw SystemException();
      }

      final errorCode = response.errorCode;

      if (errorCode != null && errorCode.isNotEmpty) {
        // 系统级错误直接抛异常
        if (AiocrErrorEnum.errorApiSystem.equals(errorCode) || AiocrErrorEnum.errorSystem.equals(errorCode)) {
          throw SystemException();
        }

        // 根据错误映射分类业务错误
        final errorType = errorTypeMap[errorCode];
        if (errorType != null) {
          errors[errorType]!.add(identityCode);
        }
      } else {
        validResults.add(response);
      }
    }

    // 按assetId去重，保留第一个
    final Map<int, SharedGetSelectedAssetListResponse> uniqueMap = {};
    for (final response in validResults) {
      final assetId = response.asset?.assetId;
      if (assetId != null && !uniqueMap.containsKey(assetId)) {
        uniqueMap[assetId] = response;
      }
    }

    return CarNumberOcrResult(errors: errors, validResults: uniqueMap.values.toList());
  }
}
