import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_page_visible_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event_bus.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_button/presentation/bindings/overlay_button_binding.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_button/presentation/widget/overlay_button_widget.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_page/presentation/bindings/overlay_binding.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_page/presentation/pages/overlay_page.dart';
import 'package:flutter/material.dart';

class FloatingOverlayManager {
  static const double _buttonBottomOffset = 75.0;
  static const double _buttonLeftOffset = 14.0;

  static final FloatingOverlayManager _instance = FloatingOverlayManager._internal();
  factory FloatingOverlayManager() => _instance;
  FloatingOverlayManager._internal() {
    EventBus.on<OverlayPageVisibleEvent>((event) {
      if (!event.visible) {
        showButton();
      } else {
        hideButton();
      }
    });
  }

  OverlayEntry? _buttonEntry;
  OverlayEntry? _pageEntry;
  OverlayState? _overlay;

  /// 按钮是否可见
  bool get isButtonVisible => _buttonEntry != null;

  /// 页面overlay是否可见
  bool get isPageVisible => _pageEntry != null;

  /// 管理器是否已初始化
  bool get isInitialized => _overlay != null;

  /// 隐藏按钮overlay
  void hideButton() {
    LogUtil.d('FloatingOverlayManager hideButton');
    _removeButton();
  }

  /// 全局初始化，显示按钮和页面overlay
  void init(BuildContext context, {OverlayState? overlay}) {
    LogUtil.d('FloatingOverlayManager init');
    _overlay = overlay ?? Overlay.of(context);

    if (_overlay == null) {
      LogUtil.e('FloatingOverlayManager init failed: overlay is null');
      return;
    }

    showButton();
    _initPageOverlay();
  }

  /// 显示按钮overlay
  void showButton() {
    LogUtil.d('FloatingOverlayManager showButton');
    if (!isInitialized) {
      LogUtil.w('FloatingOverlayManager not initialized');
      return;
    }

    if (!isButtonVisible) {
      _createButtonOverlay();
    }
  }

  /// 清理资源
  void dispose() {
    LogUtil.d('FloatingOverlayManager dispose');
    EventBus.offType<OverlayPageVisibleEvent>();
    hideButton();
    _removePage();
    _overlay = null;
  }

  void _createButtonOverlay() {
    _removeButton();

    OverlayButtonBinding().dependencies();

    _buttonEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: MediaQuery.of(context).padding.bottom + _buttonBottomOffset,
        left: _buttonLeftOffset,
        child: OverlayButtonWidget(),
      ),
    );

    _overlay!.insert(_buttonEntry!);
  }

  void _removeButton() {
    _buttonEntry?.remove();
    _buttonEntry = null;
  }

  void _initPageOverlay() {
    if (isPageVisible) return;

    OverlayBinding().dependencies();

    _pageEntry = OverlayEntry(builder: (context) => const OverlayPage());
    _overlay!.insert(_pageEntry!);
  }

  void _removePage() {
    _pageEntry?.remove();
    _pageEntry = null;
  }
}
