import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_unread_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/open_live_overlay_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_button_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event_bus.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event_bus_mixin.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:get/get.dart';

class OverlayButtonController extends BaseController with EventBusMixin {
  /// 是否显示
  var showIcon = false.obs;

  /// 是否可用
  var active = false.obs;

  /// 显示未读数量. (有的时候显示红点, 反正不显示)
  var unreadCount = 1.obs;

  @override
  void onInit() {
    super.onInit();

    onEvent<OverlayButtonEvent>(_overlayEventHandler);
    onEvent<OverlayUnreadEvent>((event) {
      unreadCount.value = event.unreadCount;
    });

    Future.delayed(Duration.zero, _resetStatus);
  }

  void _overlayEventHandler(OverlayButtonEvent event) {
    switch (event.type) {
      case OverlayButtonEventType.init:
        _resetStatus();
        showIcon.value = true;
        break;
      case OverlayButtonEventType.activate:
        active.value = true;
        break;
      case OverlayButtonEventType.logout:
        _resetStatus();
        break;
      case OverlayButtonEventType.show:
        if (active.value) {
          showIcon.value = true;
        }
        break;
      case OverlayButtonEventType.hidden:
        showIcon.value = false;
        break;
    }
  }

  void _resetStatus() {
    showIcon.value = false;
    active.value = false;
    unreadCount.value = 0;
  }

  void onTap() {
    EventBus.fire(OpenLiveOverlayEvent());
  }
}
