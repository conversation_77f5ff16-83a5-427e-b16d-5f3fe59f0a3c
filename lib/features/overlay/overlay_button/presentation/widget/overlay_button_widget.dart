import 'package:asset_force_mobile_v2/features/overlay/overlay_button/presentation/controllers/overlay_button_controller.dart';
import 'package:asset_force_mobile_v2/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class OverlayButtonWidget extends GetView<OverlayButtonController> {
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => controller.showIcon.value
          ? GestureDetector(
              onTap: () => controller.onTap(),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: controller.active.value ? Colors.white : Colors.grey,
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    alignment: Alignment.center,
                    child: SvgPicture.asset(Assets.iconsMdiLive, width: 16, height: 16),
                  ),
                  if (controller.unreadCount.value > 0)
                    Positioned(
                      right: -2,
                      top: -2,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                      ),
                    ),
                ],
              ),
            )
          : const SizedBox.shrink(),
    );
  }
}
