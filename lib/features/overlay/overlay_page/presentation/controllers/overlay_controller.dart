import 'dart:async';
import 'dart:convert';

import 'package:asset_force_mobile_v2/core/deeplink/deeplink_service.dart';
import 'package:asset_force_mobile_v2/core/deeplink/handler/handler_param.dart';
import 'package:asset_force_mobile_v2/core/deeplink/handler/link_handler_type_enum.dart';
import 'package:asset_force_mobile_v2/core/env/env_helper.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_page_visible_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_unread_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/open_live_overlay_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_button_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_create_talk_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_filtertalk_change_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_init_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event/overlay/overlay_reset_event.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event_bus.dart';
import 'package:asset_force_mobile_v2/core/event_bus/event_bus_mixin.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/routes/route_tacker.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_page/presentation/models/filter_talk_parameter.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_page/presentation/models/filter_talk_tag.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_page/presentation/models/overlay_ui_state.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_page/presentation/pages/overlay_page_const.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/get_authority_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

// 你需要根据实际情况实现这些依赖
// import 'your_services.dart';

class OverlayController extends BaseController with EventBusMixin {
  final state = OverlayUIState();
  //#region webview 相关逻辑

  Map<String, dynamic> _result(String status, {String? message, dynamic echo}) {
    final result = <String, dynamic>{'status': status};
    if (message != null) result['message'] = message;
    if (echo != null) result['echo'] = echo;
    return result;
  }

  dynamic onWebViewMessage(List<dynamic> arguments) {
    LogUtil.v('Received from WebView: $arguments');
    if (!state.isAllowTalk.value) return _result('error', message: 'Talk is not allowed');
    if (arguments.isEmpty) return _result('error', message: 'No arguments received');
    final arg = arguments[0] as Map<String, dynamic>?;
    if (arg == null) return _result('error', message: 'Invalid argument format');
    if (arg['type'] == 'iframe-ready') {
      EventBus.fire(OverlayButtonEvent(OverlayButtonEventType.activate));
      return _result('ok', message: '');
    }

    if (!arg.containsKey('data') || !arg.containsKey('type') || !arg.containsKey('system')) {
      return _result('error', message: 'Invalid argument format');
    }
    if (arg['system'] != 'live') {
      return _result('error', message: 'Unsupported system type');
    }
    final String? type = arg['type'] as String?;
    if (type == null) {
      return _result('error', message: 'Type is null');
    }
    final supporttedTypes = ['init-overlay', 'unread-messages', 'open-page', 'click-talk-list'];
    if (!supporttedTypes.contains(type)) {
      return _result('error', message: 'Unsupported type: $type');
    }
    final postObj = arg['data'];

    switch (type) {
      case 'init-overlay':
        state.isInitLive.value = true;
        break;
      case 'unread-messages':
        EventBus.fire(
          OverlayUnreadEvent(unreadCount: postObj['allUnreadCount'], latestReceiveDate: postObj['latestReceiveDate']),
        );
        break;
      case 'open-page':
        closeLiveOverlay();
        if (postObj['workflowId'] != null) {
          navigateWorkflowPage(postObj['workflowId']);
        } else {
          navigateAssetPage(postObj['assetTypeId'], postObj['assetId']);
        }
        break;
      case 'click-talk-list':
        openLiveOverlay(postObj['talkId']);
        break;
      default:
        return _result('error', message: 'Unknown type: $type');
    }

    return _result('ok', echo: arguments);
  }

  void onWebViewCreated(InAppWebViewController controller) {
    state.inAppWebViewController = controller;
    controller.addJavaScriptHandler(
      handlerName: 'FlutterChannel',
      callback: (args) {
        return onWebViewMessage(args);
      },
    );

    state.inAppWebViewController?.loadUrl(urlRequest: URLRequest(url: WebUri.uri(state.startUrl.value)));

    initTalkFilter();
  }

  Future<NavigationActionPolicy?> shouldOverrideUrlLoading(NavigationAction navigationAction) {
    final url = navigationAction.request.url.toString();
    if (state.isNeedOverrideUrl(url)) {
      state.inAppWebViewController!.loadData(
        data: OverlayPageConst.getInitialHtml(state.liveTalkUrl.value!),
        baseUrl: WebUri(_getBaseUrl()),
        mimeType: 'text/html',
        encoding: 'utf-8',
      );
      return Future.value(NavigationActionPolicy.CANCEL);
    }
    return Future.value(NavigationActionPolicy.ALLOW);
  }

  Future<GeolocationPermissionShowPromptResponse?> onGeolocationPermissionsShowPrompt(String origin) async {
    final status = await Permission.location.request();
    return GeolocationPermissionShowPromptResponse(origin: origin, allow: status.isGranted, retain: true);
  }

  Future<PermissionResponse?> onPermissionRequest(PermissionRequest request) async {
    bool granted = true;

    // 检查摄像头权限
    if (request.resources.contains(PermissionResourceType.CAMERA)) {
      final status = await Permission.camera.request();
      LogUtil.d('Camera permission status: ${status}');
      if (!status.isGranted) granted = false;
    }
    // 检查麦克风权限
    if (request.resources.contains(PermissionResourceType.MICROPHONE)) {
      final status = await Permission.microphone.request();
      LogUtil.d('Microphone permission status: ${status}');
      if (!status.isGranted) granted = false;
    }
    // 检查定位权限
    if (request.resources.contains(PermissionResourceType.GEOLOCATION)) {
      final status = await Permission.location.request();
      LogUtil.d('Geolocation permission status: ${status}');
      if (!status.isGranted) granted = false;
    }

    return PermissionResponse(
      resources: request.resources,
      action: granted ? PermissionResponseAction.GRANT : PermissionResponseAction.DENY,
    );
  }

  String _getBaseUrl() {
    // android https://mobile.asset-force.com
    // ios ionic://mobile.asset-force.com
    if (GetPlatform.isAndroid) {
      return 'https://mobile.asset-force.com';
    } else if (GetPlatform.isIOS) {
      return 'ionic://mobile.asset-force.com';
    } else {
      return 'https://mobile.asset-force.com';
    }
  }

  //#endregion

  @override
  void onInit() {
    super.onInit();

    _initEvent();
  }

  void _initEvent() {
    onEvent<OverlayInitEvent>((event) {
      _onInitOverlay();
    });

    onEvent<OpenLiveOverlayEvent>((event) {
      openLiveOverlay();
    });

    onEvent<OverlayResetEvent>((event) {
      state.isInitLive.value = false;
      state.loadAboutBlank();
    });

    onEvent<OverlayCreateTalkEvent>((event) {
      openLiveOverlay(event.talkId, event.createTalk);
    });

    onEvent<OverlayFiltertalkChangeEvent>((event) {
      final currentRoute = RouteTracker.currentRoute;
      if (!state.filterUrls.contains(currentRoute)) {
        state.filterUrls.add(currentRoute);
      }
      state.receiveFilterTalkParameter.value = event.filterTalkParameter;
    });
  }

  Future<void> _onInitOverlay() async {
    if (state.isInitLive.value) return;

    state.isAllowTalk.value = await Get.find<GetAuthorityRepository>().hasFunctionPermission(
      GetAuthorityRepository.talkFunctionPermissionId,
    );
    if (!state.isAllowTalk.value) {
      LogUtil.w('トーク権限がありません');
      return;
    }

    final token = StorageUtils.get<String>(StorageUtils.keyToken);
    final zoneId = StorageUtils.get<String>(StorageUtils.keyZoneId);
    state.liveTalkUrl.value = '${EnvHelper.getLiveBaseUrl()}/af-overlay?accessToken=$token&zoneId=$zoneId';

    LogUtil.v('state.liveTalkUrl: ${state.liveTalkUrl}');
    state.loadFakeUrl();

    // 开始显示 悬浮按钮.
    EventBus.fire(OverlayButtonEvent(OverlayButtonEventType.init));
  }

  // 打开live overlay
  void openLiveOverlay([String? talkId, bool createTalk = false]) {
    LogUtil.v('openLiveOverlay: $talkId,  createTalk: $createTalk, state.isInitLive: ${state.isInitLive.value}');

    if (state.isShowOverlay.value) {
      return;
    }

    EventBus.fire(OverlayPageVisibleEvent(visible: true));

    state.isShowOverlay.value = true;

    if (state.filterUrls.contains(RouteTracker.currentRoute)) {
      state.currentFilterTalkParameter.value = state.receiveFilterTalkParameter.value?.copyWith(
        talkId: talkId,
        createTalk: createTalk,
      );
      updateTalkFilter();
    } else if (_isWorkflowPage()) {
      setAllWorkflowFilter();
    } else {
      initTalkFilter();
    }
  }

  bool _isWorkflowPage() {
    final currentRoute = RouteTracker.currentRoute;
    return currentRoute.endsWith(AutoRoutes.workflow) || currentRoute.startsWith(AutoRoutes.appTabWorkflowTabs);
  }

  // 关闭live overlay
  void closeLiveOverlay() {
    state.isShowOverlay.value = false;
    EventBus.fire(OverlayPageVisibleEvent(visible: false));
  }

  // 初始化全部talk过滤
  void initTalkFilter() {
    state.filterTalkTags.value = [
      FilterTalkTag(name: '全てのトーク', icon: null, bgColor: '#ffffff', showCloseButton: false, isParent: true),
    ];
    state.currentFilterTalkParameter.value = FilterTalkParameter(showAllTalks: true);
    postFilterTalk();
  }

  // 全部workflow过滤
  void setAllWorkflowFilter() {
    state.filterTalkTags.value = [
      FilterTalkTag(
        name: 'ワークフロー',
        icon: 'icon-workflow-v2',
        bgColor: '#cedcf2',
        showCloseButton: true,
        isParent: true,
      ),
    ];
    state.currentFilterTalkParameter.value = FilterTalkParameter(showAllWorkflowTalks: true);
    postFilterTalk();
  }

  // 更新过滤
  void updateTalkFilter() {
    if (state.currentFilterTalkParameter.value == null) return;
    state.filterTalkTags.clear();
    final param = state.currentFilterTalkParameter.value!;
    if (param.assetTypeId != null) {
      state.filterTalkTags.add(
        FilterTalkTag(
          name: param.assetTypeName ?? '',
          icon: 'icon-assets-v2',
          bgColor: '#cedcf2',
          showCloseButton: true,
          isParent: true,
        ),
      );
      if (param.assetIds != null && param.assetIds!.isNotEmpty) {
        state.filterTalkTags.add(
          FilterTalkTag(
            name: param.assetName ?? '',
            icon: null,
            bgColor: '#edf4ff',
            showCloseButton: true,
            isParent: false,
            isAsset: true,
          ),
        );
      }
    } else if (param.workflowId != null) {
      state.filterTalkTags.add(
        FilterTalkTag(
          name: 'ワークフロー',
          icon: 'icon-workflow-v2',
          bgColor: '#cedcf2',
          showCloseButton: true,
          isParent: true,
        ),
      );
      state.filterTalkTags.add(
        FilterTalkTag(
          name: param.workflowName ?? '',
          icon: null,
          bgColor: '#edf4ff',
          showCloseButton: true,
          isParent: false,
          isWorkflow: true,
        ),
      );
    }
    postFilterTalk();
  }

  // 发送过滤条件到live
  Future<void> postFilterTalk() async {
    LogUtil.d('Posting filter talk data1: ${state.liveTalkUrl.value == null}');
    if (state.liveTalkUrl.value == null) {
      return;
    }

    await Future.delayed(const Duration(milliseconds: 500));
    LogUtil.d('Posting filter talk data: ${state.currentFilterTalkParameter.value?.toJson()}');

    try {
      final filterData = state.currentFilterTalkParameter.value?.toJson() ?? {};
      await state.inAppWebViewController?.evaluateJavascript(
        source: 'window.handleFlutterMessage(${jsonEncode(filterData)})',
      );
    } catch (e) {
      LogUtil.e('Error posting filter talk data: $e');
    }
  }

  // 关闭过滤标签
  void closeFilterTag(bool isParent) {
    if (isParent) {
      initTalkFilter();
    } else {
      final popped = state.filterTalkTags.removeLast();
      if (popped.isAsset == true) {
        state.currentFilterTalkParameter.value = FilterTalkParameter(
          assetTypeId: state.currentFilterTalkParameter.value?.assetTypeId,
          assetTypeName: state.currentFilterTalkParameter.value?.assetTypeName,
          assetSearchId: state.currentFilterTalkParameter.value?.assetSearchId,
        );
        postFilterTalk();
      } else if (popped.isWorkflow == true) {
        setAllWorkflowFilter();
      }
    }
  }

  Future<void> navigateAssetPage(String? assetTypeId, String? assetId) async {
    if (assetTypeId == null) {
      CommonDialog.showToast('対象ページが存在しませんでした');
      return;
    }

    final strategy = assetId == null
        ? DeeplinkService().linkHandlerFactory.getStrategy(LinkHandlerTypeEnum.AssetList.toString())
        : DeeplinkService().linkHandlerFactory.getStrategy(LinkHandlerTypeEnum.AssetDetail.toString());

    strategy?.execute(HandlerParam({'assetTypeId': assetTypeId, 'assetId': assetId}));
  }

  void navigateWorkflowPage(String workflowId) {
    final strategy = DeeplinkService().linkHandlerFactory.getStrategy(LinkHandlerTypeEnum.WorkFlowDetail.toString());
    strategy?.execute(HandlerParam({'workflowId': workflowId}));
  }
}
