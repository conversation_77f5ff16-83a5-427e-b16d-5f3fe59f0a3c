// 你需要根据实际情况定义这些类
class FilterTalkParameter {
  final bool? showAllTalks;
  final bool? showAllWorkflowTalks;
  final String? assetTypeId;
  final String? assetTypeName;
  final String? assetSearchId;
  final List<String>? assetIds;
  final String? assetName;
  final String? workflowId;
  final String? workflowName;
  final String? talkId;
  final bool? createTalk;

  /** 当前页面的route 信息 */
  final String? routeName;

  FilterTalkParameter({
    this.showAllTalks,
    this.showAllWorkflowTalks,
    this.assetTypeId,
    this.assetTypeName,
    this.assetSearchId,
    this.assetIds,
    this.assetName,
    this.workflowId,
    this.workflowName,
    this.talkId,
    this.createTalk,
    this.routeName,
  });

  FilterTalkParameter copyWith({
    bool? showAllTalks,
    bool? showAllWorkflowTalks,
    String? assetTypeId,
    String? assetTypeName,
    String? assetSearchId,
    List<String>? assetIds,
    String? assetName,
    String? workflowId,
    String? workflowName,
    String? talkId,
    bool? createTalk,
    String? routeName,
  }) {
    return FilterTalkParameter(
      showAllTalks: showAllTalks ?? this.showAllTalks,
      showAllWorkflowTalks: showAllWorkflowTalks ?? this.showAllWorkflowTalks,
      assetTypeId: assetTypeId ?? this.assetTypeId,
      assetTypeName: assetTypeName ?? this.assetTypeName,
      assetSearchId: assetSearchId ?? this.assetSearchId,
      assetIds: assetIds ?? this.assetIds,
      assetName: assetName ?? this.assetName,
      workflowId: workflowId ?? this.workflowId,
      workflowName: workflowName ?? this.workflowName,
      talkId: talkId ?? this.talkId,
      createTalk: createTalk ?? this.createTalk,
      routeName: routeName ?? this.routeName,
    );
  }

  Map<String, dynamic> toJson() => {
    'showAllTalks': showAllTalks,
    'showAllWorkflowTalks': showAllWorkflowTalks,
    'assetTypeId': assetTypeId,
    'assetTypeName': assetTypeName,
    'assetSearchId': assetSearchId,
    'assetIds': assetIds,
    'assetName': assetName,
    'workflowId': workflowId,
    'workflowName': workflowName,
    'talkId': talkId,
    'createTalk': createTalk,
    'routeName': routeName,
  };

  @override
  String toString() {
    return 'FilterTalkParameter{'
        'showAllTalks: $showAllTalks, '
        'showAllWorkflowTalks: $showAllWorkflowTalks, '
        'assetTypeId: $assetTypeId, '
        'assetTypeName: $assetTypeName, '
        'assetSearchId: $assetSearchId, '
        'assetIds: $assetIds, '
        'assetName: $assetName, '
        'workflowId: $workflowId, '
        'workflowName: $workflowName, '
        'talkId: $talkId, '
        'createTalk: $createTalk, '
        'routeName: $routeName'
        '}';
  }
}
