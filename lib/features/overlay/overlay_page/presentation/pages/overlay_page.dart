import 'dart:core';

import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_page/presentation/controllers/overlay_controller.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_page/presentation/models/filter_talk_tag.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';

class OverlayPage extends StatefulWidget {
  const OverlayPage({super.key});

  @override
  State<OverlayPage> createState() => _OverlayPageState();
}

class _OverlayPageState extends State<OverlayPage> with AutomaticKeepAliveClientMixin {
  late final OverlayController _overlayController;

  @override
  void initState() {
    super.initState();
    _overlayController = Get.find<OverlayController>();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Obx(
      () => Positioned(
        top: _overlayController.state.isShowOverlay.value ? 0 : -MediaQuery.of(context).size.height,
        left: 0,
        right: 0,
        bottom: _overlayController.state.isShowOverlay.value ? 0 : MediaQuery.of(context).size.height,
        child: SafeArea(
          child: Container(
            key: const ValueKey('overlay'),
            width: double.infinity,
            height: double.infinity,
            color: Colors.white,
            child: Column(children: [_buildHeaderWidget(), _buildWebViewWidget()]),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderWidget() {
    return Container(
      height: 40,
      width: double.infinity,
      color: Colors.white,
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          Obx(() => _buildFilterTagsWidget(_overlayController.state.filterTalkTags, _overlayController)),
          IconButton(
            icon: const Icon(Icons.close),
            color: Colors.blue,
            onPressed: _overlayController.closeLiveOverlay,
            style: IconButton.styleFrom(minimumSize: const Size(40, 40), padding: const EdgeInsets.all(0)),
          ),
        ],
      ),
    );
  }

  Expanded _buildWebViewWidget() {
    return Expanded(
      child: Obx(() {
        return InAppWebView(
          key: const ValueKey('ovlerlay_webview'),
          initialUrlRequest: URLRequest(url: WebUri.uri(_overlayController.state.startUrl.value)),
          initialSettings: InAppWebViewSettings(
            useShouldOverrideUrlLoading: true,
            mediaPlaybackRequiresUserGesture: false,
            allowsInlineMediaPlayback: true,
            javaScriptEnabled: true,
            offscreenPreRaster: true,
            javaScriptCanOpenWindowsAutomatically: true,
            geolocationEnabled: true,
            mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
            userAgent:
                'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
            allowFileAccessFromFileURLs: true,
            allowUniversalAccessFromFileURLs: true,
            supportZoom: false,
            builtInZoomControls: false,
            displayZoomControls: false,
            cacheEnabled: false,
            clearCache: true,
          ),
          onWebViewCreated: (controller) => _overlayController.onWebViewCreated(controller),
          onPermissionRequest: (controller, request) => _overlayController.onPermissionRequest(request),
          onGeolocationPermissionsShowPrompt: (controller, origin) =>
              _overlayController.onGeolocationPermissionsShowPrompt(origin),
          onLoadStart: (controller, url) {
            LogUtil.d('--->onLoadStart: $url');
          },
          onLoadStop: (controller, url) {
            LogUtil.d('--->onLoadStop: $url');
          },
          onReceivedError: (controller, request, error) {
            LogUtil.d('WebView Error: ${error.description}');
          },
          onReceivedHttpError: (controller, request, errorResponse) {
            LogUtil.d('HTTP Error: ${errorResponse.statusCode}');
          },
          shouldOverrideUrlLoading: (controller, navigationAction) =>
              _overlayController.shouldOverrideUrlLoading(navigationAction),
        );
      }),
    );
  }

  Expanded _buildFilterTagsWidget(RxList<FilterTalkTag> tags, OverlayController controller) {
    return Expanded(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.only(left: 10, right: 0, top: 5, bottom: 5),
        child: Row(
          children: tags.map((tag) {
            return Container(
              margin: const EdgeInsets.only(right: 5),
              padding: const EdgeInsets.only(left: 10, right: 0),
              decoration: BoxDecoration(color: hexToColor(tag.bgColor), borderRadius: BorderRadius.circular(4)),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (tag.icon != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 5),
                      child: SvgPicture.asset(
                        'assets/images/tabs/${tag.icon}.svg',
                        width: 18,
                        height: 18,
                        placeholderBuilder: (c) => const SizedBox(width: 18, height: 18),
                      ),
                    ),
                  Text(
                    tag.name,
                    style: const TextStyle(
                      fontSize: 14,
                      decoration: TextDecoration.none,
                      fontWeight: FontWeight.normal,
                      color: Colors.black,
                    ),
                  ),
                  if (tag.showCloseButton)
                    IconButton(
                      icon: const Icon(Icons.close, size: 16, color: Colors.black),
                      padding: const EdgeInsets.only(left: 4),
                      constraints: const BoxConstraints(),
                      onPressed: () => controller.closeFilterTag(tag.isParent),
                    ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  Color hexToColor(String hex) {
    final buffer = StringBuffer();
    if (hex.length == 7) buffer.write('ff');
    buffer.write(hex.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }
}
