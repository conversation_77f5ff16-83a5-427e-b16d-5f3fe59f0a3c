class OverlayPageConst {
  static String getInitialHtml(String liveTalkUrl) {
    final html =
        '''
        <!DOCTYPE html>
        <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            html, body {
              height: 100%;
              margin: 0;
              padding: 0;
            }
            iframe {
              width: 100vw;
              height: 100vh;
              border: none;
              display: block;
            }
          </style>
          
  <script>
    (function() {
      console.log('flutter message handler start.');
      
      let iframeReady = false;
      let pendingMessages = [];

      window.test = function(){
        console.log('test function called');
      }

      // WebView -> Flutter
      window.sendMessageToFlutter = function(data) {
        console.log('sendMessageToFlutter....', JSON.stringify(data));
        if (window.flutter_inappwebview) {
          window.flutter_inappwebview.callHandler('FlutterChannel', data)
            .then(function(response) {
              console.log("[sendMessageToFlutter] Got response from Flutter:", response);
            })
            .catch(function(error) {
              console.error("[sendMessageToFlutter] Error:", error);
            });
        } else {
          console.warn('[Bridge] FlutterChannel not ready.');
        }
      };

      // 1. Flutter -> WebView -> iframe (改进版本)
      window.handleFlutterMessage = function(data) {
        try {
          console.log('[Bridge flutter->iframe], iframeReady=[' + iframeReady +'] received:', JSON.stringify(data));
          const iframe = document.querySelector('iframe');

          // 检查 iframe 是否已加载
          if (!iframeReady) {
            console.log('[Bridge flutter->iframe] iframe not ready, adding to pending messages');
            pendingMessages.push(data);
            return;
          }

          // 发送消息到 iframe
          if (iframe && iframe.contentWindow) {
            const messageData = {
              type: data.type || 'af-overlay',
              system: data.system || 'core',
              data: data.data || data,
              ...data
            };
            iframe.contentWindow.postMessage(messageData, '*');
            console.log('[Bridge flutter->iframe] Message posted to iframe.');
          } else {
            console.warn('[Bridge flutter->iframe] iframe.contentWindow not available');
          }
        } catch (e) {
          console.error('[Bridge flutter->iframe] handleFlutterMessage error:', e);
        }
      };

      // 2. iframe -> WebView -> Flutter
      window.addEventListener('message', function(event) {
        try {
          const iframe = document.querySelector('iframe');
          if (iframe && event.source === iframe.contentWindow) {
            if (window.sendMessageToFlutter) {
              window.sendMessageToFlutter(event.data);
            }
          } else {
            console.log('[Bridge iframe->flutter] Message ignoring. [not from iframe], event.source:', event.source);
          }
        } catch (e) {
          console.error('[Bridge iframe->flutter] Message handler error:', e);
        }
      });

      // iframe 加载完成处理
      function onIframeLoad() {
        console.log('[Bridge] iframe loaded, pendingMessages.length:', pendingMessages.length);
        iframeReady = true;
        
        // 发送所有待处理的消息
        if (pendingMessages.length > 0) {
          console.log('[Bridge] Sending pending messages:', pendingMessages.length);
          pendingMessages.forEach(function(msg) {
            window.handleFlutterMessage(msg);
          });
          pendingMessages = [];
        }

        // 通知 Flutter iframe 已准备好
        window.sendMessageToFlutter({
          type: 'iframe-ready',
          timestamp: Date.now()
        });
      }

      // 全局错误处理
      window.onerror = function(message, source, lineno, colno, error) {
        const errorData = {
          type: 'js-error',
          message: message,
          source: source,
          lineno: lineno,
          colno: colno,
          stack: error ? error.stack : null
        };
        console.error('[Bridge] JavaScript error:', JSON.stringify(errorData));
        if (window.sendMessageToFlutter) {
          window.sendMessageToFlutter(errorData);
        }
      };

      var iframeCheckCounter = 0;
      function checkIframeReady() {
        const iframe = document.querySelector('iframe');
        console.log('[Bridge] Checking iframe ready state...');
        if (!iframe) return;

        try {
          const win = iframe.contentWindow;
          if (win) {
            onIframeLoad();
            return;
          }
        } catch (e) {
          // iframe not ready
        }
        setTimeout(checkIframeReady, 1000);
        iframeCheckCounter++;
        if(iframeCheckCounter > 6) {
          console.warn('[Bridge] iframe still not ready after 6 seconds, assuming ready.');
          onIframeLoad();
          return;
        }
      }


      window.onload = function() {
        console.log('[Bridge] webview Page loaded');

        const iframe = document.querySelector('iframe');
        if (iframe) {
          iframe.onload = onIframeLoad;
          
          checkIframeReady();
        }
      };

      console.log('flutter message handler initialized.');
    })();
  </script>
        </head>
        <body>
          <iframe src="$liveTalkUrl" 
            allow="camera;microphone;notifications;"
            sandbox="allow-same-origin allow-scripts allow-popups allow-top-navigation allow-forms"
            style="width:100vw;height:100vh;border:none;"></iframe>
        </body>
        </html>
        ''';

    return html;
  }
}
