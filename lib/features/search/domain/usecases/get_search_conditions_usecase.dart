import 'dart:convert';

import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/get_search_id_usecase.dart';
import 'package:asset_force_mobile_v2/features/search/domain/enums/search_type.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/search_condition.dart';

class GetSearchConditionParams {
  final int? assetTypeId;
  final SearchPageType searchEnum;
  final String? searchKey;
  final int? searchId;

  GetSearchConditionParams({
    required this.searchEnum,
    required this.searchKey,
    required this.searchId,
    required this.assetTypeId,
  });
}

class GetSearchConditionResult {
  /// 検索条件を取得
  final List<SearchConditionModel> searchConditionList;

  /// 用户搜索的历史记录取得
  final List<String> historySearchList;

  /// 保存在mmkv的key
  final String keyRecordSearch;

  GetSearchConditionResult({
    required this.keyRecordSearch,
    required this.searchConditionList,
    required this.historySearchList,
  });
}

class GetSearchConditionsUseCase implements UseCase<GetSearchConditionResult, GetSearchConditionParams> {
  final GetSearchIdUseCase getSearchIdUseCase;
  late String _keyRecordSearch;

  GetSearchConditionsUseCase({required this.getSearchIdUseCase});

  /// 主实现函数
  /// * int? [assetTypeId] 资产种类ID，可以为空，为空代表非资产一类页面调用。
  /// * SearchPageType [searchEnum] 必传字段，代表这个方法被调用唯一位置
  /// * String? [searchKey] 用户上一次输入的内容，可以为空
  /// * int? [searchId] 检索种类Id，可以为空，资产一类页面调用
  /// * returns [searchConditionList] 检索种类数组
  /// * returns [historySearchList] 搜索历史数组
  /// * returns [keyRecordSearch] 保存在mmkv的key
  @override
  Future<GetSearchConditionResult> call(GetSearchConditionParams params) async {
    final int? assetTypeId = params.assetTypeId;
    final SearchPageType searchEnum = params.searchEnum;
    _keyRecordSearch = searchEnum.value + StorageUtils.keyRecordHistorySearchList;

    final List<String> historySearchList = _getHistorySearchList(key: _keyRecordSearch);

    if (searchEnum == SearchPageType.assetListPage) {
      final GetSearchIdResult searchIdResult = await getSearchIdUseCase.swapSearchIdResultArrPlaces(
        assetTypeId: assetTypeId,
      );
      return GetSearchConditionResult(
        searchConditionList: searchIdResult.assetSearchNameList,
        historySearchList: historySearchList,
        keyRecordSearch: _keyRecordSearch,
      );
    }

    return GetSearchConditionResult(
      searchConditionList: [],
      historySearchList: historySearchList,
      keyRecordSearch: _keyRecordSearch,
    );
  }

  /// 检索已经被搜索的关键字历史记录
  /// - [key] mmkv的key
  /// return：[List<String>] || [] 用户历史词条
  List<String> _getHistorySearchList({required String key}) {
    final String? jsonString = StorageUtils.get<String>(key);
    final List<String>? historyList = _decodeStringList(jsonString: jsonString);
    return historyList ?? [];
  }

  /// 取值 String 转换 List< String > 专用
  /// - [jsonString] string 型 List< String > 数据
  /// 返回值：List< String > || null
  List<String>? _decodeStringList({required String? jsonString}) {
    if (jsonString == null) return null;
    try {
      // 将JSON字符串转回List
      final dynamic decoded = jsonDecode(jsonString);
      if (decoded is! List) return null;
      return decoded.map<String>((item) => item.toString()).toList();
    } catch (_) {
      // 如果解析失败，返回 null
      return null;
    }
  }

  /// 保存搜索记录
  /// - [inputKey] 用户输入的文本
  /// return [List<String>] 用户历史词条
  Future<List<String>> saveHistorySearchInput({required String inputKey}) async {
    final List<String> historySearchList = _getHistorySearchList(key: _keyRecordSearch);

    final trimmedInput = inputKey.trim();
    if (trimmedInput.isEmpty) return historySearchList;

    // 检查是否已存在相同的搜索词
    if (historySearchList.contains(trimmedInput)) return historySearchList;

    // 将新的搜索词添加到列表开头
    historySearchList.insert(0, trimmedInput);

    // 如果列表长度超过10，删除末尾元素
    if (historySearchList.length > 10) {
      historySearchList.removeLast();
    }
    final String jsonString = jsonEncode(historySearchList);
    await StorageUtils.set(_keyRecordSearch, jsonString);
    return historySearchList;
  }

  /// 清除所有用户所有历史词条列表
  Future<void> clearAllHistorySearchInput() async {
    await StorageUtils.remove(_keyRecordSearch);
  }
}
