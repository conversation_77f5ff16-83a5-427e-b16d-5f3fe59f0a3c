import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/usecase/get_search_id_usecase.dart';
import 'package:asset_force_mobile_v2/features/search/domain/enums/search_type.dart';
import 'package:asset_force_mobile_v2/features/search/domain/usecases/get_search_conditions_usecase.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/states/search_condition_ui_state.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/search_condition.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_mixin.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

/// 返回结果类，包含 searchId
class SearchConditionResult {
  final int searchId;
  final String searchKey;

  SearchConditionResult({required this.searchId, required this.searchKey});

  @override
  String toString() {
    return 'SearchConditionResult{searchId: $searchId, searchKey: $searchKey}';
  }
}

class SearchConditionController extends BaseController with OverlayMixin {
  final GetSearchConditionsUseCase getSearchConditionsUseCase;
  final NavigationService navigationService;
  final DialogService dialogService;
  final TextEditingController textEditingController = TextEditingController(); // 内部创建

  // 使用 @visibleForTesting 标记，使其在测试中可见
  @visibleForTesting
  late final GetSearchConditionParams params;

  final SearchConditionUIState state = SearchConditionUIState();

  SearchConditionController({
    required this.getSearchConditionsUseCase,
    required this.navigationService,
    required this.dialogService,
  });

  @override
  void onInit() {
    super.onInit();
    _initializeParams();
  }

  @override
  void onReady() {
    super.onReady();
    _loadData();
  }

  @override
  void onClose() {
    textEditingController.dispose(); // 释放资源
    super.onClose();
  }

  void _initializeParams() {
    try {
      final args = Get.arguments;
      if (args is GetSearchConditionParams) {
        params = args;
      } else {
        LogUtil.e('Invalid arguments type: ${args.runtimeType}');
        handleException(BusinessException('検索条件の読み込みに失敗しました'));
        navigationService.goBack();
        return;
      }
    } catch (e, stackTrace) {
      LogUtil.e('_initializeParams Exception: $e', stackTrace: stackTrace);
      handleException(e);
    }
  }

  Future<void> _loadData() async {
    try {
      await showLoading();
      final getAssetTypeResult = await getSearchConditionsUseCase.call(params);
      state.searchInput.value = params.searchKey ?? '';
      // 保留光标位置
      _updateTextController(state.searchInput.value);
      state.searchHistoryList.value = getAssetTypeResult.historySearchList;
      if (params.searchEnum == SearchPageType.assetListPage) {
        state.searchConditions.value = getAssetTypeResult.searchConditionList;
        state.selectedRadioCondition.value =
            getAssetTypeResult.searchConditionList.firstWhereOrNull((sc) => params.searchId == sc.searchId) ??
            getAssetTypeResult.searchConditionList[0];
      }
    } catch (e, stackTrace) {
      LogUtil.e('_load Exception: $e', stackTrace: stackTrace);
      handleException(e);
    } finally {
      hideLoading();
    }
  }

  /// 検索
  void onClickSearch() async {
    final isCommonSearchDialog = commonSearchConditionsDialog();
    if (isCommonSearchDialog) {
      return;
    }
    final bool isSearchIdZero = state.selectedRadioCondition.value?.searchId == searchIdConditionZero;

    if (isSearchIdZero && state.searchInput.value.isEmpty) {
      dialogService.show(content: 'キーワードを入力ください');
      return;
    }

    final List<String> historySearchList = await getSearchConditionsUseCase.saveHistorySearchInput(
      inputKey: state.searchInput.value,
    );
    state.searchHistoryList.value = historySearchList;
    // 考虑到可能网络请求失败，这时候取selectedRadioCondition是没有值，下面是解决侧
    final int paramsSearchId = params.searchId ?? searchIdConditionTwo;
    final int searchId = state.selectedRadioCondition.value?.searchId ?? paramsSearchId;
    navigationService.goBack(
      result: SearchConditionResult(searchId: searchId, searchKey: state.searchInput.value),
    );
  }

  /// 保存された検索条件
  void onClickSearchConditionItemToSearch(SearchConditionModel? searchCondition) {
    state.selectedRadioCondition.value = searchCondition;
  }

  /// 履歴をクリア
  void onClickClearAllRecordList() async {
    dialogService.show(
      type: DialogType.confirm,
      content: '過去に検索したキーワードをクリアしますか？',
      confirmText: 'OK',
      cancelText: 'キャンセル',
      onConfirm: () async {
        await getSearchConditionsUseCase.clearAllHistorySearchInput();
        state.searchHistoryList.value = [];
      },
    );
  }

  /// 何をお探しですか
  void onChangeUserSearchInput(String search) {
    // 保留光标位置
    _updateTextController(search);
  }

  /// 用户输入内容全部删除
  void onClickClearSearchInput() {
    // 保留光标位置
    _updateTextController('');
  }

  /// 返回按钮
  void goBackPage() {
    navigationService.goBack();
  }

  String getTitle() {
    if (isFromAssetList()) {
      return '検索条件設定';
    } else {
      return '検索';
    }
  }

  bool isFromAssetList() {
    return params.searchEnum == SearchPageType.assetListPage;
  }

  /// 条件をクリア(清空输入项，条件检索回到默认项目)
  void onClickClearTheConditions() {
    final isCommonSearchDialog = commonSearchConditionsDialog();
    if (isCommonSearchDialog) {
      return;
    }
    dialogService.show(
      content: '検索条件をクリアしますか？',
      confirmText: 'OK',
      cancelText: 'キャンセル',
      onConfirm: () {
        state.searchInput.value = '';
        _loadData();
      },
    );
  }

  void _updateTextController(String text) {
    textEditingController.value = textEditingController.value.copyWith(
      text: text,
      selection: TextSelection.collapsed(offset: text.length),
    );
    state.searchInput.value = text;
  }

  /// 检索页面弹窗共通
  bool commonSearchConditionsDialog() {
    // 是否从资产一览画面过来
    final bool isAssetListPage = SearchPageType.assetListPage == params.searchEnum;
    // 从资产一览画面过来的检索关键字是否为空
    final bool isOldSearchKeyEmpty = params.searchKey == null || params.searchKey?.isEmpty == true;
    // 用户是否入力了检索关键字
    final bool isSearchKeyEmpty = state.searchInput.value.isEmpty;
    // 检索ID是否是2:すべてのデータ (グループで閲覧可)
    final bool isSearchIdTwo = state.selectedRadioCondition.value?.searchId == searchIdConditionTwo;
    // 资产一览传过来的searchId和当前用户选择的searchId是否一致
    final bool isOldSearchIdEqual = state.selectedRadioCondition.value?.searchId == params.searchId;

    if (isAssetListPage && isOldSearchKeyEmpty && isSearchKeyEmpty && isSearchIdTwo && isOldSearchIdEqual) {
      dialogService.show(content: '条件が設定されていません');
      return true;
    }
    return false;
  }
}
