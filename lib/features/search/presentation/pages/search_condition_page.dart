import 'package:asset_force_mobile_v2/features/search/presentation/bindings/search_condition_binding.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/controllers/search_condition_controller.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/search_bottom_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/search_history_list_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/search_input_bar_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/search_save_conditions_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

const descriptionTextCommon = TextStyle(color: Colors.white, fontSize: 13);

final descriptionTextFontWeight = descriptionTextCommon.merge(const TextStyle(fontWeight: FontWeight.w600));

@GetRoutePage('/search', binding: SearchConditionBinding)
class SearchConditionPage extends GetView<SearchConditionController> {
  const SearchConditionPage({super.key});

  static const double distanceEdgeSize = 16.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF0B3E86),
        centerTitle: true,
        title: Text(
          controller.getTitle(),
          style: const TextStyle(
            // fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.chevron_left, color: Colors.white, size: 28),
          onPressed: () => controller.goBackPage(),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(distanceEdgeSize),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 搜索框
                    const SearchInputBarWidget(),
                    const SizedBox(height: 2),
                    // 历史关键词
                    const SearchHistoryWidget(),
                    const SizedBox(height: 30),
                    // 保存的搜索条件（单选按钮）
                    if (controller.isFromAssetList()) const SavedConditionsWidget(),
                  ],
                ),
              ),
            ),
          ),
          if (controller.isFromAssetList()) const BottomButtonsWidget(distanceEdgeSize: distanceEdgeSize),
        ],
      ),
    );
  }
}
