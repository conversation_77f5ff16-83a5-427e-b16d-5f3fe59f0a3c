import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CommonSearchBarNetController extends GetxController {
  final TextEditingController textEditingController = TextEditingController(); // 内部创建
  final RxString searchKey = ''.obs;

  void setSearchKey(String value) {
    searchKey.value = value;
    _updateTextController(value);
  }

  void clearSearchKey() {
    searchKey.value = '';

    _updateTextController('');
  }

  void _updateTextController(String text) {
    textEditingController.value = textEditingController.value.copyWith(
      text: text,
      selection: TextSelection.collapsed(offset: text.length),
    );
  }

  void onChangeUserSearchInput(String value) {
    searchKey.value = value;
    _updateTextController(value);
  }
}

/// 只包含 搜索按钮， 和 内容树入框， 以及右侧有内容的时候 的清除按钮
class CommonSearchBarNetWidget extends GetView<CommonSearchBarNetController> {
  final Function(String key) onSearch;

  /// 初始的搜索关键字，
  final String searchKey;

  CommonSearchBarNetWidget({super.key, required this.searchKey, required this.onSearch}) {
    controller.setSearchKey(searchKey);
  }

  Future<void> handleClear() async {
    controller.clearSearchKey();

    onSearch('');
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => GestureDetector(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 6.0),
          height: 45.0,
          padding: const EdgeInsets.symmetric(horizontal: 6.0),
          decoration: BoxDecoration(
            color: const Color.fromRGBO(255, 255, 255, 0.85),
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              IconButton(
                icon: Icon(
                  Icons.search,
                  color: controller.searchKey.isEmpty ? const Color(0xFF0B3E86) : const Color(0xFF4D94FF),
                ),
                onPressed: () {
                  onSearch(controller.searchKey.value);
                },
              ),
              const SizedBox(width: 6.0),
              Expanded(
                child: TextField(
                  style: const TextStyle(color: Color(0xFF323232), fontWeight: FontWeight.w100),
                  onSubmitted: onSearch,
                  onChanged: controller.onChangeUserSearchInput,
                  textInputAction: TextInputAction.search,
                  controller: controller.textEditingController,
                  decoration: InputDecoration(
                    hintText: controller.searchKey.isEmpty ? '何をお探しですか？' : controller.searchKey.value,
                    hintStyle: const TextStyle(color: Colors.grey),
                    border: InputBorder.none,
                  ),
                ),
              ),
              if (controller.searchKey.isNotEmpty)
                GestureDetector(
                  onTap: handleClear,
                  child: const Icon(Icons.close, color: Colors.black),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
