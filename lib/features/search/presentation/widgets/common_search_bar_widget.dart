import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CommonSearchBarController extends GetxController {
  final RxString searchKey = ''.obs;

  void setSearchKey(String value) {
    searchKey.value = value;
  }

  void clearSearchKey() {
    searchKey.value = '';
  }
}

class CommonSearchBarWidget extends GetView<CommonSearchBarController> {
  final Function() onTap;
  final Function() onClear;

  final bool enableClearConfirmation;
  final String? confirmTitle;
  final String confirmContent;
  final String? confirmButtonText;
  final String? cancelButtonText;

  const CommonSearchBarWidget({
    super.key,
    required this.onTap,
    required this.onClear,
    this.enableClearConfirmation = false,
    this.confirmTitle,
    this.confirmContent = '検索条件をクリアしますか？',
    this.confirmButtonText = 'OK',
    this.cancelButtonText = 'キャンセル',
  });

  Future<void> handleClear() async {
    if (enableClearConfirmation) {
      await CommonDialog.show(
        title: confirmTitle,
        content: confirmContent,
        cancelText: cancelButtonText,
        confirmText: confirmButtonText ?? 'OK',
        onConfirm: () {
          onClear();
          controller.clearSearchKey();
        },
      );
    } else {
      onClear();
      controller.clearSearchKey();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => GestureDetector(
        onTap: onTap,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 6.0),
          height: 45.0,
          padding: const EdgeInsets.symmetric(horizontal: 6.0),
          decoration: BoxDecoration(
            color: const Color.fromRGBO(255, 255, 255, 0.85),
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                Icons.search,
                color: controller.searchKey.isEmpty ? const Color(0xFF0B3E86) : const Color(0xFF4D94FF),
              ),
              const SizedBox(width: 6.0),
              Expanded(
                child: Text(
                  controller.searchKey.isEmpty ? '何をお探しですか？' : controller.searchKey.value,
                  style: TextStyle(fontSize: 16.0, color: controller.searchKey.isEmpty ? Colors.grey : Colors.black),
                ),
              ),
              if (controller.searchKey.isNotEmpty)
                GestureDetector(
                  onTap: handleClear,
                  child: Icon(Icons.close, color: Colors.grey.shade400),
                ),
              const SizedBox(width: 6.0),
              const Icon(Icons.chevron_right, color: Color(0xFF323232)),
            ],
          ),
        ),
      ),
    );
  }
}
