import 'package:asset_force_mobile_v2/features/search/presentation/controllers/search_condition_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

/// 按钮高度
const double buttonHeight = 35;

/// 按钮之间的固定间隔
const double widthBetween = 100;

/// 按钮之间的上下间距
const double buttonsTopAndBottomBetween = 5;

class BottomButtonsWidget extends GetView<SearchConditionController> {
  final double distanceEdgeSize;

  const BottomButtonsWidget({super.key, required this.distanceEdgeSize});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SafeArea(
        top: false, // 仅考虑底部安全区域
        child: Container(
          color: Colors.white,
          alignment: Alignment.topCenter,
          padding: const EdgeInsets.symmetric(vertical: buttonsTopAndBottomBetween),
          child: Row(
            children: [
              // 重置按钮
              _buildButton(
                isOutlined: true,
                onPressed: controller.onClickClearTheConditions,
                edgePadding: EdgeInsets.only(left: distanceEdgeSize),
              ),
              // 按钮间隔
              const SizedBox(width: widthBetween),
              // 检索按钮
              _buildButton(
                isOutlined: false,
                onPressed: controller.onClickSearch,
                edgePadding: EdgeInsets.only(right: distanceEdgeSize),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建按钮组件
  ///
  /// [isOutlined] - 是否使用 OutlinedButton 样式
  /// [onPressed] - 按钮点击回调
  /// [edgePadding] - 按钮与边缘的间距
  Widget _buildButton({required bool isOutlined, required VoidCallback onPressed, required EdgeInsets edgePadding}) {
    return Expanded(
      child: Padding(
        padding: edgePadding,
        child: isOutlined
            ? OutlinedButton(
                onPressed: onPressed,
                style: _outlinedButtonStyle(buttonHeight),
                child: const Text('条件をクリア', style: TextStyle(color: Color(0xFF0B3E86))),
              )
            : ElevatedButton(
                onPressed: onPressed,
                style: _elevatedButtonStyle(buttonHeight),
                child: const Text('検索', style: TextStyle(color: Colors.white)),
              ),
      ),
    );
  }

  /// OutlinedButton 的公共样式
  ButtonStyle _outlinedButtonStyle(double height) {
    return OutlinedButton.styleFrom(
      fixedSize: Size.fromHeight(height),
      foregroundColor: Colors.white,
      side: const BorderSide(color: Color(0xFF0B3E86), width: 1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      padding: EdgeInsets.zero,
      minimumSize: Size(double.infinity, height),
    );
  }

  /// ElevatedButton 的公共样式
  ButtonStyle _elevatedButtonStyle(double height) {
    return ElevatedButton.styleFrom(
      fixedSize: Size.fromHeight(height),
      backgroundColor: const Color(0xFF0B3E86),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 4,
      padding: EdgeInsets.zero,
      minimumSize: Size(double.infinity, height),
    );
  }
}
