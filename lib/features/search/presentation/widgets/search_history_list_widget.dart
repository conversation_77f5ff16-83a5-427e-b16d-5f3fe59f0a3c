import 'package:asset_force_mobile_v2/features/search/presentation/controllers/search_condition_controller.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/pages/search_condition_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';

class SearchHistoryWidget extends GetView<SearchConditionController> {
  const SearchHistoryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final historyList = controller.state.searchHistoryList;
      if (historyList.isEmpty) {
        return const SizedBox.shrink();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [_buildHeader(), const SizedBox(height: 2), _buildSearchHistoryList()],
      );
    });
  }

  /// 头部文字部分
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Text('過去に検索したキーワード', style: descriptionTextFontWeight),
        InkWell(
          onTap: controller.onClickClearAllRecordList,
          borderRadius: BorderRadius.circular(8),
          child: const Padding(
            padding: EdgeInsets.all(8.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.close, color: Colors.white, size: 15),
                SizedBox(width: 2),
                Text('履歴をクリア', style: descriptionTextCommon),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 循环历史词条
  Widget _buildSearchHistoryList() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Obx(() {
        final historyList = controller.state.searchHistoryList;
        return Row(
          children: List.generate(historyList.length, (index) {
            final clickSearchInput = historyList[index];
            final isMatched = clickSearchInput == controller.state.searchInput.value;
            return _buildHistoryItem(clickSearchInput, isMatched);
          }),
        );
      }),
    );
  }

  /// 被循环的历史词条
  Widget _buildHistoryItem(String clickSearchInput, bool isMatched) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: SizedBox(
        height: 32,
        child: GestureDetector(
          onTap: () => controller.onChangeUserSearchInput(clickSearchInput),
          child: IntrinsicWidth(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 200),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                decoration: BoxDecoration(
                  color: isMatched ? Colors.white : null,
                  border: Border.all(color: Colors.white),
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                ),
                alignment: Alignment.center,
                child: Text(
                  clickSearchInput,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 12.0,
                    height: 1.5,
                    fontWeight: FontWeight.w700,
                    color: isMatched ? Colors.black : Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
