import 'package:asset_force_mobile_v2/features/search/presentation/controllers/search_condition_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class SearchInputBarWidget extends GetView<SearchConditionController> {
  const SearchInputBarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: Colors.white.withValues(alpha: 0.8), borderRadius: BorderRadius.circular(8)),
      padding: EdgeInsets.zero, // 无需水平内边距
      child: Row(
        children: [
          const SizedBox(width: 15),
          Expanded(
            child: TextField(
              key: const Key('search-condition-page-search-input'),
              style: const TextStyle(color: Color(0xFF323232), fontSize: 15, fontWeight: FontWeight.w100),
              onChanged: controller.onChangeUserSearchInput,
              onSubmitted: (_) => controller.onClickSearch(),
              textInputAction: TextInputAction.search,
              controller: controller.textEditingController,
              decoration: const InputDecoration(
                hintText: '何をお探しですか？',
                hintStyle: TextStyle(color: Colors.black54, fontSize: 15),
                border: InputBorder.none,
                isDense: true,
                contentPadding: EdgeInsets.all(0),
              ),
            ),
          ),
          Obx(() {
            final hasInput = controller.state.searchInput.value.isNotEmpty;
            if (hasInput) {
              return SizedBox(
                width: 32,
                child: IconButton(
                  icon: const Icon(Icons.clear, color: Colors.black, size: 16),
                  onPressed: controller.onClickClearSearchInput,
                ),
              );
            }
            return const SizedBox.shrink();
          }),
          Container(
            decoration: const ShapeDecoration(
              color: Color(0xFF0B3E86),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topRight: Radius.circular(8), bottomRight: Radius.circular(8)),
              ),
            ),
            child: IconButton(
              icon: SvgPicture.asset(
                'assets/icons/icon-search.svg',
                width: 18,
                height: 18,
                colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
              ),
              onPressed: controller.onClickSearch,
            ),
          ),
        ],
      ),
    );
  }
}
