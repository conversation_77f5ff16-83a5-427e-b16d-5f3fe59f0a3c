import 'package:asset_force_mobile_v2/features/search/presentation/controllers/search_condition_controller.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/pages/search_condition_page.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/search_condition.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SavedConditionsWidget extends GetView<SearchConditionController> {
  const SavedConditionsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('保存された検索条件', style: descriptionTextFontWeight),
        const SizedBox(height: 5),
        Obx(() {
          final conditions = controller.state.searchConditions;
          return Column(
            children: List.generate(conditions.length, (index) {
              final condition = conditions[index];
              final isSelected = condition == controller.state.selectedRadioCondition.value;

              return GestureDetector(
                onTap: () => controller.onClickSearchConditionItemToSearch(condition),
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 6),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Radio<SearchConditionModel>(
                        value: condition,
                        groupValue: controller.state.selectedRadioCondition.value,
                        onChanged: (value) {
                          if (value != null) {
                            controller.onClickSearchConditionItemToSearch(value);
                          }
                        },
                        activeColor: const Color(0xFF0B3E86),
                      ),
                      Flexible(
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(0, 10, 11, 10),
                          child: Text(
                            condition.searchName ?? '',
                            style: const TextStyle(color: Colors.black, fontSize: 16, fontWeight: FontWeight.w700),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
          );
        }),
      ],
    );
  }
}
