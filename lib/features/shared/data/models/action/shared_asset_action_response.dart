import 'dart:convert';

import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_asset_item.dart';
import 'package:json_annotation/json_annotation.dart';

part 'shared_asset_action_response.g.dart';

@JsonSerializable()
class SharedAssetActionResponse extends BaseResponse {
  /// 資産データは取得上限数以上にデータ有無を判定
  @JsonKey(name: 'moreThenLimit')
  bool? moreThenLimit;

  List<SharedAssetAction>? assetActionList;

  SharedAssetActionResponse({required super.msg, required super.code, this.moreThenLimit, this.assetActionList});

  factory SharedAssetActionResponse.fromJson(Map<String, dynamic> json) {
    final result = _$SharedAssetActionResponseFromJson(json);

    // 处理两种可能的字段名
    if (json.containsKey('assetActionDataList') && json['assetActionDataList'] != null) {
      result.assetActionList = (json['assetActionDataList'] as List)
          .map((e) => SharedAssetAction.fromJson(e as Map<String, dynamic>))
          .toList();
    } else if (json.containsKey('assetActionList') && json['assetActionList'] != null) {
      result.assetActionList = (json['assetActionList'] as List)
          .map((e) => SharedAssetAction.fromJson(e as Map<String, dynamic>))
          .toList();
    }

    return result;
  }

  Map<String, dynamic> toJson() => _$SharedAssetActionResponseToJson(this);

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}

@JsonSerializable(explicitToJson: true)
class SharedAssetAction {
  /// アクションID
  @JsonKey(name: 'assetActionId')
  int? assetActionId;

  /// アクション名
  @JsonKey(name: 'assetActionName')
  String? assetActionName;

  /// テナントID
  @JsonKey(name: 'tenantId')
  String? tenantId;

  /// 資産種類ID
  @JsonKey(name: 'assetTypeId')
  int? assetTypeId;

  /// 資産種類閲覧グループ
  @JsonKey(name: 'assetTypeGroupIds')
  String? assetTypeGroupIds;

  /// 表示フラグ
  @JsonKey(name: 'displayFlg')
  String? displayFlg;

  /// 資産種類名
  @JsonKey(name: 'assetTypeName')
  String? assetTypeName;

  /// 個数管理フラグ(1:個数管理,0:個体管理)
  @JsonKey(name: 'quantityFlg')
  String? quantityFlg;

  /// アクションの更新項目
  @JsonKey(name: 'assetActionItem')
  String? assetActionItem;

  /// 携帯スキャン条件
  @JsonKey(name: 'scanCondition')
  String? scanCondition;

  /// 付帯情報情報
  @JsonKey(name: 'appurtenancesInformationTypeInfo')
  String? appurtenancesInformationTypeInfo;

  /// 実行可能ユーザー
  @JsonKey(name: 'userIds')
  String? userIds;

  /// 実行可能グループ
  @JsonKey(name: 'groupIds')
  String? groupIds;

  /// 処理内容(数量変更)
  @JsonKey(name: 'isUpdateAmount')
  String? isUpdateAmount;

  /// 処理設定種類(1:純増、2:純減、3:移動)
  @JsonKey(name: 'amountType')
  String? amountType;

  /// 資産の自動読込(0:既存の抽出条件に従う, 1:実行時に条件を指定, 2:事前に条件を指定)
  @JsonKey(name: 'autoFetchAsset')
  String? autoFetchAsset;

  /// 資産条件用セーブデータ
  @JsonKey(name: 'assetConditionSearchName')
  String? assetConditionSearchName;

  /// 自動読込用のSearchId
  @JsonKey(name: 'autoFetchSearchId')
  int? autoFetchSearchId;

  /// 接続モード設定(0:ネット接続モード 1:選択モード 2:オフラインモード)
  @JsonKey(name: 'onlineMode')
  String? onlineMode;

  /// モバイルからのみ実行可能
  @JsonKey(name: 'onlyMobileExecutable')
  String? onlyMobileExecutable;

  @JsonKey(name: 'createdById')
  String? createdById;

  @JsonKey(name: 'createdByName')
  String? createdByName;

  @JsonKey(name: 'createdDate')
  String? createdDate;

  @JsonKey(name: 'modifiedById')
  String? modifiedById;

  @JsonKey(name: 'modifiedByName')
  String? modifiedByName;

  @JsonKey(name: 'modifiedDate')
  String? modifiedDate;

  /// 処理設定のグルピングラベル
  @JsonKey(name: 'actionLabel')
  String? actionLabel;

  /// 処理設定のグルピングアイコン
  @JsonKey(name: 'actionIcon')
  String? actionIcon;

  /// 処理ID(自動採番)
  @JsonKey(name: 'processId')
  final int? processId;

  /// 処理表示名
  @JsonKey(name: 'customerActionName')
  final String? customerActionName;

  /// 資産リスト
  @JsonKey(name: 'assetList')
  final String? assetList;

  /// 処理フラグ（一時保存: 0、実行完了:1、WF関連データ実行中:2）
  @JsonKey(name: 'processType')
  final String? processType;

  /// 遡及日
  @JsonKey(name: 'retroactiveDate')
  final String? retroactiveDate;

  /// プロセスインスタンスID
  @JsonKey(name: 'processInstanceId')
  final String? processInstanceId;

  /// 作成者になるかのフラグ
  @JsonKey(name: 'isCreatedUser')
  final bool? isCreatedUser;

  /// 最新資産情報
  @JsonKey(name: 'nowAssetListJson')
  final String? nowAssetListJson;

  /// 更新者名(更新者がない場合、作成者に設定)
  @JsonKey(name: 'tmpModifiedByName')
  final String? tmpModifiedByName;

  /// 更新日(更新日がない場合、作成日に設定)
  @JsonKey(name: 'tmpModifiedDate')
  final String? tmpModifiedDate;

  /// 処理設定がワークフローに付くかどうかのフラグ
  @JsonKey(name: 'actionBindWorkflowFlag')
  final bool? actionBindWorkflowFlag;

  /// 処理設定新規履歴情報IDリスト
  @JsonKey(name: 'appurtenancesInformationIds')
  final String? appurtenancesInformationIds;

  /// 処理がロックされるかどうか（棚卸用；0:ロックされない、1:ロックされる）
  @JsonKey(name: 'isLocked')
  final String? isLocked;

  /// 処理をロックしたユーザー（棚卸用）
  @JsonKey(name: 'lockedById')
  final String? lockedById;

  /// 処理をロックしたユーザー名（棚卸用）
  @JsonKey(name: 'lockedByName')
  final String? lockedByName;

  /// 新規履歴情報種類IDリスト
  @JsonKey(name: 'appurtenancesInformationTypeIds')
  final String? appurtenancesInformationTypeIds;

  /// 展示されている資産リスト
  @JsonKey(name: 'assetDisplayList')
  final List<AssetDisplay>? assetDisplayList;

  SharedAssetAction({
    this.assetActionId,
    this.assetActionName,
    this.tenantId,
    this.assetTypeId,
    this.assetTypeGroupIds,
    this.displayFlg,
    this.assetTypeName,
    this.quantityFlg,
    this.assetActionItem,
    this.scanCondition,
    this.appurtenancesInformationTypeInfo,
    this.userIds,
    this.groupIds,
    this.isUpdateAmount,
    this.amountType,
    this.autoFetchAsset,
    this.assetConditionSearchName,
    this.autoFetchSearchId,
    this.onlineMode,
    this.onlyMobileExecutable,
    this.createdById,
    this.createdByName,
    this.createdDate,
    this.modifiedById,
    this.modifiedByName,
    this.modifiedDate,
    this.actionLabel,
    this.actionIcon,
    this.processId,
    this.customerActionName,
    this.assetList,
    this.processType,
    this.retroactiveDate,
    this.processInstanceId,
    this.isCreatedUser,
    this.nowAssetListJson,
    this.tmpModifiedByName,
    this.tmpModifiedDate,
    this.actionBindWorkflowFlag,
    this.appurtenancesInformationIds,
    this.isLocked,
    this.lockedById,
    this.lockedByName,
    this.appurtenancesInformationTypeIds,
    this.assetDisplayList,
  });

  factory SharedAssetAction.fromJson(Map<String, dynamic> json) => _$SharedAssetActionFromJson(json);

  Map<String, dynamic> toJson() => _$SharedAssetActionToJson(this);

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}

@JsonSerializable(explicitToJson: true)
class AssetDisplay {
  /// 資産ID(自動採番)
  @JsonKey(name: 'assetId')
  final int? assetId;

  /// 外部システムキー
  @JsonKey(name: 'externalCode')
  final String? externalCode;

  /// テナントID
  @JsonKey(name: 'tenantId')
  final String? tenantId;

  /// 資産種類ID
  @JsonKey(name: 'assetTypeId')
  final int? assetTypeId;

  /// 資産種類名
  @JsonKey(name: 'assetTypeName')
  final String? assetTypeName;

  /// 資産種類グループIDs
  @JsonKey(name: 'groupIds')
  final String? groupIds;

  /// 資産内訳(Json)資産種類毎に設定された項目に従って生成される
  @JsonKey(name: 'assetText')
  final String? assetText;

  /// レイアウト番号
  @JsonKey(name: 'layoutNo')
  final String? layoutNo;

  @JsonKey(name: 'createdById')
  final String? createdById;

  @JsonKey(name: 'createdDate')
  final String? createdDate;

  @JsonKey(name: 'modifiedById')
  final String? modifiedById;

  @JsonKey(name: 'modifiedDate')
  final String? modifiedDate;

  /// ステータス
  @JsonKey(name: 'state')
  final String? state;

  /// 資産名
  @JsonKey(name: 'assetName')
  final String? assetName;

  /// ワークフローID
  @JsonKey(name: 'workflowId')
  final int? workflowId;

  /// 識別コードは30文字以内にしてください
  @JsonKey(name: 'barcode')
  final String? barcode;

  /// 資産の場所
  @JsonKey(name: 'assetLocation')
  final String? assetLocation;

  /// 関連フラグ
  @JsonKey(name: 'relationFlg')
  final bool? relationFlg;

  /// 資産関連資産名
  @JsonKey(name: 'relationAssetDataList')
  final List<dynamic>? relationAssetDataList;

  /// 資産関連リスト
  @JsonKey(name: 'relationAssetIdList')
  final String? relationAssetIdList;

  /// 依存された関連資産の資産IDリスト
  @JsonKey(name: 'dependentRelationAssetIdList')
  final String? dependentRelationAssetIdList;

  /// エラー等のエラーメッセージ（一括操作用）
  @JsonKey(name: 'message')
  final String? message;

  /// グループ権限
  @JsonKey(name: 'jurisdiction')
  final String? jurisdiction;

  /// 関連資産表示カラム登録情報
  @JsonKey(name: 'insertAssetRelationColumnData')
  final String? insertAssetRelationColumnData;

  /// 資産関連更新フラグ
  @JsonKey(name: 'relationNotUpdateFlag')
  final bool? relationNotUpdateFlag;

  /// RFID(イオン対応)
  @JsonKey(name: 'rfid')
  final String? rfid;

  /// コピー用履歴種類ID
  @JsonKey(name: 'copyAppurtenancesInformationTypeIds')
  final String? copyAppurtenancesInformationTypeIds;

  /// 資産予約情報
  @JsonKey(name: 'assetReservationStatusList')
  final String? assetReservationStatusList;

  /// モバイル表示内容(展示されている資産リスト)
  @JsonKey(name: 'assetItemList')
  final List<SharedAssetItem>? assetItemList;

  /// 資産のスキャン数量合計
  @JsonKey(name: 'willChangedAmount')
  final int? willChangedAmount;

  /// データ連携操作タイプ
  @JsonKey(name: 'interactionOperation')
  final String? interactionOperation;

  AssetDisplay({
    this.assetId,
    this.externalCode,
    this.tenantId,
    this.assetTypeId,
    this.assetTypeName,
    this.groupIds,
    this.assetText,
    this.layoutNo,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.state,
    this.assetName,
    this.workflowId,
    this.barcode,
    this.assetLocation,
    this.relationFlg,
    this.relationAssetDataList,
    this.relationAssetIdList,
    this.dependentRelationAssetIdList,
    this.message,
    this.jurisdiction,
    this.insertAssetRelationColumnData,
    this.relationNotUpdateFlag,
    this.rfid,
    this.copyAppurtenancesInformationTypeIds,
    this.assetReservationStatusList,
    this.assetItemList,
    this.willChangedAmount,
    this.interactionOperation,
  });

  factory AssetDisplay.fromJson(Map<String, dynamic> json) => _$AssetDisplayFromJson(json);

  Map<String, dynamic> toJson() => _$AssetDisplayToJson(this);

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}

@JsonSerializable()
class SharedInsertActionResponse extends BaseResponse {
  /// 処理ID(自動採番)
  @JsonKey(name: 'processId')
  final int? processId;

  SharedInsertActionResponse(this.processId, {required super.code, required super.msg});

  factory SharedInsertActionResponse.fromJson(Map<String, dynamic> json) => _$SharedInsertActionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SharedInsertActionResponseToJson(this);

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}
