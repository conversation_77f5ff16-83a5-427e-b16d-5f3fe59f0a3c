import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_asset_item.dart';
import 'package:json_annotation/json_annotation.dart';

part 'shared_get_asset_list_by_asset_id_reponse.g.dart';

@JsonSerializable(explicitToJson: true)
class SharedGetAssetListByAssetIdResponse extends BaseResponse {
  @JsonKey(name: 'assets')
  List<SharedActionAsset>? assets;

  SharedGetAssetListByAssetIdResponse({required super.msg, required super.code, this.assets});

  factory SharedGetAssetListByAssetIdResponse.fromJson(Map<String, dynamic> json) =>
      _$SharedGetAssetListByAssetIdResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SharedGetAssetListByAssetIdResponseToJson(this);
}

@JsonSerializable(explicitToJson: true)
class SharedActionAsset {
  @JsonKey(name: 'assetId')
  int? assetId;
  @JsonKey(name: 'assetTypeId')
  int? assetTypeId;
  @JsonKey(name: 'assetTypeName')
  String? assetTypeName;
  @JsonKey(name: 'assetItemList')
  List<SharedAssetItem>? assetItemList;
  @JsonKey(name: 'assetSettingDetailList')
  List<SharedAssetItem>? assetSettingDetailList;
  @JsonKey(name: 'assetName')
  String? assetName;
  @JsonKey(name: 'quantity')
  int? quantity;
  @JsonKey(name: 'homeImageUrl')
  String? homeImageUrl;
  @JsonKey(name: 'location')
  String? location;
  @JsonKey(name: 'identityCode')
  String? identityCode;

  SharedActionAsset({
    this.assetId,
    this.assetTypeId,
    this.assetTypeName,
    this.assetItemList,
    this.assetSettingDetailList,
    this.assetName,
    this.quantity,
    this.homeImageUrl,
    this.location,
    this.identityCode,
  });

  factory SharedActionAsset.fromJson(Map<String, dynamic> json) => _$SharedActionAssetFromJson(json);

  Map<String, dynamic> toJson() => _$SharedActionAssetToJson(this);
}
