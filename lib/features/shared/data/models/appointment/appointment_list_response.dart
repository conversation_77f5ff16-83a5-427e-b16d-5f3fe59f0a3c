import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'appointment_list_response.g.dart';

/// schedule
@JsonSerializable()
class AppointmentListResponse extends BaseResponse {
  List<ReservationItemCommon?>? reservationItemCommonList;
  List<ReservationItemCommon?>? extraReservationItemCommonList;
  List<EventType?>? eventTypeList;
  List<Appointment?>? appointmentList;

  AppointmentListResponse(
    int code,
    String msg,
    this.reservationItemCommonList,
    this.extraReservationItemCommonList,
    this.eventTypeList,
    this.appointmentList,
  ) : super(code: code, msg: msg);

  factory AppointmentListResponse.fromJson(Map<String, dynamic> json) => _$AppointmentListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AppointmentListResponseToJson(this);
}

@JsonSerializable()
class Appointment {
  /// 予約管理番号
  int? reservationNo;

  /// 資産ID
  int? assetId;

  /// 資産種類
  int? assetTypeId;

  /// 予約名
  String? reservationName;

  /// テナントID
  String? tenantId;

  /// 予約開始日
  String? start;

  /// 予約終了日
  String? end;

  /// イベントタイプID
  int? eventTypeId;

  /// イベントタイプ名
  String? eventTypeName;

  /// 色
  String? color;

  /// 重複フラグ
  String? duplicateFlg;

  /// 予約可能日数
  int? reservationPeriod;

  /// 自動削除設定
  int? autoDeletePeriod;

  /// 予約内訳
  String? reservationText;

  /// 資産内訳(Json)
  String? assetText;

  /// 作成者ID
  String? createdById;

  /// 作成者名前
  String? createdByName;

  /// 作成日時
  String? createdDate;

  /// 更新者ID
  String? modifiedById;

  /// 更新日時
  String? modifiedDate;

  /// 共通内訳
  String? extraCommonText;

  /// 日単位フラグ
  String? unitDay;

  /// アラート設定フラグ
  String? alertSetting;

  dynamic alertData;

  /// extraCommonText to obj

  dynamic extraCommonTextObj;

  /// reservationText to obj

  dynamic reservationTextObj;

  bool? isShowMessage;

  String? showMessage;

  bool? inputRequired;

  String? inputLimitMaxDate;

  String? inputLimitMinDate;

  String? headReservationName;

  String? headEventTypeName;

  dynamic headStart;

  dynamic headEnd;

  Appointment({
    this.reservationNo,
    this.assetId,
    this.assetTypeId,
    this.reservationName,
    this.tenantId,
    this.start,
    this.end,
    this.eventTypeId,
    this.eventTypeName,
    this.color,
    this.duplicateFlg,
    this.reservationPeriod,
    this.autoDeletePeriod,
    this.reservationText,
    this.assetText,
    this.createdById,
    this.createdByName,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.extraCommonText,
    this.unitDay,
    this.alertSetting,
    this.alertData,
    this.extraCommonTextObj,
    this.reservationTextObj,
    this.isShowMessage,
    this.showMessage,
    this.inputRequired,
    this.inputLimitMaxDate,
    this.inputLimitMinDate,
  });

  factory Appointment.fromJson(Map<String, dynamic> json) => _$AppointmentFromJson(json);

  Map<String, dynamic> toJson() => _$AppointmentToJson(this);

  /// 克隆当前 Appointment 对象
  /// 创建一个新的 Appointment 实例，复制所有属性值
  /// 避免内部修改直接影响原始数据
  Appointment clone() {
    return Appointment(
        reservationNo: reservationNo,
        assetId: assetId,
        assetTypeId: assetTypeId,
        reservationName: reservationName,
        tenantId: tenantId,
        start: start,
        end: end,
        eventTypeId: eventTypeId,
        eventTypeName: eventTypeName,
        color: color,
        duplicateFlg: duplicateFlg,
        reservationPeriod: reservationPeriod,
        autoDeletePeriod: autoDeletePeriod,
        reservationText: reservationText,
        assetText: assetText,
        createdById: createdById,
        createdByName: createdByName,
        createdDate: createdDate,
        modifiedById: modifiedById,
        modifiedDate: modifiedDate,
        extraCommonText: extraCommonText,
        unitDay: unitDay,
        alertSetting: alertSetting,
        alertData: alertData,
        extraCommonTextObj: extraCommonTextObj,
        reservationTextObj: reservationTextObj,
        isShowMessage: isShowMessage,
        showMessage: showMessage,
        inputRequired: inputRequired,
        inputLimitMaxDate: inputLimitMaxDate,
        inputLimitMinDate: inputLimitMinDate,
      )
      ..headReservationName = headReservationName
      ..headEventTypeName = headEventTypeName
      ..headStart = headStart
      ..headEnd = headEnd;
  }
}

@JsonSerializable()
class ReservationItemCommon {
  int? itemId;
  String? tenantId;
  String? itemLabel;
  String? itemDisplayName;
  String? itemType;
  String? itemVal;
  String? masterDisplayItemDisplayName;
  dynamic defaultData;
  String? option;
  String? isRequired;
  int? itemSort;
  String? sysSetFlg;
  String? mobileFlg;
  int? createdById;
  String? createdDate;
  int? modifiedById;
  String? modifiedDate;
  dynamic itemValObj;

  ReservationItemCommon(
    this.itemId,
    this.tenantId,
    this.itemLabel,
    this.itemDisplayName,
    this.itemType,
    this.itemVal,
    this.masterDisplayItemDisplayName,
    this.defaultData,
    this.option,
    this.isRequired,
    this.itemSort,
    this.sysSetFlg,
    this.mobileFlg,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.itemValObj,
  );

  factory ReservationItemCommon.fromJson(Map<String, dynamic> json) => _$ReservationItemCommonFromJson(json);

  Map<String, dynamic> toJson() => _$ReservationItemCommonToJson(this);
}

@JsonSerializable()
class EventType {
  String? key;
  String? value;
  String? label;
  String? duplicateFlg;
  int? reservationPeriod;
  String? autoDeletePeriodFlg;
  int? autoDeletePeriod;
  List<EventTypeItem?>? itemList;
  List<EventTypeLayoutSetting?>? layoutSettingList;

  EventType({
    this.key,
    this.value,
    this.label,
    this.duplicateFlg,
    this.reservationPeriod,
    this.autoDeletePeriodFlg,
    this.autoDeletePeriod,
    this.itemList,
    this.layoutSettingList,
  });

  factory EventType.fromJson(Map<String, dynamic> json) => _$EventTypeFromJson(json);

  /// 获取事件类型ID
  /// 使用 key 字段作为 itemId，如果 key 为空则使用 value
  int? get itemId {
    if (key != null && key!.isNotEmpty) {
      return int.tryParse(key!);
    }
    if (value != null && value!.isNotEmpty) {
      return int.tryParse(value!);
    }
    return null;
  }

  Map<String, dynamic> toJson() => _$EventTypeToJson(this);
}

@JsonSerializable()
class EventTypeItem {
  int? tenantId;
  int? itemId;
  int? eventTypeId;
  String? eventTypeName;
  String? itemLabel;
  String? itemDisplayName;
  String? itemType;
  String? itemVal;
  String? masterDisplayItemDisplayName;
  dynamic defaultData;
  String? option;
  String? isRequired;
  String? itemSort;
  String? sysSetFlg;
  String? eventItemIndex;
  int? positionX;
  int? positionY;
  int? width;
  int? height;
  String? createdById;
  String? createdDate;
  String? modifiedById;
  String? modifiedDate;
  String? replaceName;
  String? isCommon;
  String? mobileFlg;
  String? dbtype;

  dynamic itemValObj;

  EventTypeItem(
    this.tenantId,
    this.itemId,
    this.eventTypeId,
    this.eventTypeName,
    this.itemLabel,
    this.itemDisplayName,
    this.itemType,
    this.itemVal,
    this.masterDisplayItemDisplayName,
    this.defaultData,
    this.isRequired,
    this.itemSort,
    this.sysSetFlg,
    this.eventItemIndex,
    this.positionX,
    this.positionY,
    this.width,
    this.height,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.replaceName,
    this.isCommon,
    this.mobileFlg,
    this.dbtype,
    this.option,
  );

  factory EventTypeItem.fromJson(Map<String, dynamic> json) => _$EventTypeItemFromJson(json);

  Map<String, dynamic> toJson() => _$EventTypeItemToJson(this);
}

@JsonSerializable()
class EventTypeLayoutSetting {
  int? itemId;
  int? subItemId;
  int? tenantId;
  int? classification;
  int? typeId;
  String? itemName;
  String? itemDisplayName;
  String? subItemName;
  String? subItemDisplayName;
  String? displayItemName;
  String? itemType;
  String? option;
  String? defaultData;
  String? inputFlg;
  String? uneditableFlg;
  String? mobileFlg;
  String? sectionName;
  String? sectionSort;
  int? positionX;
  int? positionY;
  int? width;
  int? height;
  String? sysSetFlg;
  String? createdById;
  String? createdDate;
  String? modifiedById;
  String? modifiedDate;
  String? replaceName;
  String? columnFlg;
  String? logContext;
  String? dbtype;

  EventTypeLayoutSetting(
    this.itemId,
    this.subItemId,
    this.tenantId,
    this.classification,
    this.typeId,
    this.itemName,
    this.itemDisplayName,
    this.subItemName,
    this.subItemDisplayName,
    this.displayItemName,
    this.itemType,
    this.option,
    this.defaultData,
    this.inputFlg,
    this.uneditableFlg,
    this.mobileFlg,
    this.sectionName,
    this.sectionSort,
    this.positionX,
    this.positionY,
    this.width,
    this.height,
    this.sysSetFlg,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.replaceName,
    this.columnFlg,
    this.logContext,
    this.dbtype,
  );

  factory EventTypeLayoutSetting.fromJson(Map<String, dynamic> json) => _$EventTypeLayoutSettingFromJson(json);

  Map<String, dynamic> toJson() => _$EventTypeLayoutSettingToJson(this);
}
