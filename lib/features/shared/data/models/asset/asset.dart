import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_relation.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_relation_column.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/models/asset_reservation_status.dart';
import 'package:json_annotation/json_annotation.dart';

part 'asset.g.dart'; // 生成されるファイル名に合わせて変更

/// 資産エンティティは、資産のコアビジネスデータ構造を表します。
/// データベースやUI固有の詳細を除き、純粋にビジネスロジックに関連するフィールドのみを含みます。
@JsonSerializable()
class Asset {
  /// 資産ID(自動採番)
  @JsonKey(name: 'assetId')
  final int? assetId;

  /// 外部システムキー
  @JsonKey(name: 'externalCode')
  final String? externalCode;

  /// テナントID
  @JsonKey(name: 'tenantId')
  final String? tenantId;

  /// 資産種類ID
  @JsonKey(name: 'assetTypeId')
  final int? assetTypeId;

  /// 資産種類名
  @JsonKey(name: 'assetTypeName')
  final String? assetTypeName;

  /// 資産種類グループIDs
  @JsonKey(name: 'groupIds')
  final String? groupIds;

  /// 資産内訳(string)
  @JsonKey(name: 'assetText')
  final String? assetText;

  /// 資産内訳(Json)
  Map<String, dynamic>? assetTextObj;

  /// レイアウト番号
  @JsonKey(name: 'layoutNo')
  final String? layoutNo;

  /// 登録者ID
  @JsonKey(name: 'createdById')
  final String? createdById;

  /// 登録日
  @JsonKey(name: 'createdDate')
  final String? createdDate;

  /// 更新者
  @JsonKey(name: 'modifiedById')
  final String? modifiedById;

  /// 更新日
  @JsonKey(name: 'modifiedDate')
  final String? modifiedDate;

  /// ステータス
  @JsonKey(name: 'state')
  final String? state;

  /// 資産名
  @JsonKey(name: 'assetName')
  final String? assetName;

  /// ワークフローID
  @JsonKey(name: 'workflowId')
  final int? workflowId;

  /// 識別コード
  @JsonKey(name: 'barcode')
  final String? barcode;

  /// 資産の場所
  @JsonKey(name: 'assetLocation')
  final String? assetLocation;

  /// 関連フラグ
  @JsonKey(name: 'relationFlg')
  final bool? relationFlg;

  /// 資産関連資産名
  @JsonKey(name: 'relationAssetDataList')
  final List<AssetRelation>? relationAssetDataList;

  /// 資産関連リスト
  @JsonKey(name: 'relationAssetIdList')
  final String? relationAssetIdList;

  /// 依存された関連資産の資産IDリスト
  @JsonKey(name: 'dependentRelationAssetIdList')
  final List<String>? dependentRelationAssetIdList;

  /// エラーメッセージ
  @JsonKey(name: 'message')
  final String? message;

  /// グループ権限
  @JsonKey(name: 'jurisdiction')
  final int? jurisdiction;

  /// 関連資産表示カラム
  @JsonKey(name: 'insertAssetRelationColumnData')
  final List<AssetRelationColumn>? insertAssetRelationColumnData;

  /// 資産関連更新フラグ
  @JsonKey(name: 'relationNotUpdateFlag')
  final bool? relationNotUpdateFlag;

  /// RFID
  @JsonKey(name: 'rfid')
  final String? rfid;

  /// コピー用履歴種類ID
  @JsonKey(name: 'copyAppurtenancesInformationTypeIds')
  final String? copyAppurtenancesInformationTypeIds;

  /// 資産予約情報
  @JsonKey(name: 'assetReservationStatusList')
  final List<AssetReservationStatus>? assetReservationStatusList;

  /// 資産リスト
  @JsonKey(name: 'assetItemList')
  final List<Map<String, dynamic>>? assetItemList;

  /// 資産のスキャン数量合計
  @JsonKey(name: 'willChangedAmount')
  final int? willChangedAmount;

  /// データ連携操作タイプ
  @JsonKey(name: 'interactionOperation')
  final String? interactionOperation;

  Map<String, dynamic>? mobielDisplayList;

  Asset({
    this.assetId,
    this.externalCode,
    this.tenantId,
    this.assetTypeId,
    this.assetTypeName,
    this.groupIds,
    this.assetText,
    this.layoutNo,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.state,
    this.assetName,
    this.workflowId,
    this.barcode,
    this.assetLocation,
    this.relationFlg,
    this.relationAssetDataList,
    this.relationAssetIdList,
    this.dependentRelationAssetIdList,
    this.message,
    this.jurisdiction,
    this.insertAssetRelationColumnData,
    this.relationNotUpdateFlag,
    this.rfid,
    this.copyAppurtenancesInformationTypeIds,
    this.assetReservationStatusList,
    this.assetItemList,
    this.willChangedAmount,
    this.interactionOperation,
    this.assetTextObj,
    this.mobielDisplayList,
  });

  /// JSONからAssetオブジェクトを生成するファクトリコンストラクタ
  factory Asset.fromJson(Map<String, dynamic> json) => _$AssetFromJson(json);

  /// AssetオブジェクトをJSONに変換するメソッド
  Map<String, dynamic> toJson() => _$AssetToJson(this);
}
