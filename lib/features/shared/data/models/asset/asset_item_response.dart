import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'asset_item_response.g.dart';

@JsonSerializable()
class AssetItemResponse extends BaseResponse {
  List<AssetItemListModel?>? assetItemList;

  AssetItemResponse({required super.code, required super.msg, this.assetItemList});

  factory AssetItemResponse.fromJson(Map<String, dynamic> json) {
    return _$AssetItemResponseFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AssetItemResponseToJson(this);
}

@JsonSerializable(explicitToJson: true)
class AssetItemListModel {
  int? itemId;
  int? subItemId;
  String? itemIdStr;
  String? subItemName;
  String? subItemType;
  String? displayItemName;
  String? tenantId;
  int? assetTypeId;
  String? itemName;
  String? itemDisplayName;
  String? itemType;
  String? option;
  dynamic defaultData;
  String? inputFlg;
  String? uneditableFlg;
  String? layoutSetFlg;
  String? sectionName;
  String? sectionType;
  int? sectionSort;
  int? positionX;
  int? positionY;
  int? width;
  int? height;
  String? sysSetFlg;
  String? createdById;
  String? createdDate;
  String? modifiedById;
  String? modifiedDate;
  String? mobileFlg;
  String? replaceName;
  String? itemIds;
  String? sectionPrivateGroupsPermissionCheckLog;
  String? sectionPrivateEditGroupsPermissionCheckLog;
  String? sectionPrivateGroupsPermissionCheckId;
  String? sectionPrivateEditGroupsPermissionCheckId;
  String? sectionPrivateGroupsName;
  String? sectionPrivateEditGroupsName;
  String? appurtenancesInformationTypeNames;
  String? masterTypeNames;
  String? dbtype;
  bool? commonItem;
  bool? isShowMessage;
  String? showMessage;

  //!============================= 处理设定 ==================================
  bool? editable;

  //!=============================非 API 返回字段=============================

  bool? isValid;

  /// show message for custom logic check
  Object? isShowMessageTS;

  /// 項目の下にメッセージを表示するか
  Object? itemMsgIsShow;

  Object? undisplayItemNameList;

  ///マスター項目の初期値を設定された場合、masterIdを格納する
  String? masterId;

  /// 单位符号
  String? unit;

  /// 百分比表示
  String? percentage;

  OptionObjModel? optionObject;

  String? isEditPermissions;

  /// 設定値
  dynamic itemValue;

  ///  静的："１"、　動的："２"
  String? method;

  /// 显示值
  dynamic valueForShow;

  /// 履历情报 页面个数
  int? dataCounts;

  /// master
  List? displayList;

  AssetItemListModel({
    this.itemId,
    this.itemIdStr,
    this.subItemId,
    this.subItemName,
    this.subItemType,
    this.displayItemName,
    this.tenantId,
    this.assetTypeId,
    this.itemName,
    this.itemDisplayName,
    this.itemType,
    this.option,
    this.defaultData,
    this.inputFlg,
    this.uneditableFlg,
    this.layoutSetFlg,
    this.sectionName,
    this.sectionType,
    this.sectionSort,
    this.positionX,
    this.positionY,
    this.width,
    this.height,
    this.sysSetFlg,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.mobileFlg,
    this.replaceName,
    this.itemIds,
    this.sectionPrivateGroupsPermissionCheckLog,
    this.sectionPrivateEditGroupsPermissionCheckLog,
    this.sectionPrivateGroupsPermissionCheckId,
    this.sectionPrivateEditGroupsPermissionCheckId,
    this.sectionPrivateGroupsName,
    this.sectionPrivateEditGroupsName,
    this.appurtenancesInformationTypeNames,
    this.masterTypeNames,
    this.dbtype,
    this.commonItem,
    this.isShowMessage,
    this.showMessage,
  });

  factory AssetItemListModel.fromJson(Map<String, dynamic> json) => _$AssetItemListModelFromJson(json);

  Map<String, dynamic> toJson() => _$AssetItemListModelToJson(this);
}
