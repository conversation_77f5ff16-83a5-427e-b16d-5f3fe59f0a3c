import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';
import 'package:json_annotation/json_annotation.dart';

part 'asset_layout_setting_response.g.dart';

@JsonSerializable()
class AssetLayoutSettingResponse {
  final List<SharedLayoutSetting> layoutSettingList;
  final List<String>? appurtenanceDeletedItemNames;

  /// 共通関数設定 - フォーム画面スクリプト
  final String? commonJS;
  final String? js;
  final String? appurJsOnSave;

  AssetLayoutSettingResponse({
    required this.layoutSettingList,
    this.appurtenanceDeletedItemNames,
    this.commonJS,
    this.js,
    this.appurJsOnSave,
  });

  factory AssetLayoutSettingResponse.fromJson(Map<String, dynamic> json) => _$AssetLayoutSettingResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AssetLayoutSettingResponseToJson(this);
}
