import 'package:json_annotation/json_annotation.dart';

part 'asset_relation_list_response.g.dart';

@JsonSerializable()
class AssetRelationListResponse {
  String? msg;
  int? code;
  List<AssetRelationItem?>? assetRelationList;

  AssetRelationListResponse(this.assetRelationList);

  factory AssetRelationListResponse.fromJson(Map<String, dynamic> json) => _$AssetRelationListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AssetRelationListResponseToJson(this);
}

@JsonSerializable()
class AssetRelationItem {
  String? tenantId;
  int? assetId;
  int? assetTypeId;
  int? relationAssetId;
  String? assetText;
  String? relationType;
  int? createdById;
  String? createdDate;
  int? modifiedById;
  String? modifiedDate;
  String? assetTypeName;
  String? state;

  @JsonKey(includeToJson: false, includeFromJson: false)
  Map<String, dynamic>? assetTextObj;

  AssetRelationItem(
    this.tenantId,
    this.assetId,
    this.assetTypeId,
    this.relationAssetId,
    this.assetText,
    this.relationType,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.assetTypeName,
    this.state,
  );

  factory AssetRelationItem.fromJson(Map<String, dynamic> json) => _$AssetRelationItemFromJson(json);

  Map<String, dynamic> toJson() => _$AssetRelationItemToJson(this);
}
