import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'asset_type_sort_response.g.dart';

@JsonSerializable()
class AssetTypeResponse extends BaseResponse {
  String? commonJS;
  List<AssetTypeListModel?>? assetTypeList;
  String? js;
  String? jsQRlogic;
  String? jsSaveAsset;
  String? rememberedAssetType;

  AssetTypeResponse({
    required super.code,
    required super.msg,
    this.commonJS,
    this.assetTypeList,
    this.js,
    this.jsQRlogic,
    this.jsSaveAsset,
    this.rememberedAssetType,
  });

  factory AssetTypeResponse.fromJson(Map<String, dynamic> json) => _$AssetTypeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AssetTypeResponseToJson(this);
}

@JsonSerializable()
class AssetTypeListModel {
  int? assetTypeId;
  String? tenantId;
  String? assetTypeName;
  String? itemFlg;
  String? layoutFlg;
  String? layoutTempSaveFlg;
  String? quantityFlg;
  String? createdById;
  String? createdDate;
  String? modifiedById;
  String? modifiedByName;
  String? modifiedDate;
  String? displayModifiedDate;
  String? groupIds;
  String? alertCount;
  String? displayFlg;
  String? groupPermissionCheckLog;

  AssetTypeListModel({
    this.assetTypeId,
    this.tenantId,
    this.assetTypeName,
    this.itemFlg,
    this.layoutFlg,
    this.layoutTempSaveFlg,
    this.quantityFlg,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedByName,
    this.modifiedDate,
    this.displayModifiedDate,
    this.groupIds,
    this.alertCount,
    this.displayFlg,
    this.groupPermissionCheckLog,
  });

  factory AssetTypeListModel.fromJson(Map<String, dynamic> json) => _$AssetTypeListModelFromJson(json);

  Map<String, dynamic> toJson() => _$AssetTypeListModelToJson(this);
}
