import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset_type/search_condition.dart';
import 'package:json_annotation/json_annotation.dart';

part 'search_conditions_result.g.dart';

/// 搜索条件结果类
@JsonSerializable()
class SearchConditionsResult extends BaseResponse {
  final List<SearchConditionModel>? assetSearchNameList;

  /// 検索条件の名前。
  final String? rememberedAssetSearchId;

  SearchConditionsResult({
    this.assetSearchNameList,
    this.rememberedAssetSearchId,
    required super.code,
    required super.msg,
  });

  /// JSONからSearchConditionsResultオブジェクトを生成するファクトリコンストラクタ
  factory SearchConditionsResult.fromJson(Map<String, dynamic> json) => _$SearchConditionsResultFromJson(json);

  /// SearchConditionsResultオブジェクトをJSONに変換するメソッド
  Map<String, dynamic> toJson() => _$SearchConditionsResultToJson(this);
}
