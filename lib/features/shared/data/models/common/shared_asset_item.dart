import 'package:json_annotation/json_annotation.dart';

part 'shared_asset_item.g.dart';

@JsonSerializable(explicitToJson: true)
class SharedAssetItem {
  @Json<PERSON>ey(name: 'itemId')
  final int? itemId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'itemDisplayName')
  final String? itemDisplayName;

  @J<PERSON><PERSON>ey(name: 'itemType')
  final String? itemType;

  @Json<PERSON>ey(name: 'value')
  final String? value;

  SharedAssetItem({this.itemId, this.itemDisplayName, this.itemType, this.value});

  factory SharedAssetItem.fromJson(Map<String, dynamic> json) => _$SharedAssetItemFromJson(json);

  Map<String, dynamic> toJson() => _$SharedAssetItemToJson(this);
}
