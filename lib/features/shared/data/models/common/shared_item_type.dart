import 'package:json_annotation/json_annotation.dart';
part 'shared_item_type.g.dart';

/// 資産モバイル設定（モバイル側利用）情報クラス
@JsonSerializable()
class SharedItemType {
  /// 項目種類（資産種類）
  @<PERSON><PERSON><PERSON><PERSON>(name: 'itemType')
  String? itemType;

  /// 項目名
  @Json<PERSON>ey(name: 'itemName')
  String? itemName;

  /// 値
  @JsonKey(name: 'itemVal')
  String? itemVal;

  /// オプション
  @JsonKey(name: 'itemOption')
  String? itemOption;

  SharedItemType({this.itemType, this.itemName, this.itemVal, this.itemOption});

  factory SharedItemType.fromJson(Map<String, dynamic> json) => _$SharedItemTypeFromJson(json);

  Map<String, dynamic> toJson() => _$SharedItemTypeToJson(this);

  @override
  String toString() {
    return 'SharedItemType{itemType: $itemType, itemName: $itemName, itemVal: $itemVal, itemOption: $itemOption}';
  }
}
