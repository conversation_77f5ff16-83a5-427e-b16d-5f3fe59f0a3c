import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'shared_layout_setting_0_5.g.dart'; // 生成的代码文件

@JsonSerializable()
class SharedLayoutSetting05Response extends BaseResponse {
  @JsonKey(name: 'layoutSettingList')
  List<SharedLayoutSetting05>? layoutSettingList;

  SharedLayoutSetting05Response({this.layoutSettingList, required super.code, required super.msg});

  factory SharedLayoutSetting05Response.fromJson(Map<String, dynamic> json) =>
      _$SharedLayoutSetting05ResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SharedLayoutSetting05ResponseToJson(this);
}

@JsonSerializable()
class SharedLayoutSetting05 {
  /// 項目ID(自動採番)
  @JsonKey(name: 'itemId')
  int? itemId;

  /// サッブ項目ID
  @JsonKey(name: 'subItemId')
  int? subItemId;

  /// テナントID
  @JsonKey(name: 'tenantId')
  String? tenantId;

  /// タイプ分類 (1.資産, 2.アクション, 3.イベント)
  @JsonKey(name: 'classification')
  int? classification;

  /// タイプID
  @JsonKey(name: 'typeId')
  int? typeId;

  /// 項目名称
  @JsonKey(name: 'itemName')
  String? itemName;

  /// 項目表示名称
  @JsonKey(name: 'itemDisplayName')
  String? itemDisplayName;

  /// サッブ項目名称
  @JsonKey(name: 'subItemName')
  String? subItemName;

  /// サッブ項目表示名称
  @JsonKey(name: 'subItemDisplayName')
  String? subItemDisplayName;

  /// 表示項目名称
  @JsonKey(name: 'displayItemName')
  String? displayItemName;

  /// 項目タイプ
  @JsonKey(name: 'itemType')
  String? itemType;

  /// リストデータ
  @JsonKey(name: 'option')
  String? option;

  /// 初期値
  @JsonKey(name: 'defaultData')
  String? defaultData;

  /// 必須入力フラグ
  @JsonKey(name: 'inputFlg')
  String? inputFlg;

  /// レイアウト設定画面編集不可フラグ
  @JsonKey(name: 'uneditableFlg')
  String? uneditableFlg;

  /// モバイルフラグ (0:非表示;1:表示)
  @JsonKey(name: 'mobileFlg')
  String? mobileFlg;

  /// Section名
  @JsonKey(name: 'sectionName')
  String? sectionName;

  /// Sectionソート順
  @JsonKey(name: 'sectionSort')
  double? sectionSort;

  /// 画面座標X
  @JsonKey(name: 'positionX')
  int? positionX;

  /// 画面座標Y
  @JsonKey(name: 'positionY')
  int? positionY;

  /// 幅
  @JsonKey(name: 'width')
  int? width;

  /// 高さ
  @JsonKey(name: 'height')
  int? height;

  /// システム設定フラグ
  @JsonKey(name: 'sysSetFlg')
  String? sysSetFlg;

  /// 登録者
  @JsonKey(name: 'createdById')
  String? createdById;

  /// 登録日
  @JsonKey(name: 'createdDate')
  String? createdDate;

  /// 更新者
  @JsonKey(name: 'modifiedById')
  String? modifiedById;

  /// 更新日
  @JsonKey(name: 'modifiedDate')
  String? modifiedDate;

  /// DBタイプ
  @JsonKey(name: 'dbtype')
  String? dbType;

  @JsonKey(name: 'replaceName')
  String? replaceName;

  @JsonKey(name: 'columnFlg')
  String? columnFlg;

  @JsonKey(name: 'logContext')
  String? logContext;

  SharedLayoutSetting05({
    this.itemId,
    this.subItemId,
    this.tenantId,
    this.classification,
    this.typeId,
    this.itemName,
    this.itemDisplayName,
    this.subItemName,
    this.subItemDisplayName,
    this.displayItemName,
    this.itemType,
    this.option,
    this.defaultData,
    this.inputFlg,
    this.uneditableFlg,
    this.mobileFlg,
    this.sectionName,
    this.sectionSort,
    this.positionX,
    this.positionY,
    this.width,
    this.height,
    this.sysSetFlg,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.dbType,
    this.replaceName,
    this.columnFlg,
    this.logContext,
  });

  // JSON序列化和反序列化的支持方法
  factory SharedLayoutSetting05.fromJson(Map<String, dynamic> json) => _$SharedLayoutSetting05FromJson(json);

  Map<String, dynamic> toJson() => _$SharedLayoutSetting05ToJson(this);
}
