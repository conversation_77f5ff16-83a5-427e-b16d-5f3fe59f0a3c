import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'shared_s3_upload_url_response.g.dart';

/// S3上传URL响应的模型类
/// 继承自[BaseResponse]基础响应类
@JsonSerializable()
class SharedS3UploadUrlResponse extends BaseResponse {
  /// 响应数据
  /// 包含获取URL、文件路径和上传URL等信息
  final Data? data;

  /// 构造函数
  /// [data] 响应数据
  /// [code] 响应状态码
  /// [msg] 响应消息
  SharedS3UploadUrlResponse({required this.data, required super.code, required super.msg});

  /// 从JSON数据中反序列化
  factory SharedS3UploadUrlResponse.fromJson(Map<String, dynamic> json) => _$SharedS3UploadUrlResponseFromJson(json);

  /// 序列化为JSON数据
  Map<String, dynamic> toJson() => _$SharedS3UploadUrlResponseToJson(this);

  /// 获取上传用 URL
  String getUploadUrl() {
    return data?.url ?? '';
  }
}

/// S3上传URL响应的数据模型类
@JsonSerializable()
class Data {
  /// 获取URL
  final String? getUrl;

  /// 文件路径
  final String? path;

  /// 上传URL
  final String? url;

  /// 构造函数
  /// [getUrl] 获取URL
  /// [path] 文件路径
  /// [url] 上传URL
  Data({this.getUrl, this.path, this.url});

  /// 从JSON数据中反序列化
  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  /// 序列化为JSON数据
  Map<String, dynamic> toJson() => _$DataToJson(this);
}
