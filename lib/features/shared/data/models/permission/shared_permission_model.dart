import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'shared_permission_model.g.dart';

@JsonSerializable()
class SharedPermissionResponse extends BaseResponse {
  @JsonKey(name: 'permissionList')
  List<SharedPermissionModel>? permissionList;

  SharedPermissionResponse({this.permissionList, required super.msg, required super.code});

  factory SharedPermissionResponse.fromJson(Map<String, dynamic> json) => _$SharedPermissionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SharedPermissionResponseToJson(this);
}

@JsonSerializable()
class SharedPermissionModel {
  @JsonKey(name: 'roleId')
  int? roleId;
  @JsonKey(name: 'tenantId')
  String? tenantId;
  @JsonKey(name: 'type')
  String? type;
  @Json<PERSON>ey(name: 'functionId')
  String? functionId;
  @Json<PERSON>ey(name: 'pageComponent')
  String? pageComponent;
  @JsonKey(name: 'pageId')
  String? pageId;
  @Json<PERSON>ey(name: 'resourceId')
  int? resourceId;
  @JsonKey(name: 'resource')
  String? resource;
  @JsonKey(name: 'createdById')
  String? createdById;
  @JsonKey(name: 'createdDate')
  String? createdDate;
  @JsonKey(name: 'modifiedById')
  String? modifiedById;
  @JsonKey(name: 'modifiedDate')
  String? modifiedDate;

  SharedPermissionModel({
    this.roleId,
    this.tenantId,
    this.type,
    this.functionId,
    this.pageComponent,
    this.pageId,
    this.resourceId,
    this.resource,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
  });

  factory SharedPermissionModel.fromJson(Map<String, dynamic> json) => _$SharedPermissionModelFromJson(json);

  Map<String, dynamic> toJson() => _$SharedPermissionModelToJson(this);
}
