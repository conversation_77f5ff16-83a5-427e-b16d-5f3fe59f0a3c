import 'package:json_annotation/json_annotation.dart';

part 'shared_tenant_model.g.dart'; // 自动生成的序列化代码文件

/// テナントマスタクラス
@JsonSerializable()
class SharedTenantModel {
  /// テナントID
  @Json<PERSON><PERSON>(name: 'tenantId')
  final String? tenantId;

  /// テナント名
  @JsonKey(name: 'tenantName')
  final String? tenantName;

  /// 管理者メール
  @JsonKey(name: 'managerMail')
  final String? managerMail;

  /// プラン(0：ベーシックプラン　1：プレミアムプラン)
  @JsonKey(name: 'membership')
  final int? membership;

  /// 組織
  @JsonKey(name: 'organzationName')
  final String? organzationName;

  /// 削除状態(0:正常　1：削除中)
  @JsonKey(name: 'delStatus')
  final String? delStatus;

  /// 削除ユーザー
  @JsonKey(name: 'delUser')
  final String? delUser;

  /// 登録者
  @J<PERSON><PERSON><PERSON>(name: 'createdById')
  final String? createdById;

  /// 登録日
  @JsonKey(name: 'createdDate')
  final String? createdDate;

  /// 更新者
  @Json<PERSON><PERSON>(name: 'modifiedById')
  final String? modifiedById;

  /// 更新日
  @JsonKey(name: 'modifiedDate')
  final String? modifiedDate;

  /// 有効無効フラグ  0：有効    1：無効
  @JsonKey(name: 'disableFlg')
  final String? disableFlg;

  /// ロックフラグ  1：ロック
  @JsonKey(name: 'lockedFlg')
  final String? lockedFlg;

  /// bi database's id
  @JsonKey(name: 'databaseId')
  final int? databaseId;

  /// ログイン用UUID
  @JsonKey(name: 'uuid')
  final String? uuid;

  /// 多要素認証
  @JsonKey(name: 'enableTwoStep')
  final bool? enableTwoStep;

  /// IP制限フラグ
  @JsonKey(name: 'ipSetting')
  final String? ipSetting;

  /// デフォルトレポートID
  @JsonKey(name: 'defaultDashboardId')
  final int? defaultDashboardId;

  /// アクセスキー利用フラグ
  @JsonKey(name: 'accessKeyKbn')
  final String? accessKeyKbn;

  /// デフォルトシステム
  @JsonKey(name: 'defaultSystem')
  final String? defaultSystem;

  /// プリンタ機能利用可否フラグ
  @JsonKey(name: 'printEnableFlg')
  final String? printEnableFlg;

  /// 帳票機能利用可否フラグ
  @JsonKey(name: 'reportEnableFlg')
  final String? reportEnableFlg;

  /// アクションスクリプト機能利用可否フラグ
  @JsonKey(name: 'actionScriptEnableFlg')
  final String? actionScriptEnableFlg;

  /// ワークブック機能利用可否フラグ
  @JsonKey(name: 'workbookEnableFlg')
  final String? workbookEnableFlg;

  /// 使用システム
  @JsonKey(name: 'useSystems')
  final String? useSystems;

  /// プラン
  @JsonKey(name: 'plan')
  final String? plan;

  /// プラン名前
  @JsonKey(name: 'planName')
  final String? planName;

  /// コピー先テナントID
  @JsonKey(name: 'sourceTenantId')
  final String? sourceTenantId;

  /// コピー先テナントID
  @JsonKey(name: 'sourceTenantName')
  final String? sourceTenantName;

  /// Liveセクション表示フラグ(0/NULL: 非表示, 1: 表示)
  @JsonKey(name: 'liveSection')
  final String? liveSection;

  /// 関連資産依存機能利用フラグ
  @JsonKey(name: 'relationAssetDependentFlg')
  final String? relationAssetDependentFlg;

  /// 資産件数の上限
  @JsonKey(name: 'assetNumberMax')
  final String? assetNumberMax;

  /// 使用容量の上限
  @JsonKey(name: 'capacityMax')
  final String? capacityMax;

  /// 現在の使用容量
  @JsonKey(name: 'dayCapacityMax')
  final String? dayCapacityMax;

  /// メンテ通知(1：要，0：不要)
  @JsonKey(name: 'maintenanceNoticeFlg')
  final String? maintenanceNoticeFlg;

  /// MFAメール利用フラグ
  @JsonKey(name: 'mfaMailEnableFlg')
  final String? mfaMailEnableFlg;

  /// MFA SMS利用フラグ
  @JsonKey(name: 'mfaSmsEnableFlg')
  final String? mfaSmsEnableFlg;

  /// MFA利用な認証方式
  @JsonKey(name: 'mfaType')
  final String? mfaType;

  /// MFA有効なグループ
  @JsonKey(name: 'mfaGroupIds')
  final String? mfaGroupIds;

  /// 管理者名前（姓）
  @JsonKey(name: 'managerLastName')
  final String? managerLastName;

  /// 管理者名前（名）
  @JsonKey(name: 'managerFirstName')
  final String? managerFirstName;

  /// ワークブックID利用数
  @JsonKey(name: 'workbookLicenseInUse')
  final int? workbookLicenseInUse;

  /// ワークブックID付与数
  @JsonKey(name: 'workbookLicenseMax')
  final String? workbookLicenseMax;

  /// マテビューフラグ
  @JsonKey(name: 'materialisticViewFlg')
  final String? materialisticViewFlg;

  /// openSearch利用フラグ
  @JsonKey(name: 'openSearchFlg')
  final String? openSearchFlg;

  /// openSearch設定時間
  @JsonKey(name: 'openSearchFlgSetDate')
  final String? openSearchFlgSetDate;

  /// ゾーンID
  @JsonKey(name: 'zoneId')
  final String? zoneId;

  /// ゾーン名
  @JsonKey(name: 'zoneName')
  final String? zoneName;

  /// シングルサインオン
  @JsonKey(name: 'enableSso')
  final bool? enableSso;

  /// SSO認証フラグ
  @JsonKey(name: 'allowEnableSso')
  final String? allowEnableSso;

  /// プロダクト分類
  @JsonKey(name: 'productKbn')
  final String? productKbn;

  /// 利用目的（ID）
  @JsonKey(name: 'usePurpose')
  final String? usePurpose;

  /// FL担当者（姓）
  @JsonKey(name: 'flLastName')
  final String? flLastName;

  /// FL担当者（名）
  @JsonKey(name: 'flFirstName')
  final String? flFirstName;

  /// 使用するIDプロバイダ
  @JsonKey(name: 'idpName')
  final String? idpName;

  SharedTenantModel({
    this.tenantId,
    this.tenantName,
    this.managerMail,
    this.membership,
    this.organzationName,
    this.delStatus,
    this.delUser,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.disableFlg,
    this.lockedFlg,
    this.databaseId,
    this.uuid,
    this.enableTwoStep,
    this.ipSetting,
    this.defaultDashboardId,
    this.accessKeyKbn,
    this.defaultSystem,
    this.printEnableFlg,
    this.reportEnableFlg,
    this.actionScriptEnableFlg,
    this.workbookEnableFlg,
    this.useSystems,
    this.plan,
    this.planName,
    this.sourceTenantId,
    this.sourceTenantName,
    this.liveSection,
    this.relationAssetDependentFlg,
    this.assetNumberMax,
    this.capacityMax,
    this.dayCapacityMax,
    this.maintenanceNoticeFlg,
    this.mfaMailEnableFlg,
    this.mfaSmsEnableFlg,
    this.mfaType,
    this.mfaGroupIds,
    this.managerLastName,
    this.managerFirstName,
    this.workbookLicenseInUse,
    this.workbookLicenseMax,
    this.materialisticViewFlg,
    this.openSearchFlg,
    this.openSearchFlgSetDate,
    this.zoneId,
    this.zoneName,
    this.enableSso,
    this.allowEnableSso,
    this.productKbn,
    this.usePurpose,
    this.flLastName,
    this.flFirstName,
    this.idpName,
  });

  bool isOpenSearch() {
    try {
      if (openSearchFlg == '1' && openSearchFlgSetDate != null) {
        final DateTime nowTime = DateTime.now();
        final DateTime openSearchDate = DateTime.parse(openSearchFlgSetDate!);
        final DateTime nextDayOpenSearchDate = openSearchDate
            .add(const Duration(days: 1))
            .subtract(const Duration(hours: 5));
        return nextDayOpenSearchDate.isBefore(nowTime);
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// `fromJson` 生成器
  factory SharedTenantModel.fromJson(Map<String, dynamic> json) => _$SharedTenantModelFromJson(json);

  /// `toJson` 生成器
  Map<String, dynamic> toJson() => _$SharedTenantModelToJson(this);
}
