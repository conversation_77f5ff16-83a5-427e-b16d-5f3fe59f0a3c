import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'tenant_info_response.g.dart';

@JsonSerializable()
class TenantInfoResponse extends BaseResponse {
  bool? hasAssetLocation;
  List<TenantInfoResponseAssetScanListSetting?>? assetScanListSetting;
  String? barcodeExtraction;
  String? planName;
  TenantInfoResponseTenant? tenant;
  TenantInfoResponsePolicy? policy;

  TenantInfoResponse(
    String msg,
    this.hasAssetLocation,
    this.assetScanListSetting,
    int code,
    this.barcodeExtraction,
    this.planName,
    this.tenant,
    this.policy,
  ) : super(code: code, msg: msg);

  factory TenantInfoResponse.fromJson(Map<String, dynamic> json) => _$TenantInfoResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TenantInfoResponseToJson(this);
}

@JsonSerializable()
class TenantInfoResponseAssetScanListSetting {
  String? tenantId;
  int? scanLevelId;
  int? scanItemId;
  dynamic itemName;
  dynamic itemDisplayName;
  dynamic itemType;
  dynamic itemOption;
  dynamic createdById;
  dynamic createdDate;
  dynamic modifiedById;
  String? modifiedDate;

  TenantInfoResponseAssetScanListSetting(
    this.tenantId,
    this.scanLevelId,
    this.scanItemId,
    this.itemName,
    this.itemDisplayName,
    this.itemType,
    this.itemOption,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
  );

  factory TenantInfoResponseAssetScanListSetting.fromJson(Map<String, dynamic> json) =>
      _$TenantInfoResponseAssetScanListSettingFromJson(json);

  Map<String, dynamic> toJson() => _$TenantInfoResponseAssetScanListSettingToJson(this);
}

@JsonSerializable()
class TenantInfoResponseTenant {
  String? tenantId;
  String? tenantName;
  String? managerMail;
  int? membership;
  dynamic organzationName;
  dynamic delStatus;
  dynamic delUser;
  String? createdById;
  String? createdDate;
  String? modifiedById;
  String? modifiedDate;
  dynamic disableFlg;
  dynamic lockedFlg;
  dynamic databaseId;
  String? uuid;
  bool? enableTwoStep;
  String? ipSetting;
  dynamic defaultDashboardId;
  String? accessKeyKbn;
  String? defaultSystem;
  String? printEnableFlg;
  String? reportEnableFlg;
  String? actionScriptEnableFlg;
  String? workbookEnableFlg;
  String? useSystems;
  String? plan;
  dynamic planName;
  dynamic sourceTenantId;
  dynamic sourceTenantName;
  String? liveSection;
  String? relationAssetDependentFlg;
  dynamic assetNumberMax;
  dynamic capacityMax;
  dynamic dayCapacityMax;
  dynamic maintenanceNoticeFlg;
  String? mfaMailEnableFlg;
  String? mfaSmsEnableFlg;
  dynamic mfaType;
  dynamic mfaGroupIds;
  dynamic managerLastName;
  dynamic managerFirstName;
  dynamic workbookLicenseInUse;
  dynamic workbookLicenseMax;
  String? materialisticViewFlg;
  dynamic openSearchFlg;
  dynamic openSearchFlgSetDate;
  dynamic zoneId;
  dynamic zoneName;
  bool? enableSso;
  dynamic allowEnableSso;
  dynamic productKbn;
  dynamic usePurpose;
  dynamic flLastName;
  dynamic flFirstName;
  dynamic idpName;
  int? openSearchValidDelayDays;
  bool? openSearch;

  TenantInfoResponseTenant(
    this.tenantId,
    this.tenantName,
    this.managerMail,
    this.membership,
    this.organzationName,
    this.delStatus,
    this.delUser,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.disableFlg,
    this.lockedFlg,
    this.databaseId,
    this.uuid,
    this.enableTwoStep,
    this.ipSetting,
    this.defaultDashboardId,
    this.accessKeyKbn,
    this.defaultSystem,
    this.printEnableFlg,
    this.reportEnableFlg,
    this.actionScriptEnableFlg,
    this.workbookEnableFlg,
    this.useSystems,
    this.plan,
    this.planName,
    this.sourceTenantId,
    this.sourceTenantName,
    this.liveSection,
    this.relationAssetDependentFlg,
    this.assetNumberMax,
    this.capacityMax,
    this.dayCapacityMax,
    this.maintenanceNoticeFlg,
    this.mfaMailEnableFlg,
    this.mfaSmsEnableFlg,
    this.mfaType,
    this.mfaGroupIds,
    this.managerLastName,
    this.managerFirstName,
    this.workbookLicenseInUse,
    this.workbookLicenseMax,
    this.materialisticViewFlg,
    this.openSearchFlg,
    this.openSearchFlgSetDate,
    this.zoneId,
    this.zoneName,
    this.enableSso,
    this.allowEnableSso,
    this.productKbn,
    this.usePurpose,
    this.flLastName,
    this.flFirstName,
    this.idpName,
    this.openSearchValidDelayDays,
    this.openSearch,
  );

  factory TenantInfoResponseTenant.fromJson(Map<String, dynamic> json) => _$TenantInfoResponseTenantFromJson(json);

  Map<String, dynamic> toJson() => _$TenantInfoResponseTenantToJson(this);
}

@JsonSerializable()
class TenantInfoResponsePolicy {
  int? policyId;
  String? tenantId;
  dynamic expireTime;
  String? mustContain;
  String? enableFlg;
  int? length;
  dynamic repeatForbidCount;
  String? lockEnableFlg;
  int? lockTimes;
  dynamic lockPeriod;
  double? strengthBase;
  int? strengthLength;
  String? strength;
  String? createdById;
  String? createdDate;
  String? modifiedById;
  String? modifiedDate;
  dynamic tenantName;

  TenantInfoResponsePolicy(
    this.policyId,
    this.tenantId,
    this.expireTime,
    this.mustContain,
    this.enableFlg,
    this.length,
    this.repeatForbidCount,
    this.lockEnableFlg,
    this.lockTimes,
    this.lockPeriod,
    this.strengthBase,
    this.strengthLength,
    this.strength,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.tenantName,
  );

  factory TenantInfoResponsePolicy.fromJson(Map<String, dynamic> json) => _$TenantInfoResponsePolicyFromJson(json);

  Map<String, dynamic> toJson() => _$TenantInfoResponsePolicyToJson(this);
}
