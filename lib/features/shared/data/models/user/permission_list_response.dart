import 'package:json_annotation/json_annotation.dart';

part 'permission_list_response.g.dart';

@JsonSerializable()
class PermissionListResponse {
  List<PermissionListModel?>? permissionList;

  PermissionListResponse(this.permissionList);

  factory PermissionListResponse.fromJson(Map<String, dynamic> json) => _$PermissionListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PermissionListResponseToJson(this);
}

@JsonSerializable()
class PermissionListModel {
  int? roleId;
  int? tenantId;
  String? type;
  String? functionId;
  String? pageComponent;
  String? pageId;
  int? resourceId;
  String? resource;
  int? createdById;
  String? createdDate;
  int? modifiedById;
  String? modifiedDate;

  PermissionListModel(
    this.roleId,
    this.tenantId,
    this.type,
    this.functionId,
    this.pageComponent,
    this.pageId,
    this.resourceId,
    this.resource,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
  );

  factory PermissionListModel.fromJson(Map<String, dynamic> json) => _$PermissionListModelFromJson(json);

  Map<String, dynamic> toJson() => _$PermissionListModelToJson(this);
}
