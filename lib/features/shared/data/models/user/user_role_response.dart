import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_role_response.g.dart';

@JsonSerializable()
class UserRoleResponse extends BaseResponse {
  List<UserRoleResponseData?>? data;

  UserRoleResponse(msg, code, this.data) : super(code: code, msg: msg);

  factory UserRoleResponse.fromJson(Map<String, dynamic> json) => _$UserRoleResponseFromJson(json);

  Map<String, dynamic> toJson() => _$UserRoleResponseToJson(this);
}

@JsonSerializable()
class UserRoleResponseData {
  String? userName;
  int? userId;
  int? roleId;
  String? tenantId;
  String? mainFlg;
  String? createdById;
  String? createdDate;
  String? modifiedById;
  String? modifiedDate;
  String? roleName;
  String? mfaFlg;

  UserRoleResponseData(
    this.userName,
    this.userId,
    this.roleId,
    this.tenantId,
    this.mainFlg,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.roleName,
    this.mfaFlg,
  );

  factory UserRoleResponseData.fromJson(Map<String, dynamic> json) => _$UserRoleResponseDataFromJson(json);

  Map<String, dynamic> toJson() => _$UserRoleResponseDataToJson(this);
}
