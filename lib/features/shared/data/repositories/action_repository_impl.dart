import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_asset_action_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_get_asset_list_by_asset_id_reponse.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_get_selected_asset_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/action_repository.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';

class ActionRepositoryImpl extends ActionRepository with RepositoryErrorHandler {
  DioUtil dioUtil;

  ActionRepositoryImpl({required this.dioUtil});

  @override
  Future<SharedAssetActionResponse> getActionListBySearchKey(ActionListQuery query) async {
    return executeRepositoryTask<SharedAssetActionResponse>(() async {
      final resp = await dioUtil.get(GlobalVariable.actionSettingSearch, queryParams: query.toMap());

      if (resp.isSuccess()) {
        final dataResp = SharedAssetActionResponse.fromJson(resp.data);
        if (dataResp.isSuccess()) {
          return dataResp;
        }
      }
      throw BusinessException('Failed to get action list due to API response status.');
    }, 'Failed to get action list by search key');
  }

  @override
  Future<List<SharedGetSelectedAsset>> getSelectedAssetList(int processId) async {
    return executeRepositoryTask<List<SharedGetSelectedAsset>>(() async {
      final resp = await dioUtil.get(
        GlobalVariable.actionGetAssetListByProcessId,
        queryParams: {'processId': processId},
      );

      if (resp.isSuccess()) {
        final t = SharedGetSelectedAssetListResponse.fromJson(resp.data);
        if (t.isSuccess()) {
          return t.assets?.whereType<SharedGetSelectedAsset>().toList() ?? [];
        }
      }
      throw BusinessException('Failed to get selected asset list due to API response status.');
    }, 'Failed to get selected asset list');
  }

  @override
  Future<List<SharedActionAsset>> getSelectedAssetById(int actionId, List<int> assetIds) async {
    return executeRepositoryTask<List<SharedActionAsset>>(() async {
      final resp = await dioUtil.post(
        GlobalVariable.actionGetAssetListByAssetId + '?assetActionId=$actionId',
        data: assetIds,
        useFormUrlEncoded: false,
      );

      if (resp.isSuccess()) {
        final t = SharedGetAssetListByAssetIdResponse.fromJson(resp.data);
        if (t.isSuccess()) {
          return t.assets?.whereType<SharedActionAsset>().toList() ?? [];
        }
      }
      throw BusinessException('Failed to get selected asset by ID due to API response status.');
    }, 'Failed to get selected asset by ID');
  }

  @override
  Future<SharedInsertActionResponse> setInsertAssetActionData({required InsertActionDataQuery query}) async {
    return executeRepositoryTask<SharedInsertActionResponse>(() async {
      final resp = await dioUtil.post(
        GlobalVariable.setInsertAssetActionList,
        data: query.toMap(),
        useFormUrlEncoded: true,
      );

      if (resp.isSuccess()) {
        final t = SharedInsertActionResponse.fromJson(resp.data);
        if (t.isSuccess()) {
          return t;
        }
      }
      throw BusinessException('Failed to setInsertAssetActionData due to API response status.');
    }, 'Failed to setInsertAssetActionData');
  }
}
