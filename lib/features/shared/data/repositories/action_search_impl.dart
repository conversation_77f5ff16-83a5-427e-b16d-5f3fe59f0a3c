import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/action_search_repository.dart';

class ActionSearchRepositoryImpl with RepositoryErrorHandler implements ActionSearchRepository {
  DioUtil dioUtil;

  ActionSearchRepositoryImpl({required this.dioUtil});

  @override
  Future<Map<int, List<int>>> findAssetsByKeyword(FindAssetsByKeywordQuery query) async {
    return executeRepositoryTask<Map<int, List<int>>>(() async {
      final resp = await dioUtil.post(
        GlobalVariable.actionFindAssetsByKeyword,
        data: query.toJson(),
        useFormUrlEncoded: false,
      );
      final bsResp = BaseResponse.fromJson(resp.data);
      if (bsResp.isSuccess()) {
        final mapData = resp.data as Map<String, dynamic>;
        return {
          mapData['totalCount'] as int: (mapData['assets'] as List<dynamic>).map((e) => (e as num).toInt()).toList(),
        };
      }

      // 获取失败的时候， 直接跑出异常。
      throw BusinessException('Failed to fetch search data.');
    }, 'Failed to fetch search data.');
  }
}
