import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/check_asset_reservation_status_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/appointment_repository.dart';

class AppointmentRepositoryImpl with RepositoryErrorHandler implements AppointmentRepository {
  DioUtil dioUtil;

  AppointmentRepositoryImpl({required this.dioUtil});

  @override
  Future<AppointmentListResponse> getAppointmentList(int assetId, int assetTypeId) async {
    return executeRepositoryTask<AppointmentListResponse>(() async {
      final response = await dioUtil.get(
        GlobalVariable.getAppointmentListByAssetId,
        queryParams: {'assetId': assetId, 'assetTypeId': assetTypeId},
      );
      if (response.isSuccess()) {
        return AppointmentListResponse.fromJson(response.data);
      } else {
        throw BusinessException(
          'Failed to load appointment list, server responded with status: ${response.statusCode}',
        );
      }
    }, 'Error fetching appointment list');
  }

  @override
  Future<CheckAssetReservationStatusResponse> checkAppointData({
    required int assetId,
    required String reservationNo,
    required int eventTypeId,
    required String start,
    required String end,
  }) async {
    return executeRepositoryTask(() async {
      final params = {
        'assetId': assetId,
        'reservationNo': reservationNo,
        'eventTypeId': eventTypeId,
        'startDate': start,
        'endDate': end,
      };
      final response = await dioUtil.post(
        GlobalVariable.checkAssetReservationStatus,
        data: params,
        useFormUrlEncoded: true,
      );
      return CheckAssetReservationStatusResponse.fromJson(response.data);
    }, 'AppointData check failed');
  }

  @override
  Future<BaseResponse> saveScheduleData({
    required dynamic alertData,
    required String alertSetting,
    required int assetId,
    required String start,
    required String end,
    required int assetTypeId,
    required int eventTypeId,
    required String extraCommonText,
    required String reservationName,
    required int reservationNo,
    required String reservationText,
    required String unitDay,
    required String modifiedDate,
  }) async {
    return executeRepositoryTask(() async {
      final response = await dioUtil.post(
        GlobalVariable.updateAppointment,
        data: {
          'alertData': alertData,
          'alertSetting': alertSetting,
          'assetId': assetId,
          'start': start,
          'end': end,
          'assetTypeId': assetTypeId,
          'eventTypeId': eventTypeId,
          'extraCommonText': extraCommonText,
          'reservationName': reservationName,
          'reservationNo': reservationNo,
          'reservationText': reservationText,
          'unitDay': unitDay,
          'modifiedDate': modifiedDate,
        },
      );
      return BaseResponse.fromJson(response.data);
    }, 'Error saving schedule data');
  }
}
