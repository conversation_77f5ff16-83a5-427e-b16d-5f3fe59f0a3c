import 'dart:convert';

import 'package:asset_force_mobile_v2/core/utils/datetime_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/number_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_item_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/asset_functional_processing_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:collection/collection.dart';

/// 资产共通处理类
class AssetFunctionalProcessingHelperImpl implements AssetFunctionalProcessingHelperRepository {
  final S3Repository s3repository;

  AssetFunctionalProcessingHelperImpl({required this.s3repository});

  /// 专为转换checkbox的值取得
  @override
  String? assembledCheckboxTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value}) {
    if (SharedItemTypeEnum.checkbox.notEquals(layoutSetting.itemType)) {
      return null;
    }
    final String? checkboxOption = layoutSetting.option;
    if (checkboxOption == null || checkboxOption.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> checkboxOption 值为空');
      return null;
    }
    final Map<String, dynamic>? checkboxOptionObj = jsonDecode(checkboxOption);
    if (checkboxOptionObj == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> checkboxOptionObj 值为空');
      return null;
    }
    // チェックボックス（複数）：1 チェックボックス（単一）：0
    final bool isCheckboxMultiFlg = checkboxOptionObj['checkboxMultiFlg'] == '1';
    String? checkboxValue;
    if (isCheckboxMultiFlg) {
      // チェックボックス（複数）
      if (value is List<dynamic>) {
        if (value.isEmpty) {
          checkboxValue = '';
        } else {
          checkboxValue = value.join(',');
        }
      }
      // 为了兼容后台的旧数据逻辑
      if (value is String && value.isNotEmpty) {
        final dynamic displayObj = jsonDecode(value);
        if (displayObj is List<dynamic>) {
          checkboxValue = displayObj.join(',');
        }
      }
    } else {
      // チェックボックス（単一）
      if (value is String && value.isNotEmpty) {
        checkboxValue = value == '0' ? 'なし' : 'あり';
      } else {
        checkboxValue = value;
      }
    }
    return checkboxValue;
  }

  /// 专为转换number的值取得
  @override
  String? assembledNumberTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value}) {
    if (SharedItemTypeEnum.number.notEquals(layoutSetting.itemType)) {
      return null;
    }
    dynamic value0 = value;
    if (value0 is! String) {
      value0 = itemValueToString(value: value);
    }
    if (value0.isEmpty) {
      return null;
    }
    final String? numberOption = layoutSetting.option;
    if (numberOption == null || numberOption.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> numberOption 值为空');
      return null;
    }
    final Map<String, dynamic>? numberOptionObj = jsonDecode(numberOption);
    if (numberOptionObj == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> numberOptionObj 值为空');
      return null;
    }
    final String numberCommaDecimalPoint = itemValueToString(value: numberOptionObj['numberCommaDecimalPoint']); // 逗号位置
    final String numberDecimalPoint = itemValueToString(value: numberOptionObj['numberDecimalPoint']); // 小数点的位数
    final String numberValue = NumberUtils.numberFormat(
      number: value0,
      comma: numberCommaDecimalPoint,
      decimal: numberDecimalPoint,
    );
    return numberValue;
  }

  /// 专为转换dateTime的值取得
  @override
  String? assembledDateTimeTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value}) {
    if (SharedItemTypeEnum.date.notEquals(layoutSetting.itemType)) {
      return null;
    }
    dynamic value0 = value;
    if (value0 is! String) {
      value0 = itemValueToString(value: value);
    }
    if (value0.isEmpty) {
      return null;
    }
    final String? dateTimeOption = layoutSetting.option;
    if (dateTimeOption == null || dateTimeOption.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> dateTimeOption 值为空');
      return null;
    }
    final Map<String, dynamic>? dateTimeOptionObj = jsonDecode(dateTimeOption);
    if (dateTimeOptionObj == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> dateTimeOptionObj 值为空');
      return null;
    }
    final String dateType = dateTimeOptionObj['dateType'];
    String? dateTimeValue;
    if (dateType == 'dateTime') {
      dateTimeValue = DateTimeUtils.getDateTimeFromDateType(value0);
    } else {
      dateTimeValue = DateTimeUtils.getDateFromDateType(value0);
    }
    return dateTimeValue;
  }

  /// 专为转换currency的值取得
  @override
  String? assembledCurrencyTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value}) {
    if (SharedItemTypeEnum.currency.notEquals(layoutSetting.itemType)) {
      return null;
    }
    dynamic value0 = value;
    if (value0 is! String) {
      value0 = itemValueToString(value: value);
    }
    if (value0.isEmpty) {
      return null;
    }
    final String? numberOption = layoutSetting.option;
    if (numberOption == null || numberOption.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> currency numberOption 值为空');
      return null;
    }
    final Map<String, dynamic>? numberOptionObj = jsonDecode(numberOption);
    if (numberOptionObj == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> currency numberOptionObj 值为空');
      return null;
    }
    final String numberCommaDecimalPoint = '3'; // 逗号位置
    final String numberDecimalPoint = itemValueToString(value: numberOptionObj['currencyDecimalPoint']); // 小数点的位数
    // 下面注释代码可以取道钱的标识符，但式样要求不需要添加，恐后世无传，故撰斯记
    // final String _currencyType = numberOptionObj['currencyType']; // 通货的钱标识符号

    final String numberValue = NumberUtils.numberFormat(
      number: value0,
      comma: numberCommaDecimalPoint,
      decimal: numberDecimalPoint.toString(),
    );
    return numberValue;
  }

  /// 专为转换appurInfoSummary的值取得
  @override
  String? assembledAppurInfoSummaryTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value}) {
    if (SharedItemTypeEnum.appurInfoSummary.notEquals(layoutSetting.itemType)) {
      return null;
    }
    dynamic value0 = value;
    if (value0 is! String) {
      value0 = itemValueToString(value: value);
    }
    if (value0.isEmpty) {
      return null;
    }
    final String numberCommaDecimalPoint = '3'; // 逗号位置
    final String numberDecimalPoint = '0'; // 小数点的位数
    final String numberValue = NumberUtils.numberFormat(
      number: value0,
      comma: numberCommaDecimalPoint,
      decimal: numberDecimalPoint,
    );
    return numberValue;
  }

  /// 专为转换calculate的值取得
  @override
  String? assembledCalculateTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value}) {
    if (SharedItemTypeEnum.calculate.notEquals(layoutSetting.itemType)) {
      return null;
    }
    dynamic value0 = value;
    if (value0 is! String) {
      value0 = itemValueToString(value: value);
    }
    if (value0.isEmpty) {
      return null;
    }
    final String? calculateOption = layoutSetting.option;
    if (calculateOption == null || calculateOption.isEmpty) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> calculateOption 值为空');
      return null;
    }
    final Map<String, dynamic>? calculateOptionObj = jsonDecode(calculateOption);
    if (calculateOptionObj == null) {
      // 能进入当前错误基本上是数据库出现错误
      LogUtil.e('数据库值返回错误 ==> calculateOptionObj 值为空');
      return null;
    }
    final String calculateCommaDecimalPoint = itemValueToString(
      value: calculateOptionObj['calculateCommaDecimalPoint'],
    ); // 逗号位置
    final String calculateDecimalPoint = itemValueToString(
      value: calculateOptionObj['calculateDecimalPoint'],
    ); // 小数点的位数
    final String calculateValue = NumberUtils.numberFormat(
      number: value0,
      comma: calculateCommaDecimalPoint,
      decimal: calculateDecimalPoint,
    );
    return calculateValue;
  }

  /// 通用版获取home画像
  @override
  Future<String?> getHomeImageFun({
    required List<SharedLayoutSetting> assetItemTypeSettingList,
    required Asset assetItem,
  }) async {
    String? assetImageUrl;
    for (int i = 0; i < assetItemTypeSettingList.length; i++) {
      final assetItemTypeSetting = assetItemTypeSettingList[i];
      if (assetItemTypeSetting.itemType != 'image') {
        continue;
      }
      final imageItemName = assetItemTypeSetting.itemName;
      if (imageItemName == null || imageItemName.isEmpty) {
        continue;
      }
      final assetTextObj = assetItem.assetTextObj;
      if (assetTextObj == null) {
        continue;
      }
      final dynamic imageObj = assetTextObj[imageItemName];
      if (imageObj == null) {
        continue;
      }
      if (imageObj is Map<String, dynamic>) {
        final bool? isHomeImage = imageObj['isHomeImage'] ?? false;
        if (isHomeImage == false) {
          continue;
        }
        assetImageUrl = imageObj['url'];
        break;
      }
      if (imageObj is List<Map<String, dynamic>>) {
        final Map<String, dynamic>? homeImageObj = imageObj.firstWhereOrNull((im) => im['isHomeImage'] == true);
        if (homeImageObj == null || homeImageObj.isEmpty) {
          continue;
        }
        assetImageUrl = homeImageObj['url'];
        break;
      }
      if (imageObj is List) {
        for (var im in imageObj) {
          if (im is Map<String, dynamic>) {
            if (im['isHomeImage'] == true) {
              assetImageUrl = im['url'];
              break;
            }
          } else {
            LogUtil.w('Unexpected item type in imageObj: ${im.runtimeType}');
          }
        }
      } else {
        LogUtil.w('imageObj is not a List: ${imageObj.runtimeType}');
      }

      // TODO 权限问题需优化
      // var optionObj = jsonDecode(assetItemTypeSetting['option']);
      // List<dynamic>? sectionPrivateGroups =
      // optionObj['sectionPrivateGroups'];
      //
      // if (sectionPrivateGroups != null) {
      //   isView = await visiblePermissionsCheck(sectionPrivateGroups);
      // }
      //
      // // 如果没有权限预览或 mobileFlg == '0'，则跳过
      // if (!isView || assetItemTypeSetting['mobileFlg'] == '0') {
      //   continue;
      // }
    }
    // S3のpresigned URLを取得する
    if (assetImageUrl != null) {
      assetImageUrl = await s3repository.getTurl(assetImageUrl);
    }
    // LogUtil.d('S3 presigned URL: $assetImageUrl');
    return assetImageUrl;
  }

  /// 任意类型itemValue转String
  @override
  String itemValueToString({required dynamic value}) {
    try {
      if (value == null) {
        return ''; // null 转为字符串 'null'
      }

      if (value is String) {
        return value; // 如果已经是字符串，直接返回
      }

      if (value is num || value is bool) {
        return value.toString(); // 数字或布尔值直接调用 toString()
      }

      if (value is List || value is Set) {
        return value.map((e) => itemValueToString(value: e)).join(', '); // 将每个元素转换为字符串后用逗号连接
      }

      if (value is Map) {
        return value.entries
            .map((e) => '${itemValueToString(value: e.key)}: ${itemValueToString(value: e.value)}')
            .join(', '); // 将键值对转换为字符串并连接
      }

      // 其他对象直接使用 toString() 方法
      return value.toString();
    } catch (e, stackTrace) {
      LogUtil.e('itemValueToString error ===> $e', stackTrace: stackTrace);
      return '';
    }
  }
}
