import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_s3_upload_url_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';

class S3RepositoryImpl with RepositoryErrorHandler implements S3Repository {
  final DioUtil dioUtil;

  S3RepositoryImpl({required this.dioUtil});

  @override
  Future<String> getTurl(String filePath) async {
    return executeRepositoryTask<String>(() async {
      // 构建请求URL
      final response = await dioUtil.get(GlobalVariable.getTurlApi, queryParams: {'filePath': filePath});

      // 从响应中提取S3 URL
      final data = response.data as Map<String, dynamic>?;
      final getUrl = data?['data']?['getUrl'] as String?;

      if (getUrl == null || getUrl.isEmpty) {
        throw BusinessException('getUrl is null or empty in the S3 response');
      }

      return getUrl;
    }, 'Failed to get S3 URL');
  }

  @override
  Future<SharedS3UploadUrlResponse> getS3UploadUrl(String fileName) async {
    try {
      final response = await dioUtil.get('${GlobalVariable.s3UploadUrl}$fileName');
      return SharedS3UploadUrlResponse.fromJson(response.data);
    } catch (error) {
      throw Exception('Failed to get S3 upload URL: $error');
    }
  }

  @override
  Future<bool> deleteFileFromS3(List<String> fileToDelete) {
    return executeRepositoryTask<bool>(() async {
      final response = await dioUtil.delete(GlobalVariable.deleteFileFromS3, data: fileToDelete);
      return response.isSuccess();
    }, 'Failed to delete file from S3');
  }
}
