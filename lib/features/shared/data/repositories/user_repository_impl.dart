import 'dart:convert';

import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/core/extensions/get_response_extension.dart';
import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_schedule/data/model/un_permission_response_entity.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/get_user_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_user_tenant_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/tenant_info_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/permission_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_my_account_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/user_role_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';

class UserRepositoryImpl with RepositoryErrorHandler implements UserRepository {
  final DioUtil dioUtil;

  UserRepositoryImpl({required this.dioUtil});

  @override
  Future<SharedMyAccountModel> getUserInfo() async {
    return executeRepositoryTask<SharedMyAccountModel>(() async {
      final response = await dioUtil.get(GlobalVariable.getUserInfoApi);
      if (response.isSuccess()) {
        final p = SharedMyAccountModel.fromJson(response.data);
        return p;
      } else {
        LogUtil.e('getUserInfo Failed: ${response.statusCode}');
        throw BusinessException('Failed to get user info due to API response status: ${response.statusCode}');
      }
    }, 'Failed to get user info');
  }

  @override
  Future<SharedUserTenantModel> getUserTenant() async {
    return executeRepositoryTask<SharedUserTenantModel>(() async {
      final response = await dioUtil.get(GlobalVariable.getUserTenant);
      if (response.isSuccess()) {
        return SharedUserTenantModel.fromJson(response.data);
      } else {
        LogUtil.e('getTenantInfo Failed: ${response.statusCode}');
        throw BusinessException('Failed to get tenant info due to API response status: ${response.statusCode}');
      }
    }, 'Failed to get tenant info');
  }

  @override
  Future<List<UserRoleResponseData>> getUserRole() async {
    return executeRepositoryTask<List<UserRoleResponseData>>(() async {
      final response = await dioUtil.get(GlobalVariable.userRole);
      if (response.isSuccess()) {
        final userRoleResponse = UserRoleResponse.fromJson(response.data);
        final nonNullData =
            userRoleResponse.data?.whereType<UserRoleResponseData>().toList() ?? <UserRoleResponseData>[];

        if (userRoleResponse.isSuccess()) {
          await StorageUtils.set(StorageUtils.keyUserRoleList, jsonEncode(nonNullData));
        }
        return nonNullData;
      } else {
        LogUtil.e('getUserRole Failed: ${response.statusCode}');
        throw BusinessException('Failed to get user role due to API response status: ${response.statusCode}');
      }
    }, 'Failed to get user role');
  }

  @override
  Future<bool> updateUserInfo(dynamic userInfo) async {
    return executeRepositoryTask<bool>(() async {
      final resp = await dioUtil.post(GlobalVariable.updateRole, data: userInfo);
      if (resp.isSuccess()) {
        final p = BaseResponse.fromJson(resp.data);
        return p.isSuccess();
      }
      return false;
    }, 'Failed to update user info');
  }

  @override
  Future<bool> getAuthorityInfo(int functionId) async {
    return executeRepositoryTask<bool>(() async {
      final response = await dioUtil.get(GlobalVariable.getAuthority, queryParams: {'functionId': functionId});
      if (response.isSuccess()) {
        final permissionListResponse = PermissionListResponse.fromJson(response.data);
        final permissionList = permissionListResponse.permissionList;
        return permissionList?.any((item) => item?.functionId == functionId.toString()) ?? false;
      } else {
        LogUtil.e('getAuthorityInfo Failed: ${response.statusCode}');
        throw BusinessException('Failed to get authority info due to API response status: ${response.statusCode}');
      }
    }, 'Failed to get authority info');
  }

  @override
  Future<List<SharedUserModel>> getUserList() async {
    return executeRepositoryTask<List<SharedUserModel>>(() async {
      final response = await dioUtil.get(GlobalVariable.getUserList);
      if (response.isSuccess()) {
        final userListResponse = GetUserListResponse.fromJson(response.data);
        return userListResponse.userList;
      }
      return <SharedUserModel>[];
    }, 'Failed to load user list');
  }

  @override
  Future<UnPermissionResponseEntity?> getUnPermission() async {
    return executeRepositoryTask<UnPermissionResponseEntity?>(() async {
      final response = await dioUtil.get(GlobalVariable.unPermission);
      if (response.isSuccess()) {
        return UnPermissionResponseEntity.fromJson(response.data);
      }
      return null;
    }, 'Failed to get un permission');
  }

  @override
  Future<TenantInfoResponse?> getTenantInfo() {
    return executeRepositoryTask<TenantInfoResponse?>(() async {
      final response = await dioUtil.get(GlobalVariable.getTenantInfo);
      if (response.isSuccess()) {
        return TenantInfoResponse.fromJson(response.data);
      }
      return null;
    }, 'Failed to get tenant info');
  }

  @override
  Future<List<SharedRoleModel>> getGroupList(String processDefinitionId, String taskDefKey) async {
    return executeRepositoryTask<List<SharedRoleModel>>(() async {
      final response = await dioUtil.get(
        GlobalVariable.workFlowGetAssignDynamicGroupList,
        queryParams: {'processDefinitionId': processDefinitionId, 'taskDefKey': taskDefKey},
      );
      final data = response.data;
      final List<dynamic> groupList = data['groupList'] ?? [];
      final List<SharedRoleModel> sharedRoleResult = groupList.map((item) => SharedRoleModel.fromJson(item)).toList();
      return sharedRoleResult;
    }, 'Failed to load group list');
  }
}
