import 'package:collection/collection.dart';

enum ScanType { barCode, RFID, ocrSerialNo }

extension ScanTypeExtension on ScanType {
  static ScanType? fromString(String value) {
    switch (value.toLowerCase()) {
      case 'barCode':
        return ScanType.barCode;
      case 'RFID':
        return ScanType.RFID;
      case 'ocrSerialNo':
        return ScanType.ocrSerialNo;
      default:
        return ScanType.barCode;
    }
  }
}

enum SharedAIOcrTypeEnum {
  /// 不知道干嘛用， 但是得存在。
  /// 効果は知りません
  SMFL_RENTAL('SMFL_RENTAL', '効果は知りません', 'smflRental'),

  /// バーコード / QRコード / RFID
  BARCODE_QR_RFID('BARCODE_QR_RFID', 'バーコード / QRコード / RFID', 'barCodeQrRfid'),

  /// AI OCR（ナンバープレート）
  TRAFFIC_RENTAL('TRAFFIC_RENTAL', 'AI OCR（ナンバープレート）', 'trafficRental'),

  /// AI OCR（AI用識別コード）
  WELCIA('WELCIA', 'AI OCR（AI用識別コード）', 'welcia', platformSupport: 1),

  /// 如果是取消，会返回这个值。
  CANCEL('CANCEL', 'キャンセル', 'cancel');

  /// flutter 侧使用的数据。
  final String value;

  /// 界面显示的文案
  final String text;

  /// storage 层存储的字段名称
  final String dataKey;

  /// 支持的平台，  IOS  1, ANDROID 2, WEB 4 。。。 默认 255, 全支持。
  /// 如需要支持 IOS + ANDROID 则传入： 255 或者 3 （1 + 2）
  final int platformSupport;

  const SharedAIOcrTypeEnum(this.value, this.text, this.dataKey, {this.platformSupport = 255});

  bool isSupportIOS() {
    return _isSupport(0);
  }

  bool isSupportAndroid() {
    return _isSupport(1);
  }

  bool _isSupport(int mask) {
    return (255 & (1 << mask)) != 0;
  }

  bool equals(SharedAIOcrTypeEnum? otherValue) => value == otherValue?.text;

  bool equalsStr(String? otherValue) => value == otherValue;

  bool notEqualsStr(String? otherValue) => value != otherValue;

  static SharedAIOcrTypeEnum? fromString(String? value) {
    return values.firstWhereOrNull((e) => e.value == value);
  }
}
