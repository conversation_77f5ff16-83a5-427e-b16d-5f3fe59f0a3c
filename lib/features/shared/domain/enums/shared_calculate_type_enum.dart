import 'package:collection/collection.dart';

enum SharedCalculateValueItemEnum {
  time('time'),
  digital('digital'),
  currency('currency'),
  boolValue('bool'),
  text('text');

  final String value;

  const SharedCalculateValueItemEnum(this.value);

  /// 检查字符串值是否存在于枚举中
  ///
  /// 返回 `true` 表示字符串值对应的枚举存在，`false` 表示不存在。
  static bool contains({
    required List<SharedCalculateValueItemEnum> calculateValueItemEnumList,
    required String? value,
  }) {
    return calculateValueItemEnumList.firstWhereOrNull((t) => t.value == value) != null;
  }

  /// 实例方法：比较枚举值与给定字符串是否相等
  ///
  /// 示例：
  /// ```dart
  /// ```
  bool equals(String? otherValue) {
    return value == otherValue;
  }

  /// 实例方法：比较枚举值与给定字符串是否不相等
  ///
  /// 示例：
  /// ```dart
  /// ```
  bool notEquals(String? otherValue) {
    return !equals(otherValue);
  }

  static List<SharedCalculateValueItemEnum> allValues() => values;
  static List<String> allNames() => values.map((e) => e.name).toList();
}
