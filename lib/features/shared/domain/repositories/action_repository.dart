import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_asset_action_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_get_asset_list_by_asset_id_reponse.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/action/shared_get_selected_asset_list_response.dart';

abstract class ActionRepository {
  ///
  /// secure/AssetActionSetting/mobile/search
  ///
  Future<SharedAssetActionResponse> getActionListBySearchKey(ActionListQuery query);

  Future<List<SharedGetSelectedAsset>> getSelectedAssetList(int processId);

  /// 处理设定 扫描结束后  需要通过这个接口去获取到完整的数据。
  /// /secure/AssetActionList/mobile/getAssetListByAssetId
  Future<List<SharedActionAsset>> getSelectedAssetById(int actionId, List<int> assetIds);

  /// 処理データを保存
  Future<SharedInsertActionResponse> setInsertAssetActionData({required InsertActionDataQuery query});
}

class ActionListQuery {
  int skip;
  int row;
  String? searchKey;

  /// null, 1, 2 的取值范围
  String? actionListType;
  String actionLabel;

  ActionListQuery({
    required this.skip,
    required this.row,
    required this.searchKey,
    this.actionListType,
    this.actionLabel = '',
  });

  Map<String, dynamic> toMap() {
    final obj = {'skip': skip, 'rows': row, 'searchText': searchKey};

    if (actionListType == '2') {
      obj['actionLabel'] = actionLabel; // Uri.encodeComponent(actionLabel); 不需要转码了，  dio会自动转码
    }
    return obj;
  }

  @override
  String toString() {
    return 'ActionListQuery{skip: $skip, row: $row, searchKey: $searchKey, actionListType: $actionListType, actionLabel: $actionLabel}';
  }
}

class InsertActionDataQuery {
  String assetActionItem;
  int assetTypeId;
  int assetActionId;
  String assetList;
  String processType;
  String customerActionName;
  String onlineMode;
  String isUpdateAmount;
  String? amountType;

  InsertActionDataQuery({
    required this.assetActionItem,
    required this.assetTypeId,
    required this.assetActionId,
    required this.assetList,
    required this.processType,
    required this.customerActionName,
    required this.onlineMode,
    required this.isUpdateAmount,
    this.amountType,
  });

  Map<String, dynamic> toMap() {
    final obj = {
      'assetActionItem': assetActionItem,
      'assetTypeId': assetTypeId,
      'assetActionId': assetActionId,
      'assetList': assetList,
      'processType': processType,
      'customerActionName': customerActionName,
      'onlineMode': onlineMode,
    };

    if (isUpdateAmount == '1') {
      obj['isUpdateAmount'] = isUpdateAmount;
      obj['amountType'] = amountType ?? '';
    }
    return obj;
  }
}
