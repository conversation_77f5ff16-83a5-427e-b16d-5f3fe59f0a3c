import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:json_annotation/json_annotation.dart';
part 'action_search_repository.g.dart';

abstract class ActionSearchRepository {
  /// 搜索过滤使用的
  Future<Map<int, List<int>>> findAssetsByKeyword(FindAssetsByKeywordQuery query);
}

@JsonSerializable(explicitToJson: true)
class FindAssetsByKeywordResponse extends BaseResponse {
  @JsonKey(name: 'totalCount')
  final int? totalCount;

  @JsonKey(name: 'assets')
  final List<int>? assets;
  FindAssetsByKeywordResponse({required super.code, required super.msg, this.totalCount, this.assets});

  factory FindAssetsByKeywordResponse.fromJson(Map<String, dynamic> json) =>
      _$FindAssetsByKeywordResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FindAssetsByKeywordResponseToJson(this);
}

class FindAssetsByKeywordQuery {
  int assetTypeId;
  String keyword;
  int skip;
  int rows;
  List<int> assetIds;

  FindAssetsByKeywordQuery({
    required this.assetTypeId,
    required this.keyword,
    required this.skip,
    required this.assetIds,
    this.rows = 200,
  });

  @override
  String toString() {
    return 'FindAssetsByKeywordQuery{' +
        'assetTypeId: $assetTypeId, ' +
        'keyword: $keyword, ' +
        'skip: $skip, ' +
        'rows: $rows, ' +
        'assetIds: $assetIds}';
  }

  Map<String, dynamic> toJson() {
    return {'assetTypeId': assetTypeId, 'keyword': keyword, 'skip': skip, 'rows': rows, 'assetIds': assetIds};
  }
}
