import 'package:asset_force_mobile_v2/core/network/base_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/appointment_list_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/appointment/check_asset_reservation_status_response.dart';

abstract class AppointmentRepository {
  /// 获取スケジュールInfo
  /// [assetId] 资产ID
  ///
  /// - AppointmentListResponse
  Future<AppointmentListResponse> getAppointmentList(int assetId, int assetTypeId);

  /// 检查预约数据
  Future<CheckAssetReservationStatusResponse> checkAppointData({
    required int assetId,
    required String reservationNo,
    required int eventTypeId,
    required String start,
    required String end,
  });

  /// 保存Schedule数据
  Future<BaseResponse> saveScheduleData({
    required dynamic alertData,
    required String alertSetting,
    required int assetId,
    required String start,
    required String end,
    required int assetTypeId,
    required int eventTypeId,
    required String extraCommonText,
    required String reservationName,
    required int reservationNo,
    required String reservationText,
    required String unitDay,
    required String modifiedDate,
  });
}
