import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_layout_setting.dart';

/// S3Repository接口定义了用于获取S3 URL的方法。
abstract class AssetFunctionalProcessingHelperRepository {
  /// 专为转换checkbox的值取得
  String? assembledCheckboxTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value});

  /// 专为转换number的值取得
  String? assembledNumberTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value});

  /// 专为转换dateTime的值取得
  String? assembledDateTimeTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value});

  /// 专为转换currency的值取得
  String? assembledCurrencyTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value});

  /// 专为转换appurInfoSummary的值取得
  String? assembledAppurInfoSummaryTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value});

  /// 专为转换calculate的值取得
  String? assembledCalculateTypeValue({required SharedLayoutSetting layoutSetting, required dynamic value});

  /// 通用版获取home画像
  Future<String?> getHomeImageFun({
    required List<SharedLayoutSetting> assetItemTypeSettingList,
    required Asset assetItem,
  });

  /// 任意类型itemValue转String
  String itemValueToString({required dynamic value});
}
