import 'package:asset_force_mobile_v2/features/asset/asset_schedule/data/model/un_permission_response_entity.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/shared_user_tenant_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/tenant/tenant_info_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_my_account_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_role_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/shared_user_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/user_role_response.dart';

abstract class UserRepository {
  ///
  /// Load user information.
  ///
  Future<SharedMyAccountModel> getUserInfo();

  ///
  /// load tenant information
  ///
  Future<SharedUserTenantModel> getUserTenant();

  /// 获取当前用户指定功能的访问权限
  /// functionId 需要判断权限的功能ID
  Future<bool> getAuthorityInfo(int functionId);

  // 获取用户权限组
  Future<List<UserRoleResponseData>> getUserRole();

  Future<bool> updateUserInfo(dynamic userInfo);

  /// 获取担当者列表
  Future<List<SharedUserModel>> getUserList();

  /// 获取Schedule 中的权限
  Future<UnPermissionResponseEntity?> getUnPermission();

  Future<TenantInfoResponse?> getTenantInfo();

  Future<List<SharedRoleModel>> getGroupList(String processDefinitionId, String taskDefKey);
}
