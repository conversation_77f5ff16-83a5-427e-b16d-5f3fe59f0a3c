import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/action_repository.dart';

class ActionTemporaryUsecase extends UseCase<void, NoParams> {
  ActionRepository actionRepository;

  ActionTemporaryUsecase({required this.actionRepository});

  @override
  Future<void> call(NoParams params) async {
    throw UnimplementedError();
  }

  /// 一时保存数据。
  Future<List<dynamic>> buildContinueScanData(
    int processId,
    List<dynamic>? registeredAssetList, {
    bool reScan = false,
  }) async {
    final netAssetList = await actionRepository.getSelectedAssetList(processId);

    var npRegisteredAssetList = <dynamic>[];

    if (reScan && registeredAssetList != null) {
      final registeredAssetIds = Set.from(registeredAssetList.map((asset) => asset.assetId.toString()));
      for (var asset in netAssetList) {
        if (!registeredAssetIds.contains(asset.assetId.toString())) {
          npRegisteredAssetList.add(asset);
          registeredAssetIds.add(asset.assetId.toString());
        }
      }
    } else {
      npRegisteredAssetList = netAssetList;
    }

    if (npRegisteredAssetList.isEmpty) {
      for (var registeredAsset in npRegisteredAssetList) {
        if (registeredAsset['count'] == null) {
          registeredAsset['count'] = 1;
        }
      }
    }

    return npRegisteredAssetList;
  }

  // Future<List<SharedActionAssetUIModel>> _getSelectedAssetById(int actionId, List<SharedArInfo> arInfos) async {
  //   final assetIds = arInfos
  //       .map((e) {
  //         final assetId = e.assetId;
  //         if (assetId != null) {
  //           return int.tryParse(assetId);
  //         }
  //         return null;
  //       })
  //       .whereType<int>()
  //       .toList();
  //
  //   LogUtil.d('message getSelectedAssetById actionId: $actionId,  assetIds: $assetIds');
  //   final rData = await actionRepository.getSelectedAssetById(actionId, assetIds);
  //
  //   final List<SharedActionAssetUIModel> ret = [];
  //   rData.forEach((p) {
  //     final uiModel = SharedActionAssetUIModel(
  //       assetId: p.assetId ?? 0,
  //       assetTypeId: p.assetTypeId ?? 0,
  //       assetTypeName: p.assetTypeName ?? '',
  //       assetItemList: p.assetItemList ?? [],
  //       assetSettingDetailList: p.assetSettingDetailList ?? [],
  //       assetName: p.assetName ?? '',
  //       quantity: p.quantity ?? 0,
  //       homeImageUrl: p.homeImageUrl ?? '',
  //       location: p.location ?? '',
  //       identityCode: p.identityCode ?? '',
  //       count: RxInt(0),
  //     );
  //     final arInfo = arInfos.where((info) {
  //       return info.assetId == p.assetId?.toString();
  //     }).firstOrNull;
  //     if (arInfo != null) {
  //       uiModel.count.value = arInfo.count ?? 0;
  //     } else {
  //       uiModel.count.value = 0;
  //     }
  //
  //     if (p.quantity == null) {
  //       if (arInfo != null && arInfo.quantity != null) {
  //         uiModel.quantity = arInfo.quantity!;
  //       } else {
  //         uiModel.quantity = 1;
  //       }
  //     }
  //     ret.add(uiModel);
  //   });
  //
  //   return ret;
  // }
}
