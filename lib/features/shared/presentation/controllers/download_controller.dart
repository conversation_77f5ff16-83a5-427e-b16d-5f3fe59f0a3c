import 'dart:io';

import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:asset_force_mobile_v2/core/network/interceptors/response_interceptor.dart' as ASReponseInterceptor;

class DownloadController extends BaseController {
  // download progress obs. [0.0 - 100.0]
  var downloadProgress = 0.0.obs;

  // 下载文件
  Future<void> downloadFile(String url, String fileName) async {
    // show download popup
    _showPopup(fileName);

    try {
      downloadProgress.value = 0.0;

      // get save file's full path
      final String saveFile = await _getSaveFile(fileName);

      // start download。
      ASReponseInterceptor.ResponseInterceptor.addSkipInterceptorPath(url);
      await _download(url, saveFile);
      ASReponseInterceptor.ResponseInterceptor.removeSkipInterceptorPath(url);

      // open file
      Future.delayed(const Duration(microseconds: 200), () {
        _openFile(saveFile);
      });

      Get.back();
    } catch (e) {
      LogUtil.e('Error during download: $e');

      Get.back();

      Future.delayed(const Duration(microseconds: 50), () {
        handleException(BusinessException('通信環境を確認して、もう一度やり直してください。'));
      });
    }
  }

  Future<String> _getSaveFile(String filename) async {
    final dir = await getApplicationDocumentsDirectory();
    return '${dir.path}/$filename';
  }

  Future<void> _download(String url, String savePath) async {
    final Dio dio = Get.find<DioUtil>().dio;
    await dio.download(
      url,
      savePath,
      options: Options(extra: {'type': 'download'}),
      onReceiveProgress: (received, total) {
        if (total != -1) {
          downloadProgress.value = (received / total) * 100;
        }
      },
    );
  }

  void _showPopup(String fileName) {
    Get.bottomSheet(
      SafeArea(
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(16),
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 第一行: 显示文件名
              Text(fileName, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
              const SizedBox(height: 20),

              // 第二行: 显示进度条和进度百分比
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(() {
                    return Expanded(
                      child: LinearProgressIndicator(
                        value: downloadProgress.value / 100,
                        backgroundColor: Colors.grey[300],
                      ),
                    );
                  }),

                  // 显示下载进度百分比
                  Obx(() {
                    return SizedBox(
                      width: 80,
                      child: Text(
                        downloadProgress.value >= 100 ? '100%' : '${downloadProgress.value.toStringAsFixed(2)}%',
                        textAlign: TextAlign.center,
                      ),
                    );
                  }),
                ],
              ),
            ],
          ),
        ),
      ),
      isScrollControlled: true,
      backgroundColor: Colors.white,
      isDismissible: false,
      barrierColor: Colors.black.withAlpha(128),
    );
  }

  Future<void> _openFile(String filePath) async {
    if (await File(filePath).exists()) {
      OpenFile.open(filePath);
    }
  }
}
