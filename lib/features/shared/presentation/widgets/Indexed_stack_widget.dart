import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:flutter/material.dart';

class IndexedStackWidget extends StatefulWidget {
  const IndexedStackWidget({
    super.key,
    this.index = 0,
    this.children = const [],
    this.alignment = AlignmentDirectional.topStart,
    this.textDirection,
    this.sizing = StackFit.loose,
    this.preloadedPages = const [],
  });

  final int index;

  final List<Widget> children;

  final AlignmentGeometry alignment;

  final TextDirection? textDirection;

  final StackFit sizing;

  /// 用来实现预加载页面
  final List<SharedNavBarEnum> preloadedPages;

  @override
  State<IndexedStackWidget> createState() => _IndexedStackWidgetState();
}

class _IndexedStackWidgetState extends State<IndexedStackWidget> {
  late final List<bool> initializedChildren;

  @override
  void initState() {
    super.initState();
    initializedChildren = List.generate(
      widget.children.length,
      (i) => widget.preloadedPages.contains(SharedNavBarEnum.values[i]) || i == widget.index,
    );
  }

  @override
  void didUpdateWidget(IndexedStackWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.index != widget.index) selectedChild(widget.index);
    for (var page in widget.preloadedPages) {
      selectedChild(page.index);
    }
  }

  void selectedChild(int? index) {
    if (index == null) return;
    if (!initializedChildren[index]) initializedChildren[index] = true;
  }

  List<Widget> get children {
    return List.generate(widget.children.length, (i) {
      if (widget.preloadedPages.contains(SharedNavBarEnum.values[i])) {
        return widget.children[i];
      } else {
        return initializedChildren[i] ? widget.children[i] : const SizedBox.shrink();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return IndexedStack(
      alignment: widget.alignment,
      textDirection: widget.textDirection,
      sizing: widget.sizing,
      index: widget.index,
      children: children,
    );
  }
}
