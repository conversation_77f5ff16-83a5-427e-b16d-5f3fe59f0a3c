import 'package:asset_force_mobile_v2/core/theme/font_icon.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:flutter/material.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/dev/customIconPreview')
class CustomIconPreviewWidget extends StatefulWidget {
  const CustomIconPreviewWidget({super.key});

  @override
  State createState() => _CustomIconPreviewWidgetState();
}

class _CustomIconPreviewWidgetState extends State<CustomIconPreviewWidget> {
  final TextEditingController _searchController = TextEditingController();
  List<MapEntry<String, IconData>> _filteredIcons = [];

  @override
  void initState() {
    super.initState();

    _filteredIcons = FontIcons.getAllIcons().entries.toList();

    _searchController.addListener(() {
      _filterIcons();
    });
  }

  void _filterIcons() {
    final String searchTerm = _searchController.text.toLowerCase();
    setState(() {
      _filteredIcons = FontIcons.getAllIcons().entries.where((entry) {
        final name = entry.key.toLowerCase();
        final codePoint = '0x${entry.value.codePoint.toRadixString(16)}'.toLowerCase();
        return name.contains(searchTerm) || codePoint.contains(searchTerm);
      }).toList();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.d(
      'FontIcons.getAllIcons().length: ${FontIcons.getAllIcons().length}, ${MediaQuery.of(context).size.width}',
    );
    return Scaffold(
      appBar: AppBar(
        title: Row(children: [Text('总数: ${_filteredIcons.length}')]),
        actions: [
          // 搜索框
          Container(
            width: 200,
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索图标名称或代码...',
                filled: true,
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8), borderSide: BorderSide.none),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                isDense: true,
              ),
              style: const TextStyle(color: Colors.black),
            ),
          ),
        ],
      ),
      body: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: MediaQuery.of(context).size.width ~/ 200,
          childAspectRatio: 1,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: _filteredIcons.length,
        itemBuilder: (context, index) {
          final iconName = _filteredIcons[index].key;
          final iconData = _filteredIcons[index].value;
          final hexCodePoint = '\\u${iconData.codePoint.toRadixString(16)}';

          return Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 图标显示区域
                  Expanded(
                    child: Center(
                      child: Icon(
                        iconData,
                        size: 48,
                        color: Colors.purple, // 设置图标颜色
                      ),
                    ),
                  ),
                  const Divider(),
                  // 信息显示区域
                  SizedBox(
                    height: 60,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 图标名称
                        Text(
                          iconName,
                          style: const TextStyle(fontSize: 12, color: Colors.black),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        // CodePoint
                        Text(hexCodePoint, style: const TextStyle(color: Colors.black)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
