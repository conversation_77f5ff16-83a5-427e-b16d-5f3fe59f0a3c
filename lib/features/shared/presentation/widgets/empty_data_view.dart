import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:flutter/material.dart';

/// 空数据提示组件
///
/// 当列表没有数据时显示的提示组件
class EmptyDataView extends StatelessWidget {
  /// 提示文本
  final String message;

  /// 文本样式
  final TextStyle? textStyle;

  /// 垂直内边距
  final double verticalPadding;

  const EmptyDataView({super.key, this.message = 'データがありません', this.textStyle, this.verticalPadding = 20});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: verticalPadding),
        child: Text(message, style: textStyle ?? const TextStyle(fontSize: 16, color: AppTheme.whiteColor)),
      ),
    );
  }
}
