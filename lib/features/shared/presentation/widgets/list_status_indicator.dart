import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:flutter/material.dart';

/// 列表状态指示器
///
/// 用于显示列表的不同状态，如加载中、全部加载完成、无数据等
/// 符合Clean Architecture的设计原则，将UI展示与业务逻辑分离
class ListStatusIndicator extends StatelessWidget {
  /// 是否正在加载
  final bool isLoading;

  /// 是否已加载全部数据
  final bool isAllLoaded;

  /// 是否没有数据
  final bool isEmpty;

  /// 加载中提示文本
  final String loadingText;

  /// 全部加载完成提示文本
  final String allLoadedText;

  /// 无数据提示文本
  final String emptyText;

  /// 文本样式
  final TextStyle? textStyle;

  /// 内边距
  final EdgeInsets padding;

  const ListStatusIndicator({
    super.key,
    this.isLoading = false,
    this.isAllLoaded = false,
    this.isEmpty = false,
    this.loadingText = '読み込んでいます...',
    this.allLoadedText = '全件表示されました',
    this.emptyText = 'データがありません',
    this.textStyle,
    this.padding = const EdgeInsets.all(16.0),
  });

  @override
  Widget build(BuildContext context) {
    // 默认文本样式
    final defaultStyle = textStyle ?? const TextStyle(color: AppTheme.white85Color, fontSize: 14);

    if (isLoading) {
      return SliverToBoxAdapter(
        child: Padding(
          padding: padding,
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(color: AppTheme.white85Color),
                if (loadingText.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(loadingText, style: defaultStyle),
                  ),
              ],
            ),
          ),
        ),
      );
    } else if (isEmpty) {
      return SliverToBoxAdapter(
        child: Padding(
          padding: padding,
          child: Center(child: Text(emptyText, style: defaultStyle)),
        ),
      );
    } else if (isAllLoaded) {
      return SliverToBoxAdapter(
        child: Padding(
          padding: padding,
          child: Center(child: Text(allLoadedText, style: defaultStyle)),
        ),
      );
    }

    // 如果没有任何状态需要显示，则返回空组件
    return const SliverToBoxAdapter(child: SizedBox.shrink());
  }
}
