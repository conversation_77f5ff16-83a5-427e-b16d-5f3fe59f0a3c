import 'package:flutter/material.dart';

///
/// 加载中的 Widget , 显示一个一直转圈的圆.
///
class LoadingWidget extends StatelessWidget {
  const LoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(red: 0, green: 0, blue: 0, alpha: 77),
        child: const Center(child: CircularProgressIndicator(valueColor: AlwaysStoppedAnimation<Color>(Colors.white))),
      ),
    );
  }
}
