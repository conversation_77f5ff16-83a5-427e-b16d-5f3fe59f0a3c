import 'package:flutter/material.dart';
import 'package:get/get.dart';

///
/// 支持 下拉刷新 + 加载更多 回到顶部. 的列表组件
///
///
/// 使用方法:
///
///
/// 在需要使用的页面内, 导入下面的代码.
///
/// final GlobalKey&lt;RefreshLoadMoreListState&gt; _refreshListKey = GlobalKey&lt;RefreshLoadMoreListState&gt;();
///
/// 调用
///
/// _refreshListKey.currentState?.scrollToTop();
///
///
class RefreshLoadMoreList<T> extends StatefulWidget {
  /// 刷新回调
  final Future<void> Function() onRefresh;

  /// 加载更多回调
  final Future<void> Function() onLoadMore;

  /// 是否正在加载, 外部需要在 loadMore 相关逻辑内主动修改这个状态.
  final RxBool isLoading;

  /// 是否还有更多数据. [true] 表示没有更多数据
  final RxBool noMoreData;

  /// 列表数据
  final RxList<T> items;

  /// 列表项构建回调
  final Widget Function(BuildContext, int, T) itemBuilder;

  /// 加载更多指示器
  final Widget? loadingIndicator;

  /// 没有更多数据时显示的 Widget
  final Widget? noMoreDataWidget;

  /// 数据列表为空时的组件.
  final Widget? emptyDataWidget;

  /// [listview]'s [shrinkWrap], 设置为false， 则只显示最大高度， 否则填充整个屏幕
  final bool shrinkWrap;

  /// [listview]'s padding info.   默认设置为 zero， 去掉当内容他不能填充整个屏幕时，底部的白边
  final EdgeInsetsGeometry? padding;

  /// 下拉刷新时显示的 widget（可选）
  final Widget? pullDownWidget;

  /// 固定在顶部的 Widget（可选）
  final Widget? fixedHeader;

  final Widget? scrollHeader;

  const RefreshLoadMoreList({
    super.key,
    required this.onRefresh,
    required this.onLoadMore,
    required this.isLoading,
    required this.noMoreData,
    required this.items,
    required this.itemBuilder,
    this.shrinkWrap = false,
    this.padding = EdgeInsets.zero,
    this.loadingIndicator = const Center(
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(8.0),
            child: CircularProgressIndicator(color: Colors.white),
          ),
          Text('読み込んでいます...', style: TextStyle(color: Colors.white)),
        ],
      ),
    ),
    this.noMoreDataWidget = const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: Text('全件表示されました', style: TextStyle(fontSize: 16.0, color: Colors.white)),
      ),
    ),
    this.emptyDataWidget = const Center(
      child: Padding(
        padding: EdgeInsets.all(8.0),
        child: Text('データがありません', style: TextStyle(fontSize: 16.0, color: Colors.white)),
      ),
    ),
    this.pullDownWidget = const Padding(
      padding: EdgeInsets.all(12.0),
      child: Text('引き下げて更新', style: TextStyle(color: Colors.white)),
    ),
    this.fixedHeader,
    this.scrollHeader,
  });

  @override
  State createState() => RefreshLoadMoreListState<T>();
}

class RefreshLoadMoreListState<T> extends State<RefreshLoadMoreList<T>> {
  late ScrollController _scrollController;
  double _dragOffset = 0.0;

  /// 返回顶部
  void scrollToTop() {
    _scrollController.animateTo(0.0, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
  }

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    // 判断是否滑动到接近底部，触发加载更多
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      // 当滑到底部并且没有正在加载数据时触发加载更多
      if (!widget.isLoading.value) {
        widget.onLoadMore();
      }
    }
    // 更新下拉的偏移量
    if (_scrollController.position.pixels <= 0) {
      setState(() {
        _dragOffset = -_scrollController.position.pixels;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: widget.onRefresh,
      child: Column(
        children: [
          // 如果有固定的顶部 Widget，则添加它
          if (widget.fixedHeader != null) widget.fixedHeader!,

          if (_dragOffset > 100) // 触发下拉刷新显示
            widget.pullDownWidget ?? const SizedBox.shrink(),

          Expanded(
            child: Obx(() {
              return ListView.builder(
                controller: _scrollController,
                shrinkWrap: widget.shrinkWrap,
                padding: widget.padding,
                // 确保ListView始终可滚动，即使内容较少也能触发下拉刷新
                physics: const AlwaysScrollableScrollPhysics(),
                // 添加这一行
                itemCount: widget.items.length + 1 + (widget.scrollHeader != null ? 1 : 0),
                // +1 是为了加载更多指示器
                itemBuilder: (context, index) {
                  if (widget.scrollHeader != null && index == 0) {
                    // 第一个项目显示scrollHeader
                    return widget.scrollHeader!;
                  }

                  // 调整实际项目的索引
                  final actualIndex = widget.scrollHeader != null ? index - 1 : index;

                  if (actualIndex == widget.items.length) {
                    // 如果是最后一个 item，则显示加载更多指示器或者没有数据了
                    return Obx(() {
                      // 加载更多指示器
                      if (widget.isLoading.value) {
                        return widget.loadingIndicator ?? const SizedBox.shrink();
                      } else if (widget.items.isEmpty) {
                        return widget.emptyDataWidget ?? const SizedBox.shrink();
                      } else if (widget.noMoreData.value) {
                        return widget.noMoreDataWidget ?? const SizedBox.shrink();
                      }
                      return const SizedBox.shrink(); // 没有更多数据或未加载完成时，什么都不显示
                    });
                  } else {
                    // 正常的列表项
                    return widget.itemBuilder(context, actualIndex, widget.items[actualIndex]);
                  }
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
