import 'package:asset_force_mobile_v2/features/asset/asset_list/presentation/controllers/asset_list_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ScrollToTopButton extends GetView<AssetListController> {
  final Function scrollToTop;
  const ScrollToTopButton({super.key, required this.scrollToTop});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          scrollToTop();
        },
        borderRadius: BorderRadius.circular(5.0),
        child: Container(
          width: 32.0,
          height: 32.0,
          decoration: BoxDecoration(color: Colors.white.withAlpha(230), borderRadius: BorderRadius.circular(5.0)),
          alignment: Alignment.center,
          child: const Icon(Icons.arrow_upward, color: Color(0xFF757575), size: 18.0),
        ),
      ),
    );
  }
}
