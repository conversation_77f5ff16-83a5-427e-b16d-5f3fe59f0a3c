import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/empty_data_view.dart';
import 'package:flutter/material.dart';

/// 可选择列表视图
///
/// 用于显示可选择的列表项，支持空数据提示
class SelectableListView<T> extends StatelessWidget {
  /// 列表项数据
  final List<T> items;

  /// 当前选中项索引
  final int selectedIndex;

  /// 点击列表项回调
  final Function(int) onItemTap;

  /// 空数据提示文本
  final String emptyMessage;

  /// 获取显示文本的函数
  final String Function(T) getDisplayText;

  final VoidCallback? onListLoaded;

  const SelectableListView({
    super.key,
    required this.items,
    required this.selectedIndex,
    required this.onItemTap,
    required this.getDisplayText,
    this.emptyMessage = 'データがありません',
    this.onListLoaded,
  });

  @override
  Widget build(BuildContext context) {
    // 检查列表是否为空
    if (items.isEmpty) {
      LogUtil.d('列表为空，显示空数据提示');
      return EmptyDataView(message: emptyMessage);
    }

    LogUtil.d('构建列表，项目数量: ${items.length}');
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          if (index == items.length - 1 && onListLoaded != null) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              onListLoaded!();
            });
          }
          return Container(
            color: AppTheme.white85Color,
            child: Column(
              children: [
                ListTile(
                  minTileHeight: 40,
                  title: Text(getDisplayText(item), style: const TextStyle(fontSize: 16)),
                  trailing: selectedIndex == index ? const Icon(Icons.check, color: AppTheme.black87Color) : null,
                  onTap: () => onItemTap(index),
                ),
                if (index < items.length - 1) const Divider(height: 1),
              ],
            ),
          );
        },
      ),
    );
  }
}

// 为了保持向后兼容性，提供一个仅使用字符串的构造函数
class StringSelectableListView extends StatelessWidget {
  final List<String> items;
  final int selectedIndex;
  final Function(int) onItemTap;
  final String emptyMessage;

  const StringSelectableListView({
    super.key,
    required this.items,
    required this.selectedIndex,
    required this.onItemTap,
    this.emptyMessage = 'データがありません',
  });

  @override
  Widget build(BuildContext context) {
    return SelectableListView<String>(
      items: items,
      selectedIndex: selectedIndex,
      onItemTap: onItemTap,
      getDisplayText: (item) => item,
      emptyMessage: emptyMessage,
    );
  }
}
