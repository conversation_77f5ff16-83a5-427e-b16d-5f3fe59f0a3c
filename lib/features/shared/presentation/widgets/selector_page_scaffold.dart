import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/selector_search_bar.dart';
import 'package:asset_force_mobile_v2/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

/// 选择器页面脚手架
///
/// 提供统一的选择器页面布局和样式
class SelectorPageScaffold extends StatelessWidget {
  /// 页面标题
  final String title;

  /// 清除按钮点击回调
  final VoidCallback onClearBtnClick;

  /// 搜索文本变化回调
  final Function(String) onSearchChanged;

  /// 页面主体内容
  final Widget body;

  /// 搜索提示文本
  final String searchHint;

  /// 排序方式
  final sortMode = true.obs;

  /// 排序按钮点击回调
  final Function(bool)? onSortBtnClick;

  SelectorPageScaffold({
    super.key,
    required this.title,
    required this.onClearBtnClick,
    required this.onSearchChanged,
    this.onSortBtnClick,
    required this.body,
    this.searchHint = '検索',
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        leadingWidth: 50,
        leading: IconButton(icon: const Icon(Icons.arrow_back_ios_new, size: 20), onPressed: () => Get.back()),
        actions: [
          onSortBtnClick == null
              ? Padding(
                  padding: const EdgeInsets.only(right: 10),
                  child: TextButton(
                    style: TextButton.styleFrom(padding: EdgeInsets.zero, minimumSize: Size.zero),
                    onPressed: onClearBtnClick,
                    child: const Text('クリア', style: TextStyle(color: Colors.white)),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.only(right: 10),
                  child: Container(
                    margin: const EdgeInsets.only(right: 10),
                    width: 25,
                    height: 25,
                    child: Obx(() {
                      return GestureDetector(
                        onTap: () {
                          sortMode.toggle();
                          if (onSortBtnClick != null) {
                            onSortBtnClick!(sortMode.value);
                          }
                        },
                        child: sortMode.value
                            ? SvgPicture.asset(
                                Assets.iconsSortAlphaDown,
                                colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                              )
                            : SvgPicture.asset(
                                Assets.iconsSortAlphaDownAlt,
                                colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                              ),
                      );
                    }),
                  ),
                ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(50),
          child: SelectorSearchBar(onChanged: onSearchChanged, hintText: searchHint),
        ),
      ),
      body: SingleChildScrollView(
        child: Container(margin: const EdgeInsets.all(10), child: body),
      ),
    );
  }
}
