import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:flutter/material.dart';

/// 选择器搜索栏组件
///
/// 用于在选择器页面中显示搜索输入框
/// 提供统一的搜索栏样式和行为
class SelectorSearchBar extends StatelessWidget {
  /// 搜索文本变化回调
  final Function(String) onChanged;

  /// 搜索提示文本
  final String hintText;

  /// 搜索栏高度
  final double height;

  /// 搜索栏圆角
  final double borderRadius;

  /// 搜索栏图标
  final IconData icon;

  /// 搜索栏图标颜色
  final Color iconColor;

  /// 搜索栏图标大小
  final double iconSize;

  const SelectorSearchBar({
    super.key,
    required this.onChanged,
    this.hintText = '検索',
    this.height = 40,
    this.borderRadius = 8,
    this.icon = Icons.search,
    this.iconColor = AppTheme.black87Color,
    this.iconSize = 20,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      height: height,
      decoration: BoxDecoration(
        color: AppTheme.whiteColor,
        borderRadius: BorderRadius.all(Radius.circular(borderRadius)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon, color: iconColor, size: iconSize),
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: const TextStyle(color: AppTheme.timePickerDividerColor, fontSize: 16),
                border: InputBorder.none,
                isDense: true,
              ),
              style: const TextStyle(fontSize: 16),
              textAlignVertical: TextAlignVertical.center,
              cursorHeight: 20,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }
}
