import 'dart:convert';

import 'package:asset_force_mobile_v2/core/env/env_helper.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/deep_convert_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/models/asset_detail_arguments_model.dart';
import 'package:asset_force_mobile_v2/features/location_select/data/models/location_page_param.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/dpp_info_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/user_role_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_ai_ocr_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// 原生返回页面迁移
enum ScanResultType {
  assetDetail('1'),
  location('5'),
  workflowUnScanned('12');

  final String value;
  const ScanResultType(this.value);

  static ScanResultType? fromValue(String? value) {
    return ScanResultType.values.firstWhereOrNull((e) => e.value == value);
  }
}

/// 扫描插件的抽象基类
/// 该类为各种扫描功能提供基础架构和通用方法，用于与原生平台通信
abstract class BaseScanAbstractPlugin {
  /// 技能（功能）名称，用于构建方法通道标识符
  final String skillName;

  /// 用户相关信息接口
  late final UserRepository _userRepository;

  /// 方法通道，用于与原生平台进行通信
  late final MethodChannel _channel;

  /// 构造函数
  /// @param skillName 必需参数，指定技能名称
  BaseScanAbstractPlugin({required this.skillName}) {
    // 获取应用包名，用于构建唯一的方法通道标识符
    final String bundleId = EnvHelper.getAppNativePackageName();
    // 初始化方法通道，格式为 "包名/技能名"
    _channel = MethodChannel('$bundleId/$skillName');
    // 初始化用户仓库，用于后续获取用户角色等信息
    _userRepository = UserRepositoryImpl(dioUtil: DioUtil());
  }

  /// 显示位置确认对话框
  /// 当扫描结果需要确认位置信息时调用此方法
  /// @param re 原生返回的响应数据
  /// @return 返回布尔值，表示用户是否确认了位置
  Future<dynamic> _showConfirmationLocationDialog({required dynamic re, required dynamic da}) async {
    // 深度转换数据，确保数据格式一致
    final convertedData = DataConverterUtil.deepConvert(re);

    // 验证转换后的数据是否为Map类型
    if (convertedData is! Map) return null;

    final data = convertedData['data'];

    // 如果数据无效或类型不匹配，返回false
    if (data == null) return null;

    // 获取当前位置信息
    final String currentLocation = StorageUtils.getAssetScanLocation();
    // 导航到位置选择页面，并传递必要参数
    final result = await Get.find<NavigationService>().navigateTo(
      AutoRoutes.locationSelectPage,
      arguments: LocationPageParam(isFromUserSettings: true, location: currentLocation),
    );

    // 如果返回结果是字符串，表示用户已选择位置
    if (result is String && data is Map) {
      // 保存用户选择的位置
      await StorageUtils.setAssetScanLocation(assetLocation: result);
      return {...da, ...data};
    }

    // 用户未选择位置，返回false
    return null;
  }

  /// 跳转到资产详细
  /// @param re 原生返回的响应数据
  /// @return 返回布尔值，表示用户迁移了资产详细
  Future<dynamic> _showConfirmationAssetDetail({required dynamic re, required dynamic da}) async {
    // 深度转换数据，确保数据格式一致
    final convertedData = DataConverterUtil.deepConvert(re);

    // 验证转换后的数据是否为Map类型
    if (convertedData is! Map) return null;

    final data = convertedData['data'];

    // 如果数据无效或类型不匹配，返回false
    if (data == null) return null;
    int? assetId;
    if (data['assetId'] is int) {
      assetId = data['assetId'];
    } else {
      assetId = int.tryParse(data['assetId']);
    }

    // 如果数据无效或类型不匹配，返回false
    if (assetId == null) return null;

    await Get.find<NavigationService>().toAssetDetail(
      AssetDetailArguments(assetId: assetId, fromScanPage: AssetDetailSource.scanPage),
    );
    if (data is Map)
      return {...da, ...data};
    else
      return null;
  }

  /// 跳转到workflow未扫描资产
  Future<dynamic> _showConfirmationWorkflowUnScanned({required dynamic re, required dynamic da}) async {
    // 深度转换数据，确保数据格式一致
    final convertedData = DataConverterUtil.deepConvert(re);

    // 验证转换后的数据是否为Map类型
    if (convertedData is! Map) return null;

    final data = convertedData['data'];
    // 如果数据无效或类型不匹配，返回false
    if (data == null) return null;

    // 导航到位置选择页面，并传递必要参数
    await Get.find<NavigationService>().navigateTo(AutoRoutes.approvalUnFinishScanList, arguments: da);
    data['isFromScannedPagesUnfinishedAssetsPage'] = true;
    return {...da, ...data as Map};
  }

  /// 处理扫描结果数据并验证类型
  /// @param re 原生返回的响应数据
  /// @param expectedType 期望的类型值
  /// @return 如果数据有效且类型匹配，返回处理后的数据；否则返回null
  String? _processScanResponse({required dynamic re}) {
    // 深度转换数据，确保数据格式一致
    final convertedData = DataConverterUtil.deepConvert(re);

    // 验证转换后的数据是否为Map类型
    if (convertedData is! Map) return null;

    final data = convertedData['data'];

    // 验证data字段是否为Map类型
    if (data is! Map) return null;

    final type = data['type'];

    return type;
  }

  T _deepCopy<T>(T object) {
    final String jsonString = jsonEncode(object); // 将对象转换为 JSON 字符串
    return jsonDecode(jsonString); // 解析成新的对象
  }

  /// 调用原生方法的封装函数
  /// @param method 要调用的原生方法名
  /// @param isSetBaseData 是否需要设置基础数据, true 会追加基础数据, 然后传递给native, false 会直接将 data 传递给native.
  /// @param data 可选参数，要传递给原生方法的数据
  /// @return 返回泛型T类型的响应数据
  Future<Map<String, dynamic>> invokeChannelMethod<T>({
    required String method,
    bool isSetBaseData = true,
    Map<String, dynamic>? data,
  }) async {
    try {
      T? response;
      if (isSetBaseData) {
        // 如果需要设置基础数据，则调用_getInfoPluginBase方法获取完整数据
        final dataCopy = await _getInfoPluginBase(data: _deepCopy(data));
        // 通过方法通道调用原生方法(有参数传到原生)
        response = await _channel.invokeMethod<T>(method, dataCopy);
      } else {
        // 通过方法通道调用原生方法(无参数传到原生)
        response = await _channel.invokeMethod<T>(method, data);
      }

      final String? resultType = _processScanResponse(re: response);
      dynamic result;
      // 如果任意一个方法返回了有效数据，就使用该数据
      switch (ScanResultType.fromValue(resultType)) {
        case ScanResultType.location:
          // 迁移场所
          result = await _showConfirmationLocationDialog(re: response, da: data);
          break;
        case ScanResultType.assetDetail:
          // 迁移资产详细
          result = await _showConfirmationAssetDetail(re: response, da: data);
          break;
        case ScanResultType.workflowUnScanned:
          result = await _showConfirmationWorkflowUnScanned(re: response, da: data);
          break;
        default:
          result = null;
      }

      // 如果需要重试（用户更改了位置），则递归调用当前方法
      if (result != null) {
        final Map<String, dynamic> resultData = Map<String, dynamic>.from(result);
        return invokeChannelMethod(method: method, isSetBaseData: isSetBaseData, data: resultData);
      }

      // 返回原生方法的响应结果
      final Map<String, dynamic> reData = DataConverterUtil.deepConvert(response);
      return reData;
    } on PlatformException catch (e) {
      if (e.code == 'INVALID_ARGUMENTS') {
        LogUtil.e('原生代码指定必须传入参数，你需要传入参数 ${e.message}');
      } else {
        LogUtil.e('Failed to get message from native: ${e.message}');
      }
      throw SystemException();
    } catch (e, stackTrace) {
      // 记录错误日志
      LogUtil.e('Failed to call $method', data: e, stackTrace: stackTrace);
      // 抛出系统异常
      throw SystemException();
    }
  }

  /// 获取插件基础信息
  /// 收集所有必要的用户信息、配置和OCR信息，用于传递给原生平台
  /// @param data 原始数据对象
  /// @return 返回包含完整基础信息的数据对象
  Future<Map<String, dynamic>> _getInfoPluginBase({required Map<String, dynamic>? data}) async {
    // 从本地存储获取各种用户和系统配置信息
    final String? token = StorageUtils.get<String>(StorageUtils.keyToken);
    final String? userName = StorageUtils.get<String>(StorageUtils.keyUserName);
    final int? userId = StorageUtils.get<int>(StorageUtils.keyUserId);
    final String? tenantId = StorageUtils.get<String>(StorageUtils.keyTenantId);
    final String apiHost = EnvHelper.getApiHost();
    final String? refreshToken = StorageUtils.get<String>(StorageUtils.keyRefreshToken);
    final bool? isVolume = StorageUtils.get<bool>(StorageUtils.keyIsVolume);
    final bool? isInpact = StorageUtils.get<bool>(StorageUtils.keyIsInpact);
    final int? scanMusicList = StorageUtils.get<int>(StorageUtils.keyScanMusicList);
    final double? musicVolume = StorageUtils.get<double>(StorageUtils.keyMusicVolume);
    final double? inpactVolume = StorageUtils.get<double>(StorageUtils.keyInpactVolume);
    final String? location = StorageUtils.get<String>(StorageUtils.keyLocation);

    // 从并行结果中提取数据并进行类型检查
    final Map<String, dynamic>? ocrInfo = _getOcrInfo();
    final List<UserRoleResponseData>? userRoles = await _userRepository.getUserRole();

    // 从OCR信息中提取特定字段
    final String ocrId = _getStringValueOrEmpty(ocrInfo, 'dppId');
    final String ocrAccessKey = _getStringValueOrEmpty(ocrInfo, 'dppAccessKey');

    final String ocrCarNumberEndpoint = ocrInfo?['trafficRental']?['moduleUrl'] ?? '';
    final String ocrUniversalSerialNumberEndpoint = ocrInfo?['welcia']?['moduleUrl'] ?? '';

    // 复制原始数据，避免修改原始对象
    final dataCopy = data ?? {};

    // 获取资产扫描位置信息并添加到数据中
    final String locationInfo = StorageUtils.getAssetScanLocation();
    dataCopy['locationInfo'] = locationInfo;
    // 添加用户角色列表到数据中
    dataCopy['userRoleList'] = userRoles?.map((d) => d.toJson()).toList() ?? [];
    await StorageUtils.set(StorageUtils.keyUserRoleList, jsonEncode(dataCopy['userRoleList']));

    // 获取扫描缩放值
    final int val = StorageUtils.get<int>(StorageUtils.KeyScanZoom) ?? 0;

    // 工作流特定处理
    final String? quantityCountScanData = StorageUtils.get<String>(StorageUtils.isNumberNeedAutoIncrease);
    var isNumberNeedToBeAutoInCreas = false;
    if (quantityCountScanData != null && quantityCountScanData != '') {
      isNumberNeedToBeAutoInCreas = jsonDecode(quantityCountScanData)['isQuantityCountScan'] ?? false;
    }
    dataCopy['isNumberNeedToBeAutoIncreas'] = isNumberNeedToBeAutoInCreas;

    // 构建完整的信息数据对象
    final indoData = {
      'haveData': data == null || data.isEmpty ? false : true, // 标记是否有原始数据
      'data': dataCopy, // 原始数据的副本
      'tenantId': tenantId, // 租户ID
      'userName': userName, // 用户名
      'userId': userId, // 用户ID
      'token': token, // 认证令牌
      'apiHost': apiHost, // API主机地址
      'refreshToken': refreshToken, // 刷新令牌
      'isVolume': isVolume, // 是否启用音量
      'isInpact': isInpact, // 是否启用震动
      'scanMusicList': scanMusicList, // 音乐列表
      'musicVolume': musicVolume, // 音乐音量
      'inpactVolume': inpactVolume, // 震动强度
      'scanZoom': val, // 扫描缩放值
      'location': location, // 位置信息
      'ocrId': ocrId, // OCR ID
      'ocrAccessKey': ocrAccessKey, // OCR 访问密钥
      'ocrCarNumberEndpoint': ocrCarNumberEndpoint, // 车牌OCR端点
      'ocrInfo': ocrInfo,
      'ocrUniversalSerialNumberEndpoint': ocrUniversalSerialNumberEndpoint, // 通用序列号OCR端点
    };

    // 添加工作流特定字段
    if (skillName == 'workflow_scan_plugin') {
      indoData.addAll({
        'typeOfScan': dataCopy['scanType'] ?? '',
        'assetTypeName': dataCopy['assetTypeName'] ?? '',
        'taskName': dataCopy['taskName'] ?? '',
        'workflowName': dataCopy['workflowName'] ?? '',
      });
    }

    // 返回带有'value'键的完整数据对象
    return {'value': indoData};
  }

  /// 获取OCR信息
  /// 从本地存储中读取和解析OCR相关配置信息
  /// @return 返回OCR模块映射表，键为模块类型，值为模块对象
  Map<String, dynamic>? _getOcrInfo() {
    try {
      // 从存储中获取OCR信息字符串
      final String? dppInfoStr = StorageUtils.get<String>(StorageUtils.keyOcrInfo);
      // 解析JSON字符串为Map对象
      final Map<String, dynamic> dppInfoObj = jsonDecode(dppInfoStr ?? '');
      // 将Map对象转换为DppInfoModel对象
      final DppInfoModel dppInfo = DppInfoModel.fromJson(dppInfoObj);
      // 获取模块列表
      final List<ModuleModel>? dppInfoModules = dppInfo.modules;

      // 检查模块列表是否存在并且非空
      if (dppInfoModules == null) {
        return null;
      }
      if (dppInfoModules.isEmpty) {
        return null;
      }

      // 创建结果映射表
      final Map<String, ModuleModel> resultModuleModel = {};
      // 遍历所有模块
      for (int i = 0; i < dppInfoModules.length; i++) {
        final ModuleModel item = dppInfoModules[i];
        final String? type = item.moduleType;
        // 跳过没有类型的模块
        if (type == null || type.isEmpty) {
          continue;
        }

        // 根据类型值查找对应的枚举
        final val = SharedAIOcrTypeEnum.values.firstWhereOrNull((e) => e.value == type);
        // 根据不同的OCR类型，将模块添加到结果映射表中
        switch (val) {
          case SharedAIOcrTypeEnum.SMFL_RENTAL:
          case SharedAIOcrTypeEnum.TRAFFIC_RENTAL:
          case SharedAIOcrTypeEnum.WELCIA:
            resultModuleModel[val!.dataKey] = item;
            break;
          default:
            // 处理默认情况，防止出现未匹配的值
            break;
        }
      }
      final objModuleModel = resultModuleModel.map((key, value) {
        return MapEntry(key, value.toJson());
      });
      // OCR模块信息, 模仿ionic中ocr数据传入格式重组
      return {...objModuleModel, ...dppInfoObj};
    } catch (e, stackTrace) {
      // 记录错误日志
      LogUtil.e('getOcrInfo Error: ', data: e, stackTrace: stackTrace);
      // 抛出系统异常
      throw SystemException();
    }
  }

  /// 从Map中获取字符串值，如果不存在或为空则返回空字符串
  /// @param map 源数据Map
  /// @param key 要获取的键
  /// @return 返回对应的字符串值，如果不存在或为空则返回空字符串
  String _getStringValueOrEmpty(Map<String, dynamic>? map, String key) {
    final value = map?[key] as String?;
    return value?.isNotEmpty == true ? value! : '';
  }
}
