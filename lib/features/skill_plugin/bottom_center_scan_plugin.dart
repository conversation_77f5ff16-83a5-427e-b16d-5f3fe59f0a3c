import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/base_abstract_plugin/base_scan_plugin_abstract.dart';

class CenterBarcodeScanPlugin extends BaseScanAbstractPlugin {
  CenterBarcodeScanPlugin() : super(skillName: 'center_barcode_scan_plugin');

  /// 'バーコード / QRコード / RFID'
  Future<Map<String, dynamic>> getLastPhotoTaken({required Map<String, dynamic>? data}) async {
    try {
      final dataCopy = {...?data}; // safe clone
      dataCopy['canActionScan'] = true;
      dataCopy['isFromRelationAsset'] = false;

      final result = await invokeChannelMethod(method: 'getLastPhotoTaken', data: dataCopy, isSetBaseData: true);
      LogUtil.d('getLastPhotoTaken result: $result, type: ${result.runtimeType}');

      return result;
    } catch (e, stackTrace) {
      LogUtil.e('Failed to call getLastPhotoTaken', data: e, stackTrace: stackTrace);
      throw SystemException();
    }
  }
}
