import 'package:asset_force_mobile_v2/core/utils/deep_convert_utils.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/base_abstract_plugin/base_scan_plugin_abstract.dart';

class OpenBarcodeScantModel {
  final String? QRCode;
  OpenBarcodeScantModel({required this.QRCode});
  factory OpenBarcodeScantModel.fromJson(Map<String, dynamic> json) {
    return OpenBarcodeScantModel(QRCode: json['QRCode'] as String?);
  }
}

class InvokeBackendUrlModel {
  final String? deepLinkUrl;
  InvokeBackendUrlModel({required this.deepLinkUrl});
  factory InvokeBackendUrlModel.fromJson(Map<String, dynamic> json) {
    return InvokeBackendUrlModel(deepLinkUrl: json['data'] as String?);
  }
}

class SsoScanPlugin extends BaseScanAbstractPlugin {
  SsoScanPlugin() : super(skillName: 'sso_plugin');

  Future<OpenBarcodeScantModel> openBarcodeScan() async {
    final json = await invoke<PERSON>hannelMethod(method: 'openBarcodeScan', isSetBaseData: false);
    final data = DataConverterUtil.deepConvert(json);
    return OpenBarcodeScantModel.fromJson(data);
  }

  Future<InvokeBackendUrlModel> toSsoSetting({required Map<String, dynamic> data}) async {
    final json = await invokeChannelMethod(method: 'invokeBackendUrl', data: data, isSetBaseData: false);
    if (json is String) {
      return InvokeBackendUrlModel(deepLinkUrl: null);
    }
    return InvokeBackendUrlModel.fromJson(json);
  }

  Future<void> toClearSsoLoginCache() async {
    await invokeChannelMethod(method: 'clearSsoLoginCache', isSetBaseData: false);
  }
}
