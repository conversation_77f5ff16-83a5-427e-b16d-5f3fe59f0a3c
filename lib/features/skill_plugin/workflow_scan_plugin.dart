import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/services/scan_handler_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_ai_ocr_type_enum.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/base_abstract_plugin/base_scan_plugin_abstract.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/ocr_universal_serial_number_plugin.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_scan_service.dart';

class WorkflowScanPlugin extends BaseScanAbstractPlugin {
  WorkflowScanPlugin() : super(skillName: 'workflow_scan_plugin');

  Future<Map<String, dynamic>> toWorkflowScan({required Map<String, dynamic>? data}) async {
    try {
      final Map<String, dynamic> result = await invokeChannelMethod(
        method: 'getWorkflowScan',
        data: data,
        isSetBaseData: true,
      );
      return result['data'];
    } catch (e, stackTrace) {
      LogUtil.e('Failed to call getWorkflowScan', data: e, stackTrace: stackTrace);
      throw SystemException();
    }
  }

  Future<Map<String, dynamic>> toNoSubprocessWorkflowScan({required Map<String, dynamic>? data}) async {
    try {
      final Map<String, dynamic> result = await invokeChannelMethod(
        method: 'getNoSubprocessWorkflowScan',
        data: data,
        isSetBaseData: true,
      );
      return result['data'];
    } catch (e, stackTrace) {
      LogUtil.e('Failed to call getWorkflowScan', data: e, stackTrace: stackTrace);
      throw SystemException();
    }
  }

  Future<Map<String, dynamic>> toNoClaimWorkflowScan({required Map<String, dynamic>? data}) async {
    try {
      final Map<String, dynamic> result = await invokeChannelMethod(
        method: 'getNoClaimWorkflowScan',
        data: data,
        isSetBaseData: true,
      );
      return result['data'];
    } catch (e, stackTrace) {
      LogUtil.e('Failed to call getWorkflowNoClaimScan', data: e, stackTrace: stackTrace);
      throw SystemException();
    }
  }

  Future<Map<String, dynamic>> workflowScanBarcode(WorkflowScanBarcodeData scanData) async {
    final SharedAIOcrTypeEnum selectedValue = await workflowButtonShowScanModeSelectionActionSheet();
    // TODO 此处当项目原生趋于稳定后需要迁移到共同方法（起始）
    if (SharedAIOcrTypeEnum.CANCEL == selectedValue) {
      return {};
    }
    switch (selectedValue) {
      case SharedAIOcrTypeEnum.BARCODE_QR_RFID:
        final result = await toWorkflowScan(data: scanData.toMap());
        return result;
      case SharedAIOcrTypeEnum.WELCIA:
        await OcrUniversalSerialNumberPlugin().universalSerialNumbersOcr(data: null);
        return {};
      default:
        return {};
    }
    // TODO 此处当项目原生趋于稳定后需要迁移到共同方法（结束）
  }

  Future<Map<String, dynamic>> noSubprocessWorkflowScanBarcode(WorkflowScanBarcodeData data) async {
    final isFromLocationSettingPage = data.isFromLocationSettingPage;
    if (isFromLocationSettingPage) {
    } else {
      final SharedAIOcrTypeEnum selectedValue = await workflowButtonShowScanModeSelectionActionSheet();

      if (SharedAIOcrTypeEnum.CANCEL == selectedValue) {
        return {};
      }
      if (selectedValue == SharedAIOcrTypeEnum.WELCIA) {
        final result = await OcrUniversalSerialNumberPlugin().universalSerialNumbersOcr(data: data.toMap());
        return result;
      }

      if (selectedValue == SharedAIOcrTypeEnum.BARCODE_QR_RFID) {
        final result = await WorkflowScanPlugin().toNoSubprocessWorkflowScan(data: data.toMap());
        return result;
      }
    }
    return {};
  }

  Future<Map<String, dynamic>> starNoClaimWorkflowScanBarcode(WorkflowScanBarcodeData data) async {
    // 处理 assetDataList
    final assetDataList = data.assetDataList?.map((value) {
      // 深拷贝 rawAssetList
      final assetData = Map<String, dynamic>.from(value['rawAssetList']);
      assetData['isScanFinish'] = false;
      assetData['isHasBeenStarted'] = false;
      assetData['isHasNotStarted'] = false;
      assetData['status'] = '';
      assetData['scanTimes'] = value['assetScannedCount'];
      assetData['assetCount'] = value['assetTotalCount'];
      return assetData;
    }).toList();
    data.assetDataList = assetDataList;
    final result = await WorkflowScanPlugin().toNoClaimWorkflowScan(data: data.toMap());
    return result;
  }
}
