import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/domain/repositories/workflow_repository.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';

/// WorkflowRepositoryImpl
///
/// 通过 DioUtil 调用后端API，返回未承认件数。
class WorkflowRepositoryImpl with RepositoryErrorHandler implements WorkflowRepository {
  final DioUtil dioUtil;

  /// 构造函数，通过注入 DioUtil 实例。
  WorkflowRepositoryImpl({required this.dioUtil});

  @override
  Future<int> getApplicationWorkflowCount() async {
    return executeRepositoryTask<int>(() async {
      final String endpoint = GlobalVariable.getUnapprovedProcessCount;
      LogUtil.d('Request: GET $endpoint');

      // 发送 GET 请求
      final response = await dioUtil.get(endpoint);

      // 假设响应里包含 'code' 和 'unApproveProcessCount'
      final data = response.data as Map<String, dynamic>?;
      if (data == null) {
        LogUtil.w('Response data is null');
        throw BusinessException('承認件数が取得できませんでした。');
      }

      // 检查 code 和 unApproveProcessCount
      final code = data['code'];
      final unApproveProcessCount = data['unApproveProcessCount'];

      if ((code == 0 || code == '0') && unApproveProcessCount is num) {
        // 返回数值
        return unApproveProcessCount.toInt();
      } else {
        LogUtil.w(
          'Response missing unApproveProcessCount or code is not 0. Code: $code, Count: $unApproveProcessCount',
        );
        throw BusinessException('未承認件数の取得に失敗しました。');
      }
    }, 'Failed to fetch unApproveProcessCount');
  }
}
