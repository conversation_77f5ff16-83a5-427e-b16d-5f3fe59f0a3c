import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/data/repositories/workflow_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/domain/usecases/get_workflow_approval_count_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/presentation/controllers/workflow_controller.dart';
import 'package:get/get.dart';

class WorkflowBinding extends Bindings {
  @override
  void dependencies() {
    // 注入 Repository
    Get.lazyPut<WorkflowRepositoryImpl>(() => WorkflowRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    // 注入 UseCase
    Get.lazyPut<GetUnapprovedProcessCountUseCase>(
      () => GetUnapprovedProcessCountUseCase(Get.find<WorkflowRepositoryImpl>()),
    );
    // 注入 Controller
    Get.lazyPut<WorkflowController>(
      () => WorkflowController(getWorkflowApprovalCountUseCase: Get.find<GetUnapprovedProcessCountUseCase>()),
    );
  }
}
