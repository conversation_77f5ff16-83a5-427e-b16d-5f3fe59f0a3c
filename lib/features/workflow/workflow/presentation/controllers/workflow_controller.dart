import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/domain/usecases/get_workflow_approval_count_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/presentation/states/workflow_ui_state.dart';
import 'package:get/get.dart';

/// WorkflowController
///
/// 用于处理业务逻辑、导航跳转等。
class WorkflowController extends BaseController {
  final WorkflowUIState state = WorkflowUIState();
  final GetUnapprovedProcessCountUseCase getWorkflowApprovalCountUseCase;

  WorkflowController({required this.getWorkflowApprovalCountUseCase});

  @override
  void onReady() {
    super.onReady();
    loadApprovalCount(); // 只加载「承認」所需的数量
  }

  /// 只加载承認行数据
  Future<void> loadApprovalCount() async {
    try {
      state.isApprovalLoading.value = true;
      final count = await getWorkflowApprovalCountUseCase(const NoParams());
      state.processingListCount.value = count.toString();
    } catch (e, stackTrace) {
      handleException(e, stackTrace);
      rethrow;
    } finally {
      state.isApprovalLoading.value = false;
    }
  }

  /// 通用跳转方法，根据传入的 initialIndex 决定显示哪个 Tab
  void navigateToWorkflowTabs(int initialIndex) {
    Get.toNamed(
      AutoRoutes.appTabWorkflowTabs,
      id: SharedNavBarEnum.workflow.navigatorId,
      arguments: {'initialIndex': initialIndex},
    );
  }
}
