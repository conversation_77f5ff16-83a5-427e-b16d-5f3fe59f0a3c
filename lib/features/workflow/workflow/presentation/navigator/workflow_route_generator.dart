import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/bindings/af_customize_view_bindings.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/presentation/bindings/workflow_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/presentation/pages/workflow_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/bindings/application_scan_list_bindings.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/bindings/application_view_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/bindings/workflow_application_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/pages/application_scan_list.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/pages/application_view_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/bindings/workflow_approval_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/choose_tantousha_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/new_application_form_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/new_application_submit_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/workflow_new_application_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/pages/choose_tantousha_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/pages/new_application_form_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/pages/new_application_submit_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/bindings/workflow_scan_list_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/pages/workflow_scan_list.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_tabs/presentation/bindings/workflow_tabs_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_tabs/presentation/pages/workflow_tabs_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// workflow局部路由
class WorkflowRouteGenerator {
  static final String workflowEmptyRoute = 'workflow/empty';

  static Route<dynamic>? generateRoute(RouteSettings settings) {
    if (settings.name == workflowEmptyRoute) {
      return MaterialPageRoute(builder: (_) => const SizedBox.shrink(), settings: settings);
    }

    Widget page;
    List<Bindings> bindings = [];
    TabsController? tabsController;
    if (Get.isRegistered<TabsController>()) {
      tabsController = Get.find<TabsController>();
    }
    switch (settings.name) {
      case AutoRoutes.workflow:
        tabsController?.showBar();
        page = const WorkflowPage();
        bindings = [WorkflowBinding()];
      case AutoRoutes.appTabWorkflowTabs:
        tabsController?.showBar();
        page = const WorkflowTabsPage();
        bindings = [
          WorkflowTabsBinding(),
          WorkflowNewApplicationBinding(),
          WorkflowApplicationBinding(),
          WorkflowApprovalBinding(),
        ];
      case AutoRoutes.newForm:
        tabsController?.hideBar();
        page = NewApplicationFormPage();
        bindings = [NewApplicationFormBinding(), AfCustomizeViewBindings()];
      case AutoRoutes.workflowScanList:
        tabsController?.hideBar();
        page = WorkflowScanList();
        bindings = [WorkflowScanListBinding()];
      case AutoRoutes.newFormSubmit:
        tabsController?.hideBar();
        page = NewApplicationSubmitPage();
        bindings = [NewApplicationSubmitBinding()];
      case AutoRoutes.chooseTantousha:
        tabsController?.hideBar();
        page = ChooseTantoushaPage();
        bindings = [ChooseTantoushaBinding()];

      case AutoRoutes.applicationView:
        tabsController?.hideBar();
        page = ApplicationViewPage();
        bindings = [ApplicationViewBinding()];

      case AutoRoutes.applicationScanList:
        tabsController?.hideBar();
        page = ApplicationScanList();
        bindings = [ApplicationScanListBindings()];

      default:
        final tPage = AutoRoutes.pages.where((p) => p.name == settings.name).firstOrNull;
        if (tPage == null) {
          tabsController?.showBar();
          page = const WorkflowPage();
          bindings = [WorkflowBinding()];
        } else {
          page = tPage.page();
          bindings = tPage.bindings;
        }
    }
    Get.routing.args = settings.arguments;
    return GetPageRoute(
      settings: settings,
      page: () => page,
      bindings: bindings,
      transitionDuration: const Duration(milliseconds: 0),
    );
  }
}
