import 'package:asset_force_mobile_v2/features/workflow/workflow/presentation/bindings/workflow_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow/presentation/controllers/workflow_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';
import 'package:intl/intl.dart';

/// WorkflowPage
@GetRoutePage('/workflow', binding: WorkflowBinding)
class WorkflowPage extends GetView<WorkflowController> {
  const WorkflowPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final processingCount = controller.state.processingListCount.value;
      final isApprovalLoading = controller.state.isApprovalLoading.value;

      return Scaffold(
        appBar: AppBar(centerTitle: true, title: const Text('ワークフロー'), automaticallyImplyLeading: false),
        body: RefreshIndicator(
          // 下拉刷新时调用此方法
          onRefresh: controller.loadApprovalCount,
          child: ListView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            children: [
              const SizedBox(height: 23),

              // 新規申請
              _ListMenuItem(
                iconPath: 'assets/icons/note-plus-outline.svg',
                label: '新規申請',
                onTap: () => controller.navigateToWorkflowTabs(0),
              ),

              // 申請
              _ListMenuItem(
                iconPath: 'assets/icons/text-box-outline.svg',
                label: '申請',
                onTap: () => controller.navigateToWorkflowTabs(1),
              ),

              // 承認
              _ListMenuItem(
                iconPath: 'assets/icons/stamper.svg',
                label: '承認',
                trailing: isApprovalLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.0,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                        ),
                      )
                    : _buildCountHint(processingCount),
                onTap: () => controller.navigateToWorkflowTabs(2),
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 构建右侧的红色数量提示
  Widget _buildCountHint(String processingCount) {
    // 将 processingCount 转成整数，如果无法解析则用 0
    final int number = int.tryParse(processingCount) ?? 0;
    // 使用 NumberFormat 将整数转成带逗号的字符串，如 500000 -> "500,000"
    final String textWithComma = NumberFormat('#,###').format(number);

    return Container(
      constraints: const BoxConstraints(minWidth: 28),
      // 固定高度28，左右padding8；数字多时会变宽
      height: 28,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFE6004D),
        // 与高度相同的一半做圆角，短文本时接近圆形；长文本时变成长椭圆。
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(color: const Color(0xFF0B3E86).withValues(alpha: 0.07), offset: const Offset(1, 1), blurRadius: 2),
        ],
      ),
      alignment: Alignment.center,
      child: Text(
        textWithComma,
        style: const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w500),
      ),
    );
  }
}

/// 可复用的列表菜单Item小部件
class _ListMenuItem extends StatelessWidget {
  final String iconPath;
  final String label;
  final Widget? trailing;
  final VoidCallback onTap;

  const _ListMenuItem({required this.iconPath, required this.label, required this.onTap, this.trailing});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [BoxShadow(color: Colors.black.withAlpha(13), offset: const Offset(0, 1), blurRadius: 2)],
        ),
        child: Row(
          children: [
            // 左侧图标区（不透明的白色）
            Container(
              width: 60,
              height: 60,
              decoration: const BoxDecoration(
                color: Colors.white, // 不透明纯白
                borderRadius: BorderRadius.only(topLeft: Radius.circular(10), bottomLeft: Radius.circular(10)),
              ),
              child: Center(
                child: SvgPicture.asset(
                  iconPath,
                  width: 26,
                  height: 26,
                  colorFilter: const ColorFilter.mode(Color(0xFF0B3E86), BlendMode.srcIn),
                ),
              ),
            ),

            // 右侧文字区（带透明度的白色）
            Expanded(
              child: Container(
                height: 60,
                decoration: const BoxDecoration(
                  color: Color.fromRGBO(255, 255, 255, 0.85),
                  borderRadius: BorderRadius.only(topRight: Radius.circular(10), bottomRight: Radius.circular(10)),
                ),
                child: Row(
                  children: [
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        label,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.black87),
                      ),
                    ),
                    if (trailing != null) ...[trailing!, const SizedBox(width: 6)],
                    Padding(
                      padding: const EdgeInsets.only(right: 10),
                      child: SvgPicture.asset(
                        'assets/icons/angle-right.svg',
                        width: 15,
                        height: 15,
                        colorFilter: const ColorFilter.mode(Colors.black, BlendMode.srcIn),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
