import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/controllers/application_scan_list_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/repositories/workflow_list_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/usecases/workflow_get_layout_settings_usecase.dart';
import 'package:get/get.dart';

class ApplicationScanListBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => GetTurlUseCase(s3Repository: Get.find<S3Repository>()));

    Get.lazyPut<WorkflowListRepository>(() => WorkflowListRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    Get.lazyPut<UserRepository>(() => UserRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    Get.lazyPut<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    Get.lazyPut(() => GetUserRoleUseCaseUseCase(userRepository: Get.find()));

    Get.lazyPut<WorkflowGetLayoutSettingsUseCase>(
      () => WorkflowGetLayoutSettingsUseCase(assetRepository: Get.find<AssetRepository>()),
    );

    Get.lazyPut(() => WorkflowActionService(workflowListRepository: Get.find<WorkflowListRepository>()));

    Get.lazyPut(
      () => ApplicationScanListController(
        dialogService: Get.find(),
        getUserRoleUseCaseUseCase: Get.find(),
        workflowActionService: Get.find(),
        workflowGetLayoutSettingsUseCase: Get.find(),
        getTurlUseCase: Get.find(),
      ),
    );
  }
}
