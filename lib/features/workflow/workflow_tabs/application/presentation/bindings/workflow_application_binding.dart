import 'package:asset_force_mobile_v2/core/extensions/getx_extension.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/get_navigation_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/domain/usecases/get_application_workflow_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/controllers/workflow_application_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/repositories/workflow_list_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:get/get.dart';

class WorkflowApplicationBinding extends Bindings {
  @override
  void dependencies() {
    // 注册 Repository 实现
    Get.lazyPutFenix<WorkflowListRepository>(() => WorkflowListRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    // 注册 UseCase
    Get.lazyPutFenix(() => GetApplicationWorkflowListUseCase(Get.find<WorkflowListRepository>()));
    Get.lazyPutFenix<UserRepository>(() => UserRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix(() => WorkflowActionService(workflowListRepository: Get.find<WorkflowListRepository>()));
    Get.lazyPut<NavigationService>(() => GetNavigationService());
    Get.lazyPutFenix(
      () => WorkflowApplicationController(
        Get.find<GetApplicationWorkflowListUseCase>(),
        dialogService: Get.find<DialogService>(),
        getUserRoleUseCaseUseCase: Get.find<GetUserRoleUseCaseUseCase>(),
        workflowActionService: Get.find<WorkflowActionService>(),
        navigationService: Get.find<NavigationService>(),
      ),
    );
  }
}
