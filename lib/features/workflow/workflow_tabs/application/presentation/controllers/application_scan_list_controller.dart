import 'dart:convert';

import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_mixin.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/usecases/workflow_get_layout_settings_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/state/workflow_scan_list_state.dart';
import 'package:get/get.dart';
import 'package:asset_force_mobile_v2/core/presentation/app_keyboard_manager.dart';

class ApplicationScanList<PERSON>ontroller extends WorkflowCommonController with OverlayMixin {
  final WorkflowGetLayoutSettingsUseCase workflowGetLayoutSettingsUseCase;
  final GetTurlUseCase getTurlUseCase;
  ApplicationScanListController({
    required super.dialogService,
    required super.getUserRoleUseCaseUseCase,
    required super.workflowActionService,
    required this.workflowGetLayoutSettingsUseCase,
    required this.getTurlUseCase,
  });

  var loadStatus = false.obs;

  /// 页码
  int nextPageNum = 1;

  /// 每次后台返回多少条数据
  final getAmountDataNumber = 20;

  final state = WorkflowScanListState();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    AppKeyboardManager().clearCurrentPageActions();
    _initDataFromArgs();
  }

  void _initDataFromArgs() async {
    final params = Get.arguments;
    state.workflowId = params['workflowId'];
    state.hasScanTask.value = params['hasScanTask'] ?? true;
    state.processDefinitionId = params['processDefinitionId'] ?? '';
    state.assetTypeId = (params['assetTypeIdWithFirstWf'] ?? '').toString();
    state.processInstanceId = params['processInstanceId'] ?? '';
    state.taskId = params['taskId'] ?? '';

    state.isUpdateAmount = params['isUpdateAmount'] ?? '';
    state.isCountingType.value = params['isCountingType'] ?? false;
    state.workflowScript = params['workflowScript'] ?? '';
    await assetListPagePaginationTarget();
  }

  Future<void> doRefresh() async {
    state.filterListTemp.clear();
    nextPageNum = 1;
    await assetListPagePaginationTarget();
  }

  Future<void> onLoadMoreData() async {
    nextPageNum += 1;
    await assetListPagePaginationTarget();
  }

  void doSearch(String searchWorld) async {
    state.filterListTemp.clear();
    if (searchWorld.isNotEmpty) {
      state.searchText = searchWorld;
    }
    nextPageNum = 1;
    await assetListPagePaginationTarget();
  }

  /// 入力した検索キーを削除
  void clearInputKey() async {
    state.filterListTemp.clear();
    state.searchText = '';
    nextPageNum = 1;
    await assetListPagePaginationTarget();
  }

  /// 分页资产列表数据组装
  Future<void> assetListPagePaginationTarget() async {
    loadStatus.value = true;
    try {
      final nextPage = nextPageNum - 1;
      // skip 从（开始条数）0或者16是从  例：0～15 16～32 公式：nextPage x getAmountDataNumber
      final skip = nextPage * getAmountDataNumber;
      final getAssetListData = await workflowActionService.getAssetsByKeywordInWfAssetListForAssignScan(
        state.processInstanceId,
        state.taskId,
        state.searchText,
        skip,
        getAmountDataNumber,
      );
      state.totalCount.value = getAssetListData.allAssetCount ?? 0;
      state.assetsCount.value = getAssetListData.allAssetCount ?? 0;

      final wfAssetList = getAssetListData.assetListDatas ?? [];
      if (nextPage == 0) {
        state.filterListTemp.value = wfAssetList;
      } else {
        state.filterListTemp.addAll(wfAssetList);
      }
      await dealData();
      state.moreThenLimit.value = !(wfAssetList.length != 0 && wfAssetList.length % getAmountDataNumber == 0);
    } finally {
      loadStatus.value = false;
    }
  }

  Future<void> dealData() async {
    try {
      final result = await workflowGetLayoutSettingsUseCase(state.assetTypeId);
      for (final item in state.filterListTemp) {
        if (item.assetText is String) {
          final assetText = jsonDecode(item.assetText);
          item.assetText = assetText;
        }
      }
      if (result.code == 0) {
        final imageTypeArray =
            result.assetItemList?.where((item) {
              return item?.itemType == 'image' && item?.mobileFlg == '1';
            }).toList() ??
            [];
        for (var iterator in state.filterListTemp) {
          for (var it2 in imageTypeArray) {
            final itemName = it2?.itemName;
            if (itemName == null) continue;

            var assetTextElement = iterator.assetText[itemName];
            if (assetTextElement == null) continue;

            // 修改处理assetText为字符串的情况
            if (assetTextElement is String && assetTextElement.isNotEmpty) {
              assetTextElement = jsonDecode(assetTextElement);
            }

            if (assetTextElement is List) {
              // 查找主图片
              final homeImage = assetTextElement.firstWhere(
                (item) => item is Map && item['isHomeImage'] == true,
                orElse: () => null,
              );

              if (homeImage != null) {
                // 过滤掉没有权限预览的home画像
                bool isView = true;
                final optionObj = jsonDecode(it2?.option ?? '{}');
                final sectionPrivateGroups = optionObj['sectionPrivateGroups'];

                if (sectionPrivateGroups != null) {
                  isView = await checkIsView(sectionPrivateGroups);
                }

                if (!isView) {
                  // 没有权限预览
                  continue;
                }

                final timeStart = DateTime.now().millisecondsSinceEpoch;
                final url = homeImage['url'];
                if (url != null) {
                  homeImage['turl'] = await getTurlUseCase(url);
                  LogUtil.d('blog--获取turl耗时：${DateTime.now()}-${timeStart}');
                  iterator.homeImage = HomeImageModel.fromJson(homeImage);
                  break;
                }
              }
            }
          }
        }
      }
    } catch (e) {
      LogUtil.d('scanList deal data Error:$e');
    }
  }
}
