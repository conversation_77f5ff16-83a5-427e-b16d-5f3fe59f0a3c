import 'dart:convert';

import 'package:asset_force_mobile_v2/core/env/env_helper.dart';
import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/list_selector/domain/models/list_selector_params.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_ai_ocr_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/workflow_preview_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/state/new_application_form_ui_state.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_task_form_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_scan_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/usecases/workflow_get_permission_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ApplicationViewController extends WorkflowCommonController {
  final NavigationService navigationService;
  final WorkflowCommonScanService workflowCommonScanService;
  final WorkflowGetPermissionUseCase workflowGetPermissionUseCase;
  ApplicationViewController({
    required super.dialogService,
    required super.getUserRoleUseCaseUseCase,
    required super.workflowActionService,
    required this.workflowCommonScanService,
    required this.navigationService,
    required this.workflowGetPermissionUseCase,
  }) {
    final pageParams = Get.arguments;
    if (pageParams != null) {
      state.workflowName.value = pageParams['workflowName'] ?? '';
    }
  }

  final state = NewApplicationFormUiState();

  @override
  void onReady() async {
    super.onReady();
    if (state.firstLoad) {
      await _initStateFormArgs();
      state.firstLoad = false;
    }
    // 每次页面显示时都会触发
    loadDataDidEnter();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    if (Get.isRegistered<WorkflowPreviewController>()) {
      Get.delete<WorkflowPreviewController>();
    }
    super.onClose();
  }

  Future<void> _initStateFormArgs() async {
    final params = Get.arguments;
    if (params != null) {
      final type = await StorageUtils.get<String>(StorageUtils.keyFontSize);
      state.isBigFont.value = type == null ? false : type == 'big';
      state.hasAssetTypeName.value = params['firstWorkflowAssetTypeName'] != null ? true : false;
      state.taskId = params['taskId'] ?? '';
      state.stepName.value = params['stepName'] ?? '';
      state.originalAssetTypeName = params['assetTypeName'] ?? '';
      state.processInstanceId = params['processInstanceId'] ?? '';
      state.processDefinitionId = params['processDefinitionId'] ?? '';
      state.assetTypeIdWithFirstWf = params['assetTypeIdWithFirstWf'] ?? '';
      state.workflowId = params['workflowId'];
      state.wfState.value = params['state'] ?? '';
      state.isSendBack.value = params['isSendBack'] ?? false;
    }
    await loadData(state.processInstanceId, state.taskId);

    state.isAllowTalk.value = await workflowGetPermissionUseCase(389);
    final accessToken = await StorageUtils.get<String>(StorageUtils.keyToken);
    final zoneId = await StorageUtils.get<String>(StorageUtils.keyZoneId);
    final baseLiveUrl = EnvHelper.getLiveBaseUrl();
    state.liveTalkListUrl.value =
        '$baseLiveUrl/af-integrate/room-list/workflow/${state.processInstanceId}?accessToken=${accessToken}&zoneId=${zoneId}';
  }

  /// 每次进页面
  void loadDataDidEnter() async {
    state.isFromNew = (state.wfState.value == '作成中');
    if (state.processInstanceId.isNotEmpty && state.taskId.isNotEmpty && !state.isFromNew) {
      await assetListPagePaginationTarget();
    }
  }

  Future<void> loadData(String processInstanceId, String taskId) async {
    await showLoading();
    try {
      final result = await workflowActionService.getWorkflowTaskForm(processInstanceId, taskId);
      state.permissions.value = result['permissions'];
      final resultData = WorkflowTaskFormResponseModel.fromJson(result['resultData']);
      state.taskType.value = resultData.taskType ?? '';
      state.isMyselfInputTask = resultData.actions?.isMyselfInputTask ?? false;
      // state.cancelStatus = resultData.actions!.cancelStatus;
      state.isPermission.value = resultData.actions?.isPermission ?? false;
      state.dynamicTantousha.value = [];

      final cjs = resultData.commonJS ?? '';
      // 如果js从后台取得后不为空那么才把commonJS拼接到js
      if (resultData.workflowLogicScript != null && resultData.workflowLogicScript!.isNotEmpty) {
        state.jsString = cjs + resultData.workflowLogicScript!;
      }
      if (resultData.workflowScript != null && resultData.workflowScript!.isNotEmpty) {
        state.workflowScript = cjs + resultData.workflowScript!;
      }
      bool isApproval = false;
      if (state.taskType.value == 'USER' || state.taskType.value == 'GROUP' || state.taskType.value == 'SCAN') {
        isApproval = true;
      }
      state.isCountingType.value = resultData.updateAmountShow ?? false;
      state.hasScanTask = resultData.hasScanTask ?? true;
      state.isFirstWfWithAssetList = resultData.isFirstWfWithAssetList ?? false;
      state.workflowState.value = resultData.workflowState ?? '';
      state.inputAssetListFlag.value = resultData.inputAssetListFlag ?? '';
      state.assignDynamicFlag = resultData.assignDynamicFlag ?? false;
      final action = resultData.actions;
      // 底部按钮相关
      state.footerBtn['showCancel'] = action?.cancelStatus ?? false;
      final rejectionStatus = (action?.rejectionStatus ?? '0') == '1';
      final sendBackStatus = (action?.sendBackStatus ?? '0') == '1';
      state.footerBtn['showLineTwo'] = (action?.isPermission ?? false) && rejectionStatus && sendBackStatus;

      state.rawFormData = resultData;
      state.autoFetchSearchId = resultData.autoFetchSearchId;
      //新规申请未提出
      //一時保存WFを再編集する場合
      if (state.wfState == '作成中') {
        if (state.assignDynamicFlag && resultData.wfAssignDynamicTasks != null) {
          // 获取需要在当前task指定担当者的task列表
          state.dynamicTaskInfo.value = resultData.wfAssignDynamicTasks!;
          for (final dynamicTaskInfoElement in state.dynamicTaskInfo) {
            if (dynamicTaskInfoElement.authorizerId != null) {
              if (dynamicTaskInfoElement.assignDynamicType == 'user') {
                //  user
                state.dynamicTantousha.add({
                  'userId': dynamicTaskInfoElement.authorizerId,
                  'lastName': dynamicTaskInfoElement.authorizerName,
                  'firstName': '',
                });
              } else {
                //  group
                state.dynamicTantousha.add({
                  'roleId': dynamicTaskInfoElement.authorizerId,
                  'roleName': dynamicTaskInfoElement.authorizerName,
                });
              }
            }
          }
        }
        state.isHasTantoushaF.value = state.dynamicTaskInfo.length > 0 && state.isPermission.value;
      } else {
        await getDynamicTaskInfo();
      }
      state.isUpdateAmount = resultData.workflowEngineAmountProcess?.isUpdateAmount;
      if ((state.rawFormData?.hasSubProcess ?? false) &&
          state.rawFormData?.productAssetTypeName != null &&
          state.rawFormData?.productAssetTypeName != '') {
        state.assetTypeName.value = state.rawFormData!.productAssetTypeName!;
      } else {
        state.assetTypeName.value = state.originalAssetTypeName;
      }
      state.amountActionTaskUpdateItemId = resultData.workflowEngineAmountProcess?.amountActionTaskUpdateItemId;
      state.amountActionTaskUpdateItemName = resultData.workflowEngineAmountProcess?.amountActionTaskUpdateItemName;
      state.sendBackDestination = action?.sendBackDestination ?? '';
      state.sendBackStatus.value = action?.sendBackStatus ?? '';
      if (result['commentData'] != null && result['commentData'] != '') {
        final decodedData = jsonDecode(result['commentData']);
        if (decodedData is List) {
          state.comments.value = decodedData.where((e) => e != null).map((e) {
            if (e is Map<String, dynamic>) {
              return CommentsModel.fromJson(e);
            }
            throw BusinessException('Invalid comment data format');
          }).toList();
        } else {
          state.comments.value = [];
        }
      }
      state.rawComment = jsonEncode(state.comments);
      state.rejectionStatus.value = action?.rejectionStatus ?? '0';
      state.showClamin.value = action?.possibleOfClaim ?? false;
      final status = getButtonStatus(resultData);
      state.showButton.value = status;
      state.isNextEnable.value = isNextEnable;
      final workflowButtonNameDic = resultData.workflowButtonName;
      final extractedButtonResult = extractWFApprovalOrApplicationRaiseButtons(workflowButtonNameDic, isApproval);
      state.buttonNameList.value = extractedButtonResult.raiseButtonList;
      state.turnBackButtonInfo = extractedButtonResult.turnBackButtonDic;
      state.vetoButtonInfo = extractedButtonResult.vetoButtonDic;
      // 取按钮数量
      state.buttonNum.value = numberButtonsFooterSection;
      state.assetDict.value = result['sectionDic'];

      state.previewPageLoadComplete.value = () {
        hideLoading();
      };
    } catch (e) {
      hideLoading();
    }
  }

  /// 分页资产列表数据组装
  Future<void> assetListPagePaginationTarget() async {
    // await showLoading();
    try {
      final data = await workflowActionService.getAssetsByKeywordInWfAssetListForAssignScan(
        state.processInstanceId,
        state.taskId,
        '',
        0,
        0,
      );
      final dataCount = data.allAssetCount ?? 0;
      state.dataCount.value = dataCount.toString();
    } finally {
      // hideLoading();
    }
  }

  void back() async {
    try {
      final result = await isRestoreAllAssets();
      final isChangedAsset = state.editAssetList.isEmpty;
      if (!result && isChangedAsset || didFormChanged) {
        if (Get.isRegistered<TabsController>()) {
          Get.find<TabsController>().showBar();
        }
        navigationService.goBack(id: SharedNavBarEnum.workflow.navigatorId);
        return;
      }
    } catch (e) {
      if (Get.isRegistered<TabsController>()) {
        Get.find<TabsController>().showBar();
      }
      navigationService.goBack(id: SharedNavBarEnum.workflow.navigatorId);
      return;
    }

    Future<void> backOkayFunAtUserTask() async {
      try {
        // 当用户操作了或者扫描了资产才去调用重置API，否则直接返回到上一个页面
        final formData = <String, dynamic>{};
        formData['processInstanceId'] = state.processInstanceId;
        formData['taskId'] = state.taskId;
        formData['assetListDataJson'] = jsonEncode(state.editAssetList);
        final result = await isRestoreAllAssets();
        if (result) {
          final assetList = await StorageUtils.get(StorageUtils.keyTempEditAssetList);
          formData['clearedAssetListDataJson'] = assetList;
        } else {
          formData['clearedAssetListDataJson'] = jsonEncode([]);
        }
        await workflowActionService.restoreAssetScanState(formData);
      } finally {
        if (Get.isRegistered<TabsController>()) {
          Get.find<TabsController>().showBar();
        }
        navigationService.goBack(
          // id: NavBarEnum.workflow.navigatorId,
        );
        return;
      }
    }

    dialogService.show(
      content: '保存せずに終了しますか？入力したデータ・及びスキャンデータは破棄されます。',
      cancelText: 'キャンセル',
      confirmText: 'OK',
      onConfirm: () async {
        await backOkayFunAtUserTask();
      },
    );
  }

  /// 資産一覧画面に遷移する
  void toAssetListPage() async {
    if (isAssetListEditable()) {
      if (int.parse(state.dataCount.value) < 1) {
        dialogService.showToast('参照可能な資産がありません。\n「次へ」を押してスキャンに進めてください');
        return;
      }
      final navigationExtras = {
        'registeredAssetList': [],
        'assetDict': state.assetDict,
        'isUpdateAmount': state.isUpdateAmount,
        'isCountingType': state.isCountingType.value,
        'assetTypeId': state.assetTypeIdWithFirstWf.toString(),
        'assetTypeName': state.assetTypeName.value,
        'taskName': state.stepName.value,
        'workflowName': state.workflowName.value,
        'stepName': state.stepName.value,
        'processDefinitionId': state.processDefinitionId,
        'processInstanceId': state.processInstanceId,
        'taskId': state.taskId,
        'isUserTask': true,
        'workflowId': state.workflowId,
        'comments': state.comments,
        'workflowRaiseButtonNameList': state.buttonNameList,
        'dynamicTaskInfo': state.dynamicTaskInfo,
        'dynamicTantousha': state.dynamicTantousha,
        'workflowScript': state.workflowScript,
        'isFromNew': state.isFromNew,
        'fromPage': AutoRoutes.applicationView,
      };
      final result = await navigationService.navigateTo(
        AutoRoutes.workflowScanList,
        arguments: navigationExtras,
        id: SharedNavBarEnum.workflow.navigatorId,
      );
      state.editAssetList = result?['editAssetList'] ?? [];
      state.dataCount.value = (result?['assetCount'] ?? 0).toString();
      return;
    }
    if (int.parse(state.dataCount.value) < 1) {
      state.isPermission.value
          ? dialogService.showToast('参照可能な資産がありません。\n「次へ」を押してスキャンに進めてください')
          : dialogService.showToast('参照可能な資産がありません。');
      return;
    } else {
      final queryParams = {
        'isEdit': false,
        'taskType': state.taskType.value,
        'assetList': [],
        'isUpdateAmount': state.isUpdateAmount,
        'amountActionTaskUpdateItemName': state.amountActionTaskUpdateItemName,
        'rawFormData': state.rawFormData,
        'permissions': state.permissions,
        'isCountingType': state.isCountingType.value,
        'hasScanTask': state.hasScanTask,
        'workflowState': state.workflowState.value,
        'showClamin': state.showClamin,
        'workflowId': state.workflowId,
        'processDefinitionId': state.processDefinitionId,
        'assetTypeIdWithFirstWf': state.assetTypeIdWithFirstWf,
        'stepName': state.stepName.value,
        'processInstanceId': state.processInstanceId,
        'taskId': state.taskId,
        'isMyselfInputTask': state.isMyselfInputTask,
      };
      navigationService.navigateTo(
        AutoRoutes.applicationScanList,
        arguments: queryParams,
        id: SharedNavBarEnum.workflow.navigatorId,
      );
    }
  }

  /// 一時保存
  void saveForApplication() async {
    bool isCheck = false;
    try {
      isCheck = await checkAndGetData(state.assetDict);
    } finally {}
    if (!isCheck) return;
    state.processDefinitionId = await StorageUtils.get(StorageUtils.keyWfPdID);
    saveWorkflowData(state.assetDict, state.comments, state.processDefinitionId, true, '');
  }

  /// 一時保存
  void startSaveTemporary() {
    baseAction('一時保存', false, state.assetDict, () async {
      await showLoading();
      try {
        final formData = getFormData(state.assetDict, state.comments);
        formData['processInstanceId'] = state.processInstanceId;
        formData['status'] = 'update';
        await workflowActionService.workflowStartSaveTemporary(formData);
        // 重置flag和备份用的资产list
        workflowActionService.clearLocalData();
      } finally {
        hideLoading();
      }
      setNavigateBack('一時保存', id: SharedNavBarEnum.workflow.navigatorId);
    }, () async {});
  }

  void next() async {
    if (!state.hasAssetTypeName.value) {
      activeApply(null);
      return;
    }
    // 資産入力必要ではない、かつ、担当者の指定必要場合、担当者画面に遷移する
    if (state.inputAssetListFlag.value == '0' && state.isHasTantoushaF.value) {
      final params = {
        'isNoAsset': true,
        'processDefinitionId': state.processDefinitionId,
        'dynamicTaskInfo': state.dynamicTaskInfo,
      };
      final result = await navigationService.navigateTo(AutoRoutes.chooseTantousha, arguments: params);
      if (result != null) {
        state.dynamicTantousha.value = result['dynamicTantousha'] ?? [];
        state.isSubmit.value = result['isSubmit'] ?? false;
        state.isNextEnable.value = isNextEnable;
      }
      return;
    }
    try {
      await showLoading();
      final isCheck = await checkAndGetData(state.assetDict);
      if (!isCheck) {
        return;
      }
    } finally {
      hideLoading();
    }
    final dataScan = WorkflowScanBarcodeData(
      assetTypeId: state.assetTypeIdWithFirstWf.toString(),
      locationInfo: state.locationInfo,
      jobType: 'createNewWF',
      isFromNew: state.isFromNew,
      scannedAssetIdList: [], // assetID(同じbarcode、違う場所)
      isCountingType: state.isUpdateAmount == '1' ? false : state.isCountingType.value,
      searchId: state.autoFetchSearchId,
      workflowId: state.workflowId,
      engineId: state.processDefinitionId,
      scannedAssetCount: 0,
      processInstanceId: state.processInstanceId,
      editAssetList: [],
      taskId: state.taskId,
      whatKindWF: NewApplicationWorkflow.newW1FormPage,
      state: null, // どちらの一覧画面から： 0:申請； 1:承認； 2:全て；
      scanType: null,
      isFromLocationSettingPage: false,
      assetTypeName: state.assetTypeName.value,
      taskName: state.stepName.value,
      workflowName: state.workflowName.value,
    );
    void goToNextPage(List<dynamic> registeredAssetList, ScanType scanType) {
      final navigationExtras = {
        'registeredAssetList': registeredAssetList,
        'scanType': scanType,
        'assetDict': state.assetDict,
        'isUpdateAmount': state.isUpdateAmount,
        'isCountingType': state.isCountingType.value,
        'assetTypeId': state.assetTypeIdWithFirstWf.toString(),
        'assetTypeName': state.assetTypeName.value,
        'taskName': state.stepName.value,
        'workflowName': state.workflowName.value,
        'stepName': state.stepName.value,
        'processDefinitionId': state.processDefinitionId,
        'taskId': state.taskId,
        'workflowId': state.workflowId,
        'comments': state.comments,
        'workflowRaiseButtonNameList': state.buttonNameList,
        'dynamicTaskInfo': state.dynamicTaskInfo,
        'dynamicTantousha': state.dynamicTantousha,
        'workflowScript': state.workflowScript,
        'isFromNew': false,
        'fromPage': AutoRoutes.applicationView,
      };
      navigationService.navigateTo(
        AutoRoutes.workflowScanList,
        id: SharedNavBarEnum.workflow.navigatorId,
        arguments: navigationExtras,
      );
    }

    workflowCommonScanService.startWorkflowScanBarcode(dataScan, (returnValueAfterScan) {
      goToNextPage(returnValueAfterScan['registeredAssetList'], returnValueAfterScan['scanType']);
    });
  }

  void activeApply(WorkflowRaiseButtonModel? raiseDic) async {
    if (!tantoushaVisible && state.isHasTantoushaF.value) {
      final params = {
        'isNoAsset': true,
        'processDefinitionId': state.processDefinitionId,
        'dynamicTaskInfo': state.dynamicTaskInfo,
      };
      final result = await navigationService.navigateTo(AutoRoutes.chooseTantousha, arguments: params);
      if (result != null) {
        state.dynamicTantousha.value = result['dynamicTantousha'] ?? [];
        state.isSubmit.value = result['isSubmit'] ?? false;
        state.isNextEnable.value = isNextEnable;
      }
      return;
    }

    try {
      await showLoading();
      final isCheck = await checkAndGetData(state.assetDict);
      if (!isCheck) return;
    } finally {
      hideLoading();
    }
    // 提交时用来判断
    if (!checkDynamicTaskCanSubmit(state.dynamicTaskInfo, state.dynamicTantousha)) {
      dialogService.show(content: '各ステップの承認者を選択してください。', cancelText: 'はい');
      return;
    } else {
      if (state.dynamicTaskInfo.isNotEmpty && state.dynamicTantousha.isNotEmpty) {
        for (var i = 0; i < state.dynamicTaskInfo.length; i++) {
          final element = state.dynamicTaskInfo[i];
          if (element.assignDynamicType == 'group') {
            element.authorizerId = state.dynamicTantousha[i]['roleId'];
            element.authorizerName = state.dynamicTantousha[i]['roleName'];
          } else {
            element.authorizerId = state.dynamicTantousha[i]['userId'];
            element.authorizerName =
                state.dynamicTantousha[i]['lastName'] + ' ' + state.dynamicTantousha[i]['firstName'];
          }
        }
      }
    }
    activeApplyWithout(
      state.assetDict,
      state.comments,
      state.processInstanceId,
      state.dynamicTaskInfo,
      state.isMyselfInputTask,
      raiseDic,
      state.stepName.value,
      state.workflowScript,
    );
  }

  void applyForApplication(WorkflowRaiseButtonModel? raiseDic) async {
    if (state.hasAssetTypeName.value) {
      if (!tantoushaVisible && state.isHasTantoushaF.value) {
        final params = {
          'isNoAsset': true,
          'processDefinitionId': state.processDefinitionId,
          'dynamicTaskInfo': state.dynamicTaskInfo,
        };
        final result = await navigationService.navigateTo(AutoRoutes.chooseTantousha, arguments: params);
        if (result != null) {
          state.dynamicTantousha.value = result['dynamicTantousha'] ?? [];
          state.isSubmit.value = result['isSubmit'] ?? false;
          state.isNextEnable.value = isNextEnable;
        }
        return;
      }
    }

    bool isCheck = false;
    isCheck = await checkAndGetData(state.assetDict);
    if (!isCheck) return;
    // 提交时用来判断
    if (!checkDynamicTaskCanSubmit(state.dynamicTantousha, state.dynamicTaskInfo)) {
      dialogService.show(content: '各ステップの承認者を選択してください。', confirmText: 'はい');
      return;
    } else {
      if (state.dynamicTaskInfo.isNotEmpty && state.dynamicTantousha.isNotEmpty) {
        for (var i = 0; i < state.dynamicTaskInfo.length; i++) {
          final element = state.dynamicTaskInfo[i];
          if (element.assignDynamicType == 'group') {
            element.authorizerId = state.dynamicTantousha[i]['roleId'];
            element.authorizerName = state.dynamicTantousha[i]['roleName'];
          } else {
            element.authorizerId = state.dynamicTantousha[i]['userId'];
            element.authorizerName =
                state.dynamicTantousha[i]['lastName'] + ' ' + state.dynamicTantousha[i]['firstName'];
          }
        }
      }
    }
    await applyWithList(
      state.assetDict,
      state.comments,
      state.processDefinitionId,
      state.processInstanceId,
      [], // todo registeredAssetList: [], ionic 这个地方标注需要特别确认一下。
      state.workflowName.value,
      state.assetTypeIdWithFirstWf.toString(),
      false,
      state.taskId,
      state.assetListId,
      state.isCountingType.value,
      state.dynamicTaskInfo,
      false,
      state.isMyselfInputTask,
      raiseDic,
      state.actionType,
      state.stepName.value,
      state.workflowScript,
    );
  }

  void approvalButton(WorkflowRaiseButtonModel? raiseDic) async {
    try {
      await showLoading();
      final isCheck = await checkAndGetData(state.assetDict);
      if (!isCheck) return;
    } finally {
      hideLoading();
    }
    // 提交时用来判断
    if (!checkDynamicTaskCanSubmit(state.dynamicTaskInfo, state.dynamicTantousha)) {
      dialogService.show(content: '各ステップの承認者を選択してください。', cancelText: 'はい');
      return;
    } else {
      if (state.dynamicTaskInfo.isNotEmpty && state.dynamicTantousha.isNotEmpty) {
        for (var i = 0; i < state.dynamicTaskInfo.length; i++) {
          final element = state.dynamicTaskInfo[i];
          if (element.assignDynamicType == 'group') {
            element.authorizerId = state.dynamicTantousha[i]['roleId'];
            element.authorizerName = state.dynamicTantousha[i]['roleName'];
          } else {
            element.authorizerId = state.dynamicTantousha[i]['userId'];
            element.authorizerName =
                state.dynamicTantousha[i]['lastName'] + ' ' + state.dynamicTantousha[i]['firstName'];
          }
        }
      }
    }
    //申请时的承认操作
    approvalWithList(
      state.assetDict,
      state.comments,
      state.processInstanceId,
      state.dataCount.value,
      state.taskId,
      state.dynamicTaskInfo,
      state.stepName.value,
      state.workflowScript,
      isAssetListEditable(),
      raiseDic,
    );
  }

  // 否决
  void rejectionButton(String buttonName) async {
    if (state.isUpdateAmount == '1') {
    } else if (state.isUpdateAmount == '0') {
      try {
        await showLoading();
        final isCheck = await checkAndGetData(state.assetDict);
        if (!isCheck) return;
      } finally {
        hideLoading();
      }
      rejectionWithList(
        state.assetDict,
        state.comments,
        state.processInstanceId,
        [],
        state.assetListTitle,
        state.assetTypeIdWithFirstWf,
        state.taskId,
        state.workflowScript,
        state.stepName.value,
        approvalBtnDisabled,
        buttonName,
      );
    }
  }

  void chooseTantousha(int index) async {
    if (state.dynamicTaskInfo.length > index) {
      final params = ListSelectorParams(
        index: 0,
        type: state.dynamicTaskInfo[index].assignDynamicType,
        dynamicLeader: {
          'procDefId': state.dynamicTaskInfo[index].procDefId,
          'taskDefKey': state.dynamicTaskInfo[index].taskDefKey,
        },
        isFromPage: 'choose-tantousha-view',
        autoFetchSearchId: state.autoFetchSearchId,
        searchConditionDataList: [],
        title: '',
        items: [],
      );
      final result = await navigationService.navigateTo(AutoRoutes.userGroupSelector, arguments: params);
      if (result != null && result['info'] != null) {
        if (index >= 0 && index < state.dynamicTantousha.length) {
          state.dynamicTantousha[index] = result['info'];
        } else {
          state.dynamicTantousha.add(result['info']);
        }
      }
    }
  }

  void sendBack(String buttonName) async {
    try {
      await showLoading();
      final isCheck = await checkAndGetData(state.assetDict);
      if (!isCheck) return;
    } finally {
      hideLoading();
    }
    workflowSendBack(
      state.assetDict,
      state.comments,
      state.sendBackDestination,
      state.taskId,
      state.workflowScript,
      state.stepName.value,
      approvalBtnDisabled,
      buttonName,
      backTabIndex: 1,
    );
  }

  void approvalBtnDisabled(bool result) {
    state.isDisabled.value = result;
  }

  void cancel() async {
    baseAction('取り消し', false, state.assetDict, () async {
      try {
        await showLoading();
        await workflowActionService.workflowCancel(state.processInstanceId);
        // 重置flag和备份用的资产list
        StorageUtils.set(StorageUtils.keyIsDeleteAllAssetAtBefore, 0); // 重置资产状态后flag也要重置一下
        StorageUtils.set(StorageUtils.keyTempEditAssetList, '[]'); // 重置资产状态后备份用的资产list也要重置一下
      } finally {
        hideLoading();
      }
      setNavigateBack('取り消し', id: SharedNavBarEnum.workflow.navigatorId);
    }, () async {});
  }

  /// 普通のWFの否決
  void rejectionWithListButton(String buttonName) async {
    final isCheck = await checkAndGetData(state.assetDict);
    if (!isCheck) return;
    rejectionWithList(
      state.assetDict,
      state.comments,
      state.processInstanceId,
      [],
      state.assetListTitle,
      state.assetTypeIdWithFirstWf ?? 0,
      state.taskId,
      state.workflowScript,
      state.stepName.value,
      approvalBtnDisabled,
      buttonName,
    );
  }

  /// 舞浜の否決
  void rejectionWithMaihamaList(String buttonName) {}

  bool get isNextEnable {
    if (state.hasAssetTypeName.value) {
      return state.isHasTantoushaF.value || state.inputAssetListFlag.value == '1';
    } else {
      return !getSubmitLableName;
    }
  }

  /// 是否修改过入力情报
  bool get didFormChanged {
    if (state.assetDict.isEmpty && state.comments.isEmpty) {
      throw SystemException(message: 'Basic data is empty');
    }
    if (state.rawWfDict.isEmpty && state.rawComment.isEmpty) {
      return false;
    }
    final Map<String, dynamic> decodedRawDict = jsonDecode(state.rawWfDict);
    final List<CommentsModel> decodedRawComment = jsonDecode(state.rawComment);
    final originalForm = setOriginalForm(state.assetDict, state.comments);
    final Map<String, List<AssetItemListModel>> typedRawAssetDict = {};
    try {
      decodedRawDict.forEach((key, value) {
        if (value is List) {
          typedRawAssetDict[key] = value
              .map((itemJson) => AssetItemListModel.fromJson(itemJson as Map<String, dynamic>))
              .toList();
        } else {
          typedRawAssetDict[key] = [];
        }
      });
    } catch (e) {
      throw SystemException(message: '入力情报比对转换错误: $e');
    }

    final rawItemData = setOriginalForm(typedRawAssetDict, decodedRawComment);
    final isFormChanged = originalForm == rawItemData;
    return isFormChanged;
  }

  String getTantoushaInfoWithIndex(int index) {
    final dynamicTaskInfo = state.dynamicTaskInfo;
    final dynamicTantousha = state.dynamicTantousha;
    if (dynamicTaskInfo.length > 0 && dynamicTantousha.length == dynamicTaskInfo.length) {
      final chooseType = dynamicTaskInfo[index].assignDynamicType;
      final datum = dynamicTantousha[index] ?? null;
      if (datum != null) {
        if (chooseType == 'group') {
          return datum['roleName'];
        } else {
          return datum['lastName'] + ' ' + datum['firstName'];
        }
      } else {
        return '未選択';
      }
    }
    return '未選択';
  }

  /// 資産リスト編集できるか
  bool isAssetListEditable() {
    return (state.isFirstWfWithAssetList &&
        state.inputAssetListFlag.value == '1' &&
        state.taskType.value == 'USER' &&
        state.isPermission.value);
  }

  Future<void> getDynamicTaskInfo() async {
    final result = await workflowActionService.getDynamicTaskInfo(state.processDefinitionId);
    state.dynamicTaskInfo.assignAll(result);
    state.isHasTantoushaF.value = state.dynamicTaskInfo.length > 0 && state.isPermission.value;
    for (final dynamicTaskInfoElement in state.dynamicTaskInfo) {
      if (dynamicTaskInfoElement.authorizerId != null) {
        if (dynamicTaskInfoElement.assignDynamicType == 'user') {
          //  user
          state.dynamicTantousha.add({
            'userId': dynamicTaskInfoElement.authorizerId,
            'lastName': dynamicTaskInfoElement.authorizerName,
            'firstName': '',
          });
        } else {
          //  group
          state.dynamicTantousha.add({
            'roleId': dynamicTaskInfoElement.authorizerId,
            'roleName': dynamicTaskInfoElement.authorizerName,
          });
        }
      }
    }
  }

  String setOriginalForm(Map<String, List<AssetItemListModel>> assetDict, List<CommentsModel> comments) {
    final Map<String, dynamic> formData = getFormData(assetDict, comments, fromOriginCompare: true);
    final String jsonString = jsonEncode(formData);
    return jsonString;
  }

  /// 确认是否进行了一扩删除动作
  Future<bool> isRestoreAllAssets() async {
    final assetList = await StorageUtils.get<String>(StorageUtils.keyTempEditAssetList);
    return assetList != null && assetList != '[]' ? true : false;
  }

  /// 判断是否是提交
  bool get tantoushaVisible {
    final isSendBackOrCreateNew = state.isMyselfInputTask || state.wfState.value == '作成中';
    return (state.isSubmit.value && state.isHasTantoushaF.value) ||
        (!state.isMyselfInputTask && state.isPermission.value && !isSendBackOrCreateNew);
  }

  bool get getSubmitLableName {
    if (state.isHasTantoushaF.value)
      return tantoushaVisible ? false : true;
    else
      return false;
  }

  int get numberButtonsFooterSection {
    final raiseButtonList = state.buttonNameList;
    final turnBackButtonDic = state.turnBackButtonInfo;
    final vetoButtonDic = state.vetoButtonInfo;

    int allButtonNum = 0;

    if ((state.taskType == 'USER' || state.taskType == 'GROUP') && state.isPermission.value) {
      // 取消
      if (state.footerBtn['showCancel'] ?? false) {
        allButtonNum = allButtonNum + 1;
      }
      // 差し戻し
      if (turnBackButtonDic != null && state.sendBackStatus == '1') {
        allButtonNum = allButtonNum + 1;
      }
      // 否决
      if (vetoButtonDic != null && state.rejectionStatus == '1') {
        allButtonNum = allButtonNum + 1;
      }

      if (state.taskType == 'GROUP') {
        allButtonNum = allButtonNum + 1;
      } else {
        // 承认或者提出按钮总数
        allButtonNum = allButtonNum + raiseButtonList.length;
      }
      return allButtonNum;
    }

    if (state.taskType == 'INPUT' || state.taskType == 'SUSPENDED') {
      // 取消
      if ((state.footerBtn['showCancel'] ?? false) || state.taskType == 'SUSPENDED') {
        allButtonNum = allButtonNum + 1;
      }
      // 一時保存
      if (!state.isSendBack.value || state.taskType == 'SUSPENDED') {
        allButtonNum = allButtonNum + 1;
      }
      // 次へ
      if (state.inputAssetListFlag == '1' || state.isHasTantoushaF.value) {
        allButtonNum = allButtonNum + 1;
      } else {
        // 承认或者提出按钮总数
        allButtonNum = allButtonNum + raiseButtonList.length;
        return allButtonNum;
      }
    }

    if (state.taskType == 'SCAN') {
      // 取消
      if (state.footerBtn['showCancel'] ?? false) {
        allButtonNum = allButtonNum + 1;
      }
      // 差戻し
      if (state.sendBackStatus == '1' && state.isPermission.value) {
        allButtonNum = allButtonNum + 1;
      }
      return allButtonNum;
    }

    return allButtonNum;
  }

  /// その他
  void otherButtonSwitchOpenModal(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        CupertinoActionSheetAction _buildActionSheetAction(BuildContext context, WorkflowRaiseButtonModel raiseDic) {
          return CupertinoActionSheetAction(
            isDefaultAction: true,
            onPressed: () {
              Navigator.pop(context);
              apply(
                state.assetDict,
                state.comments,
                state.processDefinitionId,
                state.workflowScript,
                state.dynamicTantousha,
                raiseDic,
              );
            },
            child: Text(
              raiseDic.nameRaise,
              style: const TextStyle(fontSize: 16, color: Colors.blueAccent, fontWeight: FontWeight.w300),
            ),
          );
        }

        final actionButtons = state.buttonNameList.isEmpty
            ? <WorkflowRaiseButtonModel>[]
            : state.buttonNameList.sublist(0, state.buttonNameList.length - 1);
        return CupertinoActionSheet(
          title: const Column(
            children: [
              Text('その他', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
              Text('その他の実行処理を選択してください'),
            ],
          ),
          actions: actionButtons.map((button) => _buildActionSheetAction(context, button)).toList(),
          cancelButton: CupertinoActionSheetAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text(
              'キャンセル',
              style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue, fontSize: 18.0),
            ),
          ),
        );
      },
    );
  }
}
