import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/domain/usecases/get_application_workflow_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/models/approval_view_arguments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_search_task_condition.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/state/workflow_common_ui_state.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_tabs/presentation/controllers/workflow_tabs_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WorkflowApplicationController extends WorkflowCommonController {
  final GetApplicationWorkflowListUseCase getApplicationWorkflowListUseCase;
  final NavigationService navigationService;
  WorkflowApplicationController(
    this.getApplicationWorkflowListUseCase, {
    required super.dialogService,
    required super.getUserRoleUseCaseUseCase,
    required super.workflowActionService,
    required this.navigationService,
  });

  final scrollController = ScrollController();
  final WorkflowCommonUIState state = WorkflowCommonUIState();

  @override
  void onInit() {
    super.onInit();
    scrollController.addListener(_onScroll);
    final params = Get.arguments;
    if (params != null) {
      defaultForDoneSearchCondition('application');
      defaultForAllSearchCondition('application');
      var name;
      if (params is ApprovalViewArgumentsModel) {
        name = params.model.name;
      } else {
        name = params['name'];
      }
      if (name != null && name != '') {
        Get.snackbar('', '$nameしました。', duration: const Duration(seconds: 1));
      }
    }
    loadData();
    // 监听排序状态变化
    final workflowTabsController = Get.find<WorkflowTabsController>();
    ever(workflowTabsController.isApplicationAscending, (bool isAscending) {
      if (isAscending) {
        state.order.value = Order.ascend;
      } else {
        state.order.value = Order.descend;
      }
      state.filteredItems.value = sortByDeadlineDate(state.items, state.order.value);
    });
  }

  @override
  void onClose() {
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    super.onClose();
  }

  Future<void> doRefresh() async {
    await refreshData();
  }

  Future<void> refreshData() async {
    await loadData();
  }

  void onSearchChanged(String query) {
    state.currentSearchQuery.value = query;
    _applySearchAndFilter(query);
  }

  void openNotYet() {
    state.progress.value = 'not-yet';
    loadData();
  }

  void openDone() {
    state.progress.value = 'done';
    loadData();
  }

  void openAll() {
    state.progress.value = 'all';
    loadData();
  }

  void onItemTap(WorkflowEngineTaskModel item) async {
    final queryParams = {
      'processDefinitionId': item.processDefinitionId,
      'workflowTypeCode': item.workflowTypeCode,
      'workflowName': item.workflowName,
      'stepName': item.name,
      'assetTypeName': item.firstWorkflowAssetTypeName,
      'processInstanceId': item.processInstanceId,
      'taskId': item.id,
      'workflowId': item.workflowId,
      'state': item.state,
      'firstWorkflowAssetTypeName': item.firstWorkflowAssetTypeName,
      'assetTypeIdWithFirstWf': item.assetTypeIdWithFirstWf,
    };
    // 跳转到详情页面，可根据需求调整路由和参数
    await navigationService.navigateTo(
      AutoRoutes.applicationView,
      arguments: queryParams,
      id: SharedNavBarEnum.workflow.navigatorId,
    );
    await refreshData();
  }

  Future<void> loadData() async {
    try {
      state.isInitialLoading.value = true;
      final isFromPage = this.isFromPage();
      final searchConditionDataList =
          StorageUtils.get<String>(StorageUtils.keySearchConditionList + isFromPage) ?? '[]';
      // 使用 use case 获取数据
      final workflowEngineSearchTaskCondition = WorkflowEngineSearchTaskCondition();
      workflowEngineSearchTaskCondition.from = '0';
      String status;
      //　作成中
      if (state.progress.value == 'not-yet') {
        workflowEngineSearchTaskCondition.processStateCondition = 'TEMP';
        status = 'TEMP';
        // 申請済み
      } else if (state.progress.value == 'done') {
        status = 'ACTIVE';
        workflowEngineSearchTaskCondition.processStateCondition = 'ACTIVE';
        // 全て
      } else {
        status = 'ALL';
        workflowEngineSearchTaskCondition.processStateCondition = 'ALL';
      }
      List<WorkflowEngineTaskModel> tasks;
      final formDta = {'processStateCondition': status, 'searchConditions': searchConditionDataList, 'from': '0'};
      if (state.progress.value == 'not-yet') {
        tasks = await getApplicationWorkflowListUseCase(workflowEngineSearchTaskCondition);
      } else {
        tasks = await getApplicationWorkflowListUseCase(formDta);
      }

      state.items.value = tasks;
      state.filteredItems.value = sortByDeadlineDate(state.items, state.order.value);
      _applySearchAndFilter(state.currentSearchQuery.value);
    } catch (e, stackTrace) {
      handleException(e, stackTrace);
    } finally {
      state.isInitialLoading.value = false;
    }
  }

  /// 匹配搜索内容
  bool _matchesQuery(WorkflowEngineTaskModel item, String query) {
    final lowerQuery = query.toLowerCase();
    final workflowNameMatches = formatSearch((item.workflowName ?? '').toLowerCase()).contains(lowerQuery);
    final processInstanceIdMatches =
        item.processInstanceId != null && formatSearch(item.processInstanceId!.toLowerCase()).contains(lowerQuery);
    final wfNameMatches = formatSearch((item.wfName ?? '-').toLowerCase()).contains(lowerQuery);
    final nameMatches = formatSearch((item.name ?? '').toLowerCase()).contains(lowerQuery);

    return workflowNameMatches || processInstanceIdMatches || wfNameMatches || nameMatches;
  }

  /// 输出匹配的内容
  void _applySearchAndFilter(String query) {
    List<WorkflowEngineTaskModel> sortList;
    if (query.isEmpty) {
      sortList = List.from(state.items);
    } else {
      sortList = state.items.where((item) => _matchesQuery(item, query)).toList();
    }
    state.isEmptyResult.value = sortList.isEmpty;
    state.filteredItems.value = sortByDeadlineDate(sortList, state.order.value);
  }

  /// 清除参数并导航回指定页面
  void clearPosParam(BuildContext context) {
    Navigator.of(context).popUntil((route) => route.isFirst);
    Navigator.of(context).pushReplacementNamed('/tabs/workflow/application', arguments: {});
  }

  /// どの画面から遷移してきた
  String isFromPage() {
    String isFromPage = '';
    if (state.progress.value == 'done') {
      isFromPage = 'application';
    } else if (state.progress.value == 'all') {
      isFromPage = 'application-all';
    }
    return isFromPage;
  }

  void _onScroll() {
    state.showScrollToTop.value = scrollController.offset > 100;
  }
}
