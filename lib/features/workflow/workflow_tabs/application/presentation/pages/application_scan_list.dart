import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/refresh_load_more_list.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/bindings/application_scan_list_bindings.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/controllers/application_scan_list_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/scan_list_preview_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/application/scan_list', isConst: false, binding: ApplicationScanListBindings)
class ApplicationScanList extends GetView<ApplicationScanListController> {
  @override
  Widget build(BuildContext context) {
    final TextEditingController _searchController = TextEditingController();
    final _showClearButton = false.obs;
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text('資産リスト'),
        leading: IconButton(
          icon: const Icon(size: 37, Icons.chevron_left),
          onPressed: () {
            Get.back(id: SharedNavBarEnum.workflow.navigatorId);
          },
        ),
      ),
      body: RefreshLoadMoreList(
        scrollHeader: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Column(
            spacing: 4,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 40,
                width: double.infinity,
                child: Obx(() {
                  return TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: '何をお探しですか？',
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          if (_showClearButton.value)
                            IconButton(
                              icon: const Icon(Icons.clear, size: 20),
                              splashRadius: 20,
                              onPressed: () {
                                _searchController.clear();
                                _showClearButton.value = false;
                                controller.clearInputKey();
                              },
                            ),
                          Container(
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topRight: Radius.circular(8),
                                bottomRight: Radius.circular(8),
                              ),
                              color: Color(0xFF0B3E86),
                            ),
                            padding: const EdgeInsets.all(8),
                            child: GestureDetector(
                              onTap: () => controller.doSearch(_searchController.text.trim()),
                              child: SvgPicture.asset(
                                'assets/icons/icon-search.svg',
                                colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    style: const TextStyle(color: Colors.black, fontSize: 16),
                    onChanged: (value) {
                      _showClearButton.value = value.isNotEmpty;
                    },
                  );
                }),
              ),
              Obx(() {
                return Text('資産件数 ${controller.state.assetsCount.value}件', style: const TextStyle(color: Colors.white));
              }),
            ],
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        onRefresh: controller.doRefresh,
        onLoadMore: controller.onLoadMoreData,
        isLoading: controller.loadStatus,
        noMoreData: controller.state.moreThenLimit,
        items: controller.state.filterListTemp,
        itemBuilder: (context, index, item) {
          return ScanListPreviewWidget(
            asset: item,
            isCountingType: controller.state.isCountingType.value,
            hasScanTask: controller.state.hasScanTask.value,
          );
        },
      ),
    );
  }
}
