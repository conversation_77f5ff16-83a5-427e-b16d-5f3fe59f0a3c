import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_talk_item_view.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/widgets/expandable_section.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/bindings/application_view_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/controllers/application_view_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/pages/workflow_preview_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_comments_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_appbar.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_comments_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/application/view', isConst: false, bindings: [ApplicationViewBinding])
class ApplicationViewPage extends GetView<ApplicationViewController> {
  @override
  Widget build(BuildContext context) {
    // controller.loadDataDidEnter();
    return Scaffold(
      appBar: WorkflowAppBar(
        title: controller.state.workflowName.value,
        leading: IconButton(icon: const Icon(size: 37, Icons.chevron_left), onPressed: controller.back),
      ),
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Center(
                child: Column(
                  children: [
                    Obx(
                      () => controller.state.stepName.value.isNotEmpty
                          ? Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 30.0, vertical: 16),
                              child: ConstrainedBox(
                                // 设置最大宽度限制
                                constraints: BoxConstraints(maxWidth: Get.width - 60),
                                // 为了让下划线跟随文字长度变化
                                child: IntrinsicWidth(
                                  child: Column(
                                    children: [
                                      Padding(
                                        // 下划线比文字稍长
                                        padding: const EdgeInsets.only(left: 4, right: 4, bottom: 6),
                                        child: Text(
                                          controller.state.stepName.value,
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      ),
                                      Container(color: Colors.white, height: 2),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),
                    Obx(
                      () => controller.state.hasAssetTypeName.value
                          ? ExpandableSection(
                              title: '資産情報',
                              hasPaddingTop: false,
                              child: InkWell(
                                onTap: controller.toAssetListPage,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          children: [
                                            // 第一行
                                            Row(
                                              children: [
                                                SizedBox(
                                                  width: Get.width / 3 - 20,
                                                  child: Text(
                                                    '資産数',
                                                    style: TextStyle(color: Colors.black.withAlpha(140)),
                                                    textAlign: TextAlign.left,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Obx(() {
                                                    return Text(
                                                      '${controller.state.dataCount.value}件',
                                                      textAlign: TextAlign.left,
                                                    );
                                                  }),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 8),
                                            // 第二行
                                            Row(
                                              children: [
                                                SizedBox(
                                                  width: Get.width / 3 - 20,
                                                  child: Text(
                                                    '資産種類',
                                                    style: TextStyle(color: Colors.black.withAlpha(140)),
                                                    textAlign: TextAlign.left,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Obx(() {
                                                    return Text(
                                                      controller.state.assetTypeName.value,
                                                      textAlign: TextAlign.left,
                                                    );
                                                  }),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      const Icon(Icons.chevron_right, color: Colors.black),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),
                    Obx(
                      () => controller.state.assetDict.length > 0
                          ? ExpandableSection(
                              title: '入力情報',
                              hasPaddingTop: false,
                              child: WorkflowPreviewPage(
                                itemData: controller.state.assetDict,
                                comments: controller.state.comments,
                                workflowActionService: controller.workflowActionService,
                                onLoadComplete: controller.state.previewPageLoadComplete.value,
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),
                    Obx(
                      () => controller.state.hasAssetTypeName.value && controller.state.showButton.value != null
                          ? ExpandableSection(
                              title: 'コメント',
                              hasPaddingTop: false,
                              child: GetBuilder<WorkflowCommentsController>(
                                init: WorkflowCommentsController(
                                  showButton: controller.state.showButton.value ?? true,
                                  comments: controller.state.comments,
                                ),
                                builder: (controller) {
                                  return WorkflowCommentsWidget();
                                },
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),
                    Obx(
                      () =>
                          controller.state.isAllowTalk.value &&
                              controller.state.wfState.value != '作成中' &&
                              controller.state.liveTalkListUrl.value.isNotEmpty
                          ? ExpandableSection(
                              title: 'トーク',
                              subTitle: '+ トークルームを作成',
                              hasPaddingTop: false,
                              child: AfCustomizeTalkItemView(
                                // key: const ValueKey('talk_item_view'),
                                talkUrl: controller.state.liveTalkListUrl.value,
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),
                    Obx(
                      () =>
                          controller.state.isHasTantoushaF == true &&
                              controller.state.dynamicTaskInfo.length > 0 &&
                              controller.state.isSubmit == true
                          ? ExpandableSection(
                              title: '担当者',
                              hasPaddingTop: false,
                              child: Container(
                                width: double.infinity,
                                padding: const EdgeInsets.only(right: 5, top: 5, bottom: 5),
                                child: Column(
                                  children: controller.state.dynamicTaskInfo.asMap().entries.map((entries) {
                                    final int i = entries.key;
                                    final item = entries.value;
                                    return InkWell(
                                      onTap: () {
                                        controller.chooseTantousha(i);
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 10.0),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: RichText(
                                                text: TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text: item.taskDefName ?? '',
                                                      style: const TextStyle(
                                                        fontFamily: 'NotoSansJP',
                                                        fontSize: 16,
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                    const TextSpan(
                                                      text: '※',
                                                      style: TextStyle(
                                                        fontFamily: 'NotoSansJP',
                                                        fontSize: 16,
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.red,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                softWrap: true,
                                                overflow: TextOverflow.visible,
                                              ),
                                            ),
                                            const SizedBox(width: 4),
                                            Expanded(
                                              child: Obx(() {
                                                return Text(
                                                  controller.getTantoushaInfoWithIndex(i),
                                                  overflow: TextOverflow.visible,
                                                );
                                              }),
                                            ),
                                            const SizedBox(width: 4),
                                            const Icon(Icons.arrow_forward_ios, size: 14, color: Colors.black),
                                          ],
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                            )
                          : const SizedBox(),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Obx(() {
        final state = controller.state;
        final allButtons = <Widget>[];

        // USER Task 情况
        if (state.taskType.value == 'USER' && (state.isPermission.value)) {
          allButtons.add(
            WorkflowButton(
              text: '取消',
              moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
              onTapCallback: controller.cancel,
            ),
          );
          if (state.rejectionStatus.value == '1') {
            allButtons.add(
              WorkflowButton(
                text: state.vetoButtonInfo?.nameVeto ?? '',
                textColor: Colors.red,
                tapColor: Colors.red,
                moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
                onTapCallback: () {
                  controller.rejectionButton(state.vetoButtonInfo?.nameVeto ?? '');
                },
              ),
            );
          }
          if (state.sendBackStatus.value == '1') {
            allButtons.add(
              WorkflowButton(
                text: state.turnBackButtonInfo?.nameTurnBack ?? '',
                textColor: Colors.red,
                tapColor: Colors.red,
                moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
                onTapCallback: () {
                  controller.sendBack(state.turnBackButtonInfo?.nameTurnBack ?? '');
                },
              ),
            );
          }

          if (state.buttonNum.value > 5) {
            allButtons.add(
              WorkflowButton(
                text: 'その他',
                moreButtons: true,
                onTapCallback: () {
                  controller.otherButtonSwitchOpenModal(context);
                },
              ),
            );
            allButtons.add(
              WorkflowButton(
                text: state.buttonNameList[2].nameRaise,
                buttonColor: const Color(0xFF0B3E86),
                textColor: Colors.white,
                moreButtons: true,
                onTapCallback: () {
                  controller.approvalButton(state.buttonNameList[2]);
                },
              ),
            );
          } else {
            for (final raiseButtonDic in state.buttonNameList) {
              allButtons.add(
                WorkflowButton(
                  text: raiseButtonDic.nameRaise,
                  buttonColor: const Color(0xFF0B3E86),
                  textColor: Colors.white,
                  moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
                  onTapCallback: () {
                    controller.approvalButton(raiseButtonDic);
                  },
                ),
              );
            }
          }
        }
        // INPUT Task 情况
        else if (state.taskType.value == 'INPUT') {
          if (state.footerBtn['showCancel'] ?? false) {
            allButtons.add(
              WorkflowButton(
                text: '取消',
                moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
                onTapCallback: controller.cancel,
              ),
            );
          }
          if (state.hasAssetTypeName.value && !state.isSendBack.value) {
            allButtons.add(
              WorkflowButton(
                text: '一時保存',
                moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
                onTapCallback: controller.startSaveTemporary,
              ),
            );
          }
          if (controller.isNextEnable) {
            allButtons.add(
              WorkflowButton(
                text: '次へ',
                buttonColor: const Color(0xFF0B3E86),
                textColor: Colors.white,
                moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
                onTapCallback: controller.next,
              ),
            );
          } else {
            for (final raiseButtonDic in state.buttonNameList) {
              allButtons.add(
                WorkflowButton(
                  text: raiseButtonDic.nameRaise,
                  buttonColor: const Color(0xFF0B3E86),
                  textColor: Colors.white,
                  moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
                  onTapCallback: () {
                    controller.applyForApplication(raiseButtonDic);
                  },
                ),
              );
            }
          }
        }
        // SUSPENDED Task 情况
        else if (state.taskType.value == 'SUSPENDED') {
          allButtons.add(
            WorkflowButton(
              text: '取消',
              moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
              onTapCallback: controller.cancel,
            ),
          );
          if (state.hasAssetTypeName.value || !state.isSubmit.value) {
            allButtons.add(
              WorkflowButton(
                text: '一時保存',
                moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
                onTapCallback: controller.startSaveTemporary,
              ),
            );
          }
          if (controller.isNextEnable) {
            allButtons.add(
              WorkflowButton(
                text: '次へ',
                buttonColor: const Color(0xFF0B3E86),
                textColor: Colors.white,
                moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
                onTapCallback: controller.next,
              ),
            );
          } else {
            for (final raiseButtonDic in state.buttonNameList) {
              allButtons.add(
                WorkflowButton(
                  text: raiseButtonDic.nameRaise,
                  buttonColor: const Color(0xFF0B3E86),
                  textColor: Colors.white,
                  moreButtons: state.buttonNum.value == 4 && state.isBigFont.value,
                  onTapCallback: () {
                    controller.applyForApplication(raiseButtonDic);
                  },
                ),
              );
            }
          }
        }
        // GROUP Task 情况
        else if (state.taskType.value == 'GROUP' && (state.isPermission.value)) {
          if (state.footerBtn['showCancel'] ?? false) {
            allButtons.add(WorkflowButton(text: '取消', moreButtons: false, onTapCallback: controller.cancel));
          }
          if (state.rejectionStatus == '1') {
            allButtons.add(
              WorkflowButton(
                text: '否決',
                textColor: Colors.red,
                moreButtons: false,
                onTapCallback: controller.rejection,
              ),
            );
          }
          if (state.sendBackStatus == '1') {
            allButtons.add(
              WorkflowButton(
                text: '差戻し',
                textColor: Colors.red,
                moreButtons: false,
                onTapCallback: controller.sendBack,
              ),
            );
          }
          allButtons.add(
            WorkflowButton(
              text: '承認',
              buttonColor: const Color(0xFF0B3E86),
              textColor: Colors.white,
              moreButtons: false,
              onTapCallback: controller.approval,
            ),
          );
        }
        // SCAN Task 情况
        else if (state.taskType.value == 'SCAN') {
          if (state.footerBtn['showCancel'] ?? false) {
            allButtons.add(WorkflowButton(text: '取消', moreButtons: false, onTapCallback: controller.cancel));
          }
          if (state.sendBackStatus.value == '1' && state.isPermission.value) {
            allButtons.add(
              WorkflowButton(
                text: '差戻し',
                textColor: Colors.red,
                moreButtons: false,
                onTapCallback: controller.sendBack,
              ),
            );
          }
        }
        // 其他情况
        else {
          allButtons.add(WorkflowButton(text: '戻る', moreButtons: false, onTapCallback: controller.back));
          allButtons.add(WorkflowButton(text: '取消', moreButtons: false, onTapCallback: controller.cancel));
        }

        // 使用 distributeButtons 方法分配按钮到两行
        final buttonRows = WorkflowButton.distributeButtons(allButtons, state.isBigFont.value);

        return Container(
          color: Colors.white,
          child: SafeArea(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(spacing: 8, mainAxisAlignment: MainAxisAlignment.spaceBetween, children: buttonRows[0]),
                  buttonRows[1].isNotEmpty
                      ? Row(spacing: 8, mainAxisAlignment: MainAxisAlignment.spaceBetween, children: buttonRows[1])
                      : const SizedBox.shrink(),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }
}
