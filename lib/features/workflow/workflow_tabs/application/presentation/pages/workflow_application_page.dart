import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/scroll_to_top_button.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/controllers/workflow_application_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_build_row.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_build_segment_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WorkflowApplicationPage extends GetView<WorkflowApplicationController> {
  const WorkflowApplicationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            RefreshIndicator(
              onRefresh: controller.doRefresh,
              child: CustomScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                controller: controller.scrollController,
                slivers: [
                  // 搜索框
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: <PERSON><PERSON><PERSON><PERSON>(
                        height: 40,
                        child: TextField(
                          onChanged: controller.onSearchChanged,
                          decoration: InputDecoration(
                            hintText: 'WF名で検索',
                            prefixIcon: const Icon(Icons.search),
                            filled: true,
                            fillColor: Colors.white,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide.none,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // 分段选择器
                  // 在页面中替换原有的分段选择器部分
                  SliverToBoxAdapter(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white), // 整体白色边框
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Obx(() {
                        return Row(
                          children: [
                            // 分段项：作成中
                            Expanded(
                              child: GestureDetector(
                                onTap: () => controller.openNotYet(),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(8),
                                      bottomLeft: Radius.circular(8),
                                    ),
                                    color: controller.state.progress.value == 'not-yet'
                                        ? Colors.white
                                        : Colors.transparent,
                                  ),
                                  child: WorkflowBuildSegmentButton(
                                    text: '作成中',
                                    isActive: controller.state.progress.value == 'not-yet',
                                  ),
                                ),
                              ),
                            ),
                            // 垂直分割线
                            Container(width: 1, height: 40, color: Colors.white),
                            // 分段项：申請済
                            Expanded(
                              child: GestureDetector(
                                onTap: () => controller.openDone(),
                                child: Container(
                                  color: controller.state.progress.value == 'done' ? Colors.white : Colors.transparent,
                                  child: WorkflowBuildSegmentButton(
                                    text: '申請済',
                                    isActive: controller.state.progress.value == 'done',
                                  ),
                                ),
                              ),
                            ),
                            // 垂直分割线
                            Container(width: 1, height: 40, color: Colors.white),
                            // 分段项：すべて
                            Expanded(
                              child: GestureDetector(
                                onTap: () => controller.openAll(),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.only(
                                      topRight: Radius.circular(8),
                                      bottomRight: Radius.circular(8),
                                    ),
                                    color: controller.state.progress.value == 'all' ? Colors.white : Colors.transparent,
                                  ),
                                  child: WorkflowBuildSegmentButton(
                                    text: 'すべて',
                                    isActive: controller.state.progress.value == 'all',
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      }),
                    ),
                  ),

                  // 列表项
                  Obx(() {
                    if (controller.state.isInitialLoading.value) {
                      return const SliverFillRemaining(child: Center(child: CircularProgressIndicator()));
                    }
                    if (controller.state.isEmptyResult.value) {
                      return const SliverFillRemaining(
                        child: Center(child: Text('一致する結果はありません', style: TextStyle(fontSize: 16))),
                      );
                    }

                    return SliverList(
                      delegate: SliverChildBuilderDelegate((context, index) {
                        final item = controller.state.filteredItems[index];
                        return InkWell(
                          onTap: () => controller.onItemTap(item),
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.9),
                              borderRadius: BorderRadius.circular(8),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      WorkflowBuildRow(title: 'WFID', value: item.processInstanceId ?? ''),
                                      WorkflowBuildRow(
                                        title: 'WF名',
                                        value: item.wfName?.isEmpty ?? true ? '-' : item.wfName!,
                                      ),
                                      WorkflowBuildRow(title: 'WF種類名', value: item.workflowName ?? ''),
                                      WorkflowBuildRow(title: 'ステップ名', value: item.name ?? ''),
                                      WorkflowBuildRow(title: 'ステータス', value: item.state ?? ''),
                                      WorkflowBuildRow(title: '作成日', value: item.createdDate ?? ''),
                                      if (item.assignedBy != null)
                                        WorkflowBuildRow(title: '担当者', value: item.assignedBy!),
                                      if (item.approvedBy != null)
                                        WorkflowBuildRow(title: '依頼者', value: item.approvedBy!),
                                    ],
                                  ),
                                ),
                                const Icon(Icons.arrow_forward_ios, color: Colors.grey), // 右侧的向右箭头图标
                              ],
                            ),
                          ),
                        );
                      }, childCount: controller.state.filteredItems.length),
                    );
                  }),
                ],
              ),
            ),

            // 返回顶部按钮
            Positioned(
              right: 16,
              bottom: 16,
              child: Obx(
                () => AnimatedOpacity(
                  opacity: controller.state.showScrollToTop.value ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 200),
                  child: Visibility(
                    visible: controller.state.showScrollToTop.value,
                    child: ScrollToTopButton(
                      scrollToTop: () {
                        controller.scrollToTop(controller.scrollController);
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
