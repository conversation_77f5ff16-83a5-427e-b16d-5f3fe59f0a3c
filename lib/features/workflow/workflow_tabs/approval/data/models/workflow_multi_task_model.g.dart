// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_multi_task_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowMultiTaskModel _$WorkflowMultiTaskModelFromJson(
  Map<String, dynamic> json,
) => WorkflowMultiTaskModel(
  msg: json['msg'] as String?,
  code: (json['code'] as num?)?.toInt(),
  actions:
      json['actions'] == null
          ? null
          : WorkflowProcessActionModel.fromJson(
            json['actions'] as Map<String, dynamic>,
          ),
  assetListData:
      (json['assetListDatas'] as List<dynamic>?)
          ?.map(
            (e) =>
                WorkflowAssetListDataModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
);

Map<String, dynamic> _$WorkflowMultiTaskModelToJson(
  WorkflowMultiTaskModel instance,
) => <String, dynamic>{
  'msg': instance.msg,
  'code': instance.code,
  'actions': instance.actions?.toJson(),
  'assetListDatas': instance.assetListData?.map((e) => e.toJson()).toList(),
};
