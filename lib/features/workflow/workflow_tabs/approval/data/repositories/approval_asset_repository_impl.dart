import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/domain/repositories/approval_asset_repository.dart';

class ApprovalAssetRepositoryImpl with RepositoryErrorHandler implements ApprovalAssetRepository {
  final DioUtil dioUtil;

  ApprovalAssetRepositoryImpl({required this.dioUtil});

  @override
  Future<Map<String, dynamic>> httpWorkflowSubFormLayoutInfo(String subformId, String subformVersion) {
    return executeRepositoryTask<Map<String, dynamic>>(() async {
      final result = await dioUtil.get(
        GlobalVariable.workflowSubFormLayoutInfo,
        queryParams: {'subformId': subformId, 'version': subformVersion},
      );
      final data = result.data ?? {};
      return data;
    }, 'Error get workflow subform layout info.');
  }

  @override
  Future<void> httpAmountAssignScan(Map<String, dynamic> formData) {
    return executeRepositoryTask<void>(() async {
      final result = await dioUtil.post(GlobalVariable.amountAssignScan, data: formData, useFormUrlEncoded: true);
      final data = result.data ?? {};
      return data;
    }, 'Error get workflow amount assign.');
  }

  @override
  Future<Map<String, dynamic>> httpWorkflowsClaim(Map<String, dynamic> formData) {
    return executeRepositoryTask<Map<String, dynamic>>(() async {
      final result = await dioUtil.post(GlobalVariable.workflowsClaim, data: formData, useFormUrlEncoded: true);
      final data = result.data ?? {};
      return data;
    }, 'Error get workflow claim response.');
  }

  @override
  Future<Map<String, dynamic>> httpWorkflowsMultiTaskReload(Map<String, dynamic> formData) {
    return executeRepositoryTask<Map<String, dynamic>>(() async {
      final result = await dioUtil.post(
        GlobalVariable.workflowMultiTaskReload,
        data: formData,
        useFormUrlEncoded: true,
      );
      final data = result.data ?? {};
      return data;
    }, 'Error get workflow claim response.');
  }

  @override
  Future<void> httpWorkflowUpdateScanAmount(Map<String, dynamic> formData) {
    return executeRepositoryTask<void>(() async {
      final result = await dioUtil.post(
        GlobalVariable.workflowMobileAmountScan,
        data: formData,
        useFormUrlEncoded: true,
      );
      final data = result.data ?? {};
      return data;
    }, 'Error get workflow amount assign.');
  }
}
