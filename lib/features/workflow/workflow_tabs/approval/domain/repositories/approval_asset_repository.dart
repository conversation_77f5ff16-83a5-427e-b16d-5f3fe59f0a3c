abstract class ApprovalAssetRepository {
  /// サブフォーム情報
  Future<Map<String, dynamic>> httpWorkflowSubFormLayoutInfo(String subformId, String subformVersion);

  Future<void> httpAmountAssignScan(Map<String, dynamic> formData);

  Future<Map<String, dynamic>> httpWorkflowsClaim(Map<String, dynamic> formData);

  /// 複数人スキャン
  Future<Map<String, dynamic>> httpWorkflowsMultiTaskReload(Map<String, dynamic> formData);

  Future<void> httpWorkflowUpdateScanAmount(Map<String, dynamic> formData);
}
