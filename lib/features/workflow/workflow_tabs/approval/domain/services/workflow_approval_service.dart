import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/data/models/workflow_claim_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/data/models/workflow_multi_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/domain/repositories/approval_asset_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_task_form_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';

class WorkflowApprovalService {
  final ApprovalAssetRepository approvalAssetRepository;
  final WorkflowListRepository workflowListRepository;
  WorkflowApprovalService({required this.approvalAssetRepository, required this.workflowListRepository});

  /// 更新扫描数量
  Future<void> amountAssignScan(Map<String, dynamic> formData) async {
    try {
      return await approvalAssetRepository.httpAmountAssignScan(formData);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('スキャン数量の更新バリデーションが失敗に失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// claim work flow WFプロセスを受取
  Future<WorkflowClaimResponseModel> claimWorkFlow(Map<String, dynamic> formData) async {
    try {
      final result = await approvalAssetRepository.httpWorkflowsClaim(formData);
      final workflowClaimResponseModel = WorkflowClaimResponseModel.fromJson(result);
      return workflowClaimResponseModel;
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('WFプロセスを受取に失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<WorkflowMultiTaskModel> multiTaskReload(String processInstanceId, String taskId) async {
    try {
      final formData = {'processInstanceId': processInstanceId, 'taskId': taskId};
      final result = await approvalAssetRepository.httpWorkflowsMultiTaskReload(formData);
      final workflowMultiTaskModel = WorkflowMultiTaskModel.fromJson(result);
      return workflowMultiTaskModel;
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('WFプロセスを受取に失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> updateScanAmount(
    String processInstanceId,
    String taskId,
    String assetListId,
    int assetId,
    int operation,
    int? amount,
  ) async {
    try {
      final formData = {
        'processInstanceId': processInstanceId,
        'taskId': taskId,
        'assetListId': assetListId,
        'operation': operation.toString(),
        'assetId': assetId.toString(),
      };
      if (operation == 2) formData['amount'] = amount.toString();
      await approvalAssetRepository.httpWorkflowUpdateScanAmount(formData);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('WFスキャン数量を更新に失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<WorkflowTaskFormResponseModel> workflowUnScannedAssetList(
    String processInstanceId,
    String taskId,
    String state,
  ) async {
    try {
      final result = await workflowListRepository.httpGetWorkflowTaskForm(processInstanceId, taskId, state);
      return WorkflowTaskFormResponseModel.fromJson(result);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('WFスキャン数量を获取に失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
}
