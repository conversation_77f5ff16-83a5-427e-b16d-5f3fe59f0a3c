import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/data/repositories/approval_asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/domain/repositories/approval_asset_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/domain/services/workflow_approval_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/domain/usecases/get_subform_layout_Info_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/controllers/approval_scan_list_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/repositories/workflow_list_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_scan_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/usecases/workflow_get_layout_settings_usecase.dart';
import 'package:get/get.dart';

class ApprovalScanListBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ApprovalAssetRepository>(() => ApprovalAssetRepositoryImpl(dioUtil: Get.find()));
    Get.lazyPut<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find()));
    Get.lazyPut<UserRepository>(() => UserRepositoryImpl(dioUtil: Get.find()));
    Get.lazyPut<WorkflowListRepository>(() => WorkflowListRepositoryImpl(dioUtil: Get.find()));

    Get.lazyPut(() => WorkflowCommonScanService(repository: Get.find<WorkflowListRepository>()));
    Get.lazyPut(() => WorkflowGetLayoutSettingsUseCase(assetRepository: Get.find()));
    Get.lazyPut(() => GetSubformLayoutInfoUseCase(repository: Get.find()));
    Get.lazyPut(() => GetUserRoleUseCaseUseCase(userRepository: Get.find()));
    Get.lazyPut(() => WorkflowActionService(workflowListRepository: Get.find()));
    Get.lazyPut(() => WorkflowApprovalService(approvalAssetRepository: Get.find(), workflowListRepository: Get.find()));
    Get.lazyPut(() => GetTurlUseCase(s3Repository: Get.find()));

    Get.lazyPut(
      () => ApprovalScanListController(
        workflowGetLayoutSettingsUseCase: Get.find(),
        getSubformLayoutInfoUseCase: Get.find(),
        dialogService: Get.find(),
        getUserRoleUseCaseUseCase: Get.find(),
        workflowActionService: Get.find(),
        workflowApprovalService: Get.find(),
        getTurlUseCase: Get.find(),
        workflowCommonScanService: Get.find(),
      ),
    );
  }
}
