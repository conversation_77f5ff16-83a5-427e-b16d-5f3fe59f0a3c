import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_talk_item_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/controllers/expandable_section_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/get_authority_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/get_authority_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/controllers/approval_view_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/repositories/workflow_list_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/usecases/workflow_get_permission_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:get/get.dart';

class ApprovalViewBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<WorkflowListRepository>(() => WorkflowListRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    Get.lazyPut<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    Get.lazyPut<UserRepository>(() => UserRepositoryImpl(dioUtil: Get.find()));

    Get.lazyPut<GetAuthorityRepository>(() => GetAuthorityRepositoryImpl(dioUtil: Get.find()));

    Get.lazyPut(() => GetUserRoleUseCaseUseCase(userRepository: Get.find<UserRepository>()));

    Get.lazyPut(() => WorkflowGetPermissionUseCase(getAuthorityRepository: Get.find<GetAuthorityRepository>()));

    Get.lazyPut(() => WorkflowActionService(workflowListRepository: Get.find<WorkflowListRepository>()));

    Get.lazyPut(() => ExpandableSectionController());

    Get.lazyPut(() => AfCustomizeTalkItemController());

    Get.lazyPut(
      () => WorkflowCommonController(
        dialogService: Get.find(),
        getUserRoleUseCaseUseCase: Get.find<GetUserRoleUseCaseUseCase>(),
        workflowActionService: Get.find<WorkflowActionService>(),
      ),
    );

    Get.lazyPut(
      () => ApprovalViewController(
        dialogService: Get.find<DialogService>(),
        getUserRoleUseCaseUseCase: Get.find<GetUserRoleUseCaseUseCase>(),
        workflowActionService: Get.find<WorkflowActionService>(),
        workflowGetPermissionUseCase: Get.find<WorkflowGetPermissionUseCase>(),
      ),
    );
  }
}
