import 'package:asset_force_mobile_v2/core/extensions/getx_extension.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/domain/usecases/get_approval_workflow_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/controllers/workflow_approval_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/repositories/workflow_list_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:get/get.dart';

class WorkflowApprovalBinding extends Bindings {
  @override
  void dependencies() {
    // 注册 Repository 实现
    Get.lazyPutFenix<WorkflowListRepository>(() => WorkflowListRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    // 注册 UseCase
    Get.lazyPutFenix(() => GetApprovalWorkflowListUseCase(Get.find<WorkflowListRepository>()));
    Get.lazyPutFenix<UserRepository>(() => UserRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix(() => WorkflowActionService(workflowListRepository: Get.find<WorkflowListRepository>()));
    Get.lazyPutFenix<GetUserRoleUseCaseUseCase>(
      () => GetUserRoleUseCaseUseCase(userRepository: Get.find<UserRepository>()),
    );
    Get.lazyPutFenix(
      () => WorkflowApprovalController(
        Get.find<GetApprovalWorkflowListUseCase>(),
        dialogService: Get.find<DialogService>(),
        getUserRoleUseCaseUseCase: Get.find<GetUserRoleUseCaseUseCase>(),
        workflowActionService: Get.find<WorkflowActionService>(),
      ),
    );
  }
}
