import 'dart:convert';

import 'package:asset_force_mobile_v2/core/utils/number_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/workflow_scan_plugin.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/data/models/workflow_claim_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/domain/services/workflow_approval_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/domain/usecases/get_subform_layout_Info_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/state/approval_scan_list_state.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_asset_column_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_scan_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_task_form_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_scan_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/usecases/workflow_get_layout_settings_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum ScanTabType {
  all, // すべて
  scanned, // スキャン済
  unScanned, // 未スキャン
}

extension ScanTabTypeExt on ScanTabType {
  int? get numberValue {
    switch (this) {
      case ScanTabType.all:
        return null;
      case ScanTabType.scanned:
        return 1;
      case ScanTabType.unScanned:
        return 0;
    }
  }
}

/// 编辑数量方式
enum ManualType {
  subtraction, // 减少
  addition, // 增加
  input, // 手动输入
}

class ApprovalScanListController extends WorkflowCommonController {
  final WorkflowGetLayoutSettingsUseCase workflowGetLayoutSettingsUseCase;
  final GetSubformLayoutInfoUseCase getSubformLayoutInfoUseCase;
  final WorkflowApprovalService workflowApprovalService;
  final GetTurlUseCase getTurlUseCase;
  final WorkflowCommonScanService workflowCommonScanService;
  ApprovalScanListController({
    required this.workflowGetLayoutSettingsUseCase,
    required this.getSubformLayoutInfoUseCase,
    required super.dialogService,
    required super.getUserRoleUseCaseUseCase,
    required super.workflowActionService,
    required this.workflowApprovalService,
    required this.getTurlUseCase,
    required this.workflowCommonScanService,
  });

  ApprovalScanListState state = ApprovalScanListState();
  final scrollController = ScrollController();
  int _nextPageNum = 1; // 页码
  final _getAmountDataNumber = 20; // 每次后台返回多少条数据
  var loadStatus = false.obs;

  @override
  void onInit() {
    super.onInit();
    scrollController.addListener(_onScroll);
  }

  @override
  void onReady() {
    super.onReady();
    _initDataFromParams();
  }

  void onTabChanged(ScanTabType scanState) async {
    state.dataNumAndNextPage.scanState = scanState;
    _nextPageNum = 1; // 切换tab时重置页码
    await assetListPagePaginationTarget();
  }

  void _initDataFromParams() async {
    final params = Get.arguments;
    if (params == null) return;
    final isFromLocationSettingPage = params['isFromLocationSettingPage'] ?? false;
    if (isFromLocationSettingPage) {
      // toNoSubprocessScan(isFromLocationSettingPage: true, scanType: params['scanType'] ?? '');
      return;
    }
    //リスト画面から
    final claimNow = params['shouldClaim'] ?? false;
    state.dynamicTantousha = params['dynamicTantousha'];
    state.dynamicTaskInfo = params['dynamicTaskInfo'];
    state.rawFormData = params['rawFormData'];
    state.stepName = params['stepName'] ?? '';
    state.workflowScript = params['workflowScript'] ?? '';
    state.allMustScan = state.rawFormData?.actions?.allMustScan ?? false;
    state.comments = params['comments'] ?? <CommentsModel>[].obs;
    state.wfModel = params['rawWorkFlowModel'];

    state.wfDict = params['wfDict'] ?? <String, List<AssetItemListModel>>{};
    state.simplyMode.value = params['simplyMode'] ?? false;
    state.dataNumAndNextPage.processInstanceId = state.wfModel.processInstanceId ?? '';
    state.dataNumAndNextPage.taskId = state.wfModel.id ?? '';
    state.processInstanceId = state.wfModel.processInstanceId ?? '';
    state.taskId = state.wfModel.id ?? '';
    // 回调
    state.updateAssetAction = params['updateAssetAction'] ?? null;
    state.updateIDAction = params['updateIDAction'] ?? null;
    state.setHasBeenChangedAsset = params['setHasBeenChangedAsset'] ?? null;
    await loadData(state.processInstanceId, state.taskId);
    if (claimNow) {
      claim();
    }
  }

  Future<void> loadData(String processInstanceId, String taskId, {bool doubleRequest = false}) async {
    final List<Future> allApi = [];
    // 获取资产类型列表
    final assetItemResultApi = workflowGetLayoutSettingsUseCase.call(state.wfModel.assetTypeIdWithFirstWf.toString());
    allApi.add(assetItemResultApi);
    // ワークフローの資産リスト
    final assetColumnsApi = workflowActionService.getWorkflowAssetColumn(
      state.wfModel.workflowId,
      state.wfModel.processDefinitionId,
    );
    allApi.add(assetColumnsApi);
    // taskForm
    final resultApi = workflowActionService.getWorkflowTaskForm(processInstanceId, taskId, state: '1');
    allApi.add(resultApi);
    final isSubformId = state.rawFormData?.subformId != null && state.rawFormData?.subformId != '';
    if (isSubformId) {
      // サブフォーム情報
      final subformInfoApi = getSubformLayoutInfoUseCase.call(
        ApprovalSubformParams(
          subformId: state.rawFormData!.subformId.toString(),
          subformVersion: state.rawFormData!.subformVersion.toString(),
        ),
      );
      allApi.add(subformInfoApi);
    } else {
      allApi.add(Future.value(null));
    }

    // 并发执行 按照ionic
    final resultAllApi = await Future.wait(allApi);

    // 资产类型列表
    final assetItemResult = resultAllApi[0];
    // ワークフローの資産リスト
    final assetColumns = resultAllApi[1];
    // taskForm
    final result = resultAllApi[2];
    // サブフォーム情報
    final subformInfo = isSubformId ? resultAllApi[3] : null;

    state.assetColumns = assetColumns as List<WorkflowAssetColumnModel>;
    if (assetItemResult is AssetItemResponse && assetItemResult.assetItemList is List) {
      final assetItemList = assetItemResult.assetItemList!;
      state.assetTypeItemMap.clear();
      for (final obj in assetItemList) {
        final itemId = obj?.itemId;
        if (itemId != null) {
          if (obj != null) state.assetTypeItemMap[itemId] = obj;

          // 获取资产名的显示名
          if (obj?.itemName == 'assetName') {
            state.assetNameDisplayName = obj?.itemDisplayName ?? '';
          }

          // 获取识别码的显示名
          if (obj?.itemName == 'identityCode') {
            state.identityCodeDisplayName = obj?.itemDisplayName ?? '';
          }
        }
      }
    }

    if (isSubformId) {
      state.subformTitle.value = (subformInfo?['subform']?['subformName']?.isNotEmpty ?? false)
          ? subformInfo?['subform']?['subformName']
          : 'フォーム入力';
    }
    // 处理主表单数据
    final resultData = result['resultData'] ?? {};
    state.isCountingType.value = resultData['updateAmountShow'] ?? false;
    state.rawFormData = WorkflowTaskFormResponseModel.fromJson(resultData);
    state.hasScanTask.value = resultData['hasScanTask'] ?? true;

    final actions = resultData['actions'] ?? {};
    state.isPermissionWF.value = actions['isPermission'] ?? false;
    if (resultData['taskType'] == 'SCAN') {
      state.showUnclaimButton.value = actions['isPermission'] ?? false;
    }

    if (actions['rejectionStatus'] == '1') {
      state.showRejectButton.value = actions['isPermission'] ?? false;
    }

    if (actions['sendBackStatus'] == '1' || actions['sendBackStatus'] == '2') {
      state.showSendBackButton.value = actions['isPermission'] ?? false;
    }

    if (actions['sendBackDestination'] != null) {
      state.backDestination = actions['sendBackDestination'];
    }

    state.isRentalWorkflow.value = resultData['isRentalWorkflow'] ?? false;
    state.isPermission = actions['isPermission'] ?? false;

    // 主体资产列表数据处理方法
    await assetListPagePaginationTarget();
    state.showClaimButton.value = claimStatus;
    state.showConfirmButton.value = confirmStatus;
    state.showActions.value = hasExec;
    state.showFooter.value = isShowFooter;
    getScannedCount();
  }

  /// 加载更多，下一页
  void onLoadMoreData() async {
    _nextPageNum += 1;
    await assetListPagePaginationTarget();
  }

  /// 列表数量
  void getScannedCount() {
    final total = state.dataNumAndNextPage.keyword.isNotEmpty
        ? state.workflowExecData.value.allAssetCountInKeywordSearch
        : state.workflowExecData.value.totalAssets;
    final unScanned = state.dataNumAndNextPage.keyword.isNotEmpty
        ? state.workflowExecData.value.noScanningAssetCountInKeywordSearch
        : state.workflowExecData.value.numberOfUnScanned;
    state.tabCounts[1] = total - unScanned;
    state.tabCounts[2] = state.dataNumAndNextPage.keyword.isNotEmpty
        ? state.workflowExecData.value.noScanningAssetCountInKeywordSearch
        : state.workflowExecData.value.numberOfUnScanned;
    state.tabCounts[0] = state.dataNumAndNextPage.keyword.isNotEmpty
        ? state.workflowExecData.value.allAssetCountInKeywordSearch
        : state.workflowExecData.value.totalAssets;
  }

  Future<void> assetListPagePaginationTarget() async {
    await showLoading();
    try {
      // 迎合api需要把正常页码减去1
      final nextPage = _nextPageNum - 1;
      final skip = nextPage * _getAmountDataNumber;
      if (state.dataNumAndNextPage.skip == skip && skip != 0) {
        return;
      }
      state.dataNumAndNextPage.skip = skip;
      final getAssetListData = await getAssetsByKeywordInWfAssetListForAssignScanDataFun(
        state.dataNumAndNextPage.processInstanceId,
        state.dataNumAndNextPage.taskId,
        state.dataNumAndNextPage.keyword,
        state.dataNumAndNextPage.skip,
        state.dataNumAndNextPage.rows,
      );
      final wfAssetList = getAssetListData.assetListDatas ?? [];
      if (nextPage == 0) {
        // 初始状态资产
        state.filterListTemp.value = wfAssetList;
      } else {
        // 下一页资产
        state.filterListTemp.addAll(wfAssetList);
      }
      state.moreThenLimit.value = !(wfAssetList.length != 0 && wfAssetList.length % _getAmountDataNumber == 0);
      await updateAssetList();
    } finally {
      hideLoading();
    }
  }

  Future<WorkflowAssignScanModel> getAssetsByKeywordInWfAssetListForAssignScanDataFun(
    String processInstanceId,
    String taskId,
    String? keyWord,
    int skip,
    int row,
  ) async {
    // 分页核心api请求
    final getAssetListData = await workflowActionService.getAssetsByKeywordInWfAssetListForAssignScan(
      processInstanceId,
      taskId,
      keyWord,
      skip,
      row,
      scanState: state.dataNumAndNextPage.scanState.numberValue,
    );
    state.workflowExecData.value.totalAssets = getAssetListData.allAssetCount ?? 0;
    state.workflowExecData.value.numberOfUnScanned = getAssetListData.noScanningAssetCount ?? 0;
    state.workflowExecData.value.allAssetCountInKeywordSearch = getAssetListData.allAssetCountInKeywordSearch ?? 0;
    state.workflowExecData.value.totalSavedAssetAmountCount = getAssetListData.totalSavedAssetAmountCount ?? 0;
    state.workflowExecData.value.noScanningAssetCountInKeywordSearch =
        getAssetListData.noScanningAssetCountInKeywordSearch ?? 0;
    state.workflowExecData.value.scannedAssetCount = getAssetListData.scannedAssetCount ?? 0;

    // 完成的资产数
    final finishScanningAssetCount =
        (state.workflowExecData.value.totalAssets) - (state.workflowExecData.value.numberOfUnScanned);
    if (!state.isCountingType.value && !state.isRentalWorkflow.value)
      state.workflowExecData.value.totalSavedAssetAmountCount = finishScanningAssetCount;
    return getAssetListData;
  }

  /// 資産リスト更新
  Future<void> updateAssetList() async {
    sortAssetListData();
    for (var asset in state.filterListTemp) {
      bool isFinishScan;
      if (state.isCountingType.value) {
        isFinishScan = asset.savedAssetAmount == asset.assetAmount.value;
      } else {
        isFinishScan = asset.scanState?.value == '1';
      }
      var totalAmount = 1;
      if (state.isRentalWorkflow.value) totalAmount = asset.assetTotalCount ?? 1;
      if (state.isCountingType.value) totalAmount = asset.assetAmount.value;

      int assetScannedCount;
      bool isSelect = false;
      if (state.isCountingType.value || state.isRentalWorkflow.value) {
        assetScannedCount = asset.savedAssetAmount ?? 0;
        if (assetScannedCount > 0) {
          isSelect = true;
        }
      } else {
        assetScannedCount = asset.scanState?.value == '1' ? totalAmount : 0;
      }

      String step = 'スキャン';
      //セクション関連の設定
      final assetDic = safeDecodeAssetText(asset.assetText);
      String status = (asset.processStepName == null || asset.processStepName == '') ? ' - ' : asset.processStepName!;

      // ホーム画像の更新
      dynamic homeImage;
      for (final item in state.assetTypeItemMap.values) {
        if (item.itemType == 'image' && item.mobileFlg == '1') {
          dynamic imageTypeItem = assetDic[item.itemName] ?? '';
          if (imageTypeItem is String && imageTypeItem.isNotEmpty) {
            imageTypeItem = jsonDecode(imageTypeItem);
          }
          if (imageTypeItem is List && imageTypeItem.isNotEmpty) {
            final homeImageItem = imageTypeItem.firstWhere((value) => value['isHomeImage'] == true, orElse: () => null);
            if (homeImageItem != null) {
              bool isView = true;
              final optionObj = jsonDecode(item.option ?? '{}');
              final sectionPrivateGroups = optionObj['sectionPrivateGroups'] ?? '';
              if (sectionPrivateGroups != null) {
                isView = checkIsView(sectionPrivateGroups);
              }
              if (!isView) {
                continue;
              }
              homeImage = homeImageItem;
              homeImage['turl'] = await getTurlUseCase(homeImage['url'] ?? '');
              asset.homeImage = HomeImageModel.fromJson(homeImage);
              break;
            }
          }
        }
      }

      if (isFinishScan) {
        status = '完了';
        step = 'スキャン済';
      } else {
        // メタルワン場合、ステータスは完了かどうかを判断する
        if (isPassedSubProcessWithSubForm && state.rawFormData?.subProcessProductAssetList != null) {
          for (var subProcessProductAsset in state.rawFormData!.subProcessProductAssetList!) {
            if (subProcessProductAsset.assetId == asset.assetId.toString() && subProcessProductAsset.taskId == null) {
              status = '完了';
              step = subProcessProductAsset.stepName ?? '';
            }
            break;
          }
        }
      }
      if (!state.allMustScan && !state.isRentalWorkflow.value && (asset.savedAssetAmount ?? 0) > 0) {
        status = '完了';
      }
      final backupEnterScannedCount =
          state.backupEnterScannedCount[assetDic['identityCode'] ?? '' + assetDic['location'] ?? ''];
      if (backupEnterScannedCount == null || backupEnterScannedCount == '') {
        state.backupEnterScannedCount[assetDic['identityCode'] ?? '' + assetDic['location'] ?? ''] = assetScannedCount;
      }
      asset.assetScanedCount.value = assetScannedCount;
      asset.status = status;
      asset.step = step;
      asset.isSelect = isSelect;
    }
  }

  Future<void> statisticsScan() async {}

  /// 正常扫描 (合并スキャン画面に遷移する toScanPage； isNosubprocess一直为true 不会进入toStanderScan)
  Future<void> toNoSubprocessScan({bool isFromLocationSettingPage = false, String scanType = ''}) async {
    state.isFromLocationSettingPage = isFromLocationSettingPage;
    state.scanType = scanType;
    final data = WorkflowScanBarcodeData(
      workflowName: state.wfModel.wfName ?? '',
      assetTypeName: state.wfModel.firstWorkflowAssetTypeName ?? '',
      progressDefinitionId: state.wfModel.processDefinitionId,
      taskName: state.wfModel.name ?? '',
      assetListId: state.rawFormData?.assetListId.toString(),
      processInstanceId: state.processInstanceId,
      taskId: state.taskId,
      isMultiScan: false,
      showTrim: state.isRentalWorkflow.value, // 'テナント区分("true"：レンタル会社、 false：レンタル以外の会社)'
      jobType: 'approvalWF', // AIOCR用
      isFromNew: false,
      isFromLocationSettingPage: state.isFromLocationSettingPage,
      isCountingType: state.isCountingType.value,
      totalSavedAssetAmountCount: state.workflowExecData.value.scannedAssetCount,
    );
    final scanData = await WorkflowScanPlugin().noSubprocessWorkflowScanBarcode(data);
    state.workflowExecData.value.totalSavedAssetAmountCount = scanData['totalSavedAssetAmountCount'] ?? 0;

    await assetListPagePaginationTarget();
  }

  /// 検索する
  void doSearch() async {
    state.filterListTemp.clear();
    _nextPageNum = 1;
    await assetListPagePaginationTarget();
  }

  /// 入力した検索キーを削除
  void clearInputKey() async {
    state.filterListTemp.clear();
    state.dataNumAndNextPage.keyword = '';
    _nextPageNum = 1;
    await assetListPagePaginationTarget();
  }

  /// claim 実行
  void claim() async {
    final List<AssetItemListModel> assetTypeList = [];
    final itemDataDictCopy = Map<String, List<AssetItemListModel>>.from(
      state.wfDict.map((k, v) => MapEntry(k, List<AssetItemListModel>.from(v))),
    );
    itemDataDictCopy.forEach((sectionName, assets) {
      assetTypeList.addAll(assets);
    });

    for (var assetType in assetTypeList) {
      bool isView = true;
      final optionObj = assetType.option != null && assetType.option != '' ? jsonDecode(assetType.option!) : {};
      final sectionPrivateGroups = optionObj['sectionPrivateGroups'];
      if (sectionPrivateGroups != null) {
        isView = checkIsView(sectionPrivateGroups);
      }
      if (!isView) {
        //没有权限预览
        continue;
      }
      var optionObject = assetType.optionObject;
      if (optionObject == null) {
        optionObject = optionObj;
      }
      // true 不可以编辑（只读），false可以编辑
      final isReadonly = (optionObject?.readonly?.toString() ?? '') == '1';

      if (isReadonly) {
        continue;
      }

      // 必須チェック
      if (assetType.inputFlg == '1') {
        final data = assetType.defaultData;
        if (data == '' ||
            data == null ||
            (data is Map && data.isEmpty) ||
            (data is String && data.trim().isEmpty) ||
            data == '[]') {
          await dialogService.show(content: '${assetType.itemName}を設定してください。');
          return;
        }
      }

      if (assetType.inputFlg == '1' && assetType.itemType == 'checkbox') {
        final data = assetType.defaultData;
        if (data != null && data is String && data != '1') {
          final cbName = assetType.itemName ?? assetType.itemDisplayName;
          await dialogService.show(content: '$cbNameを設定してください。');
          return;
        }
      }

      // 错误提示
      if (assetType.isShowMessageTS == true) {
        await dialogService.show(content: '入力エラーがあるのでご確認ください。');
        return;
      }

      // 长度、数字、邮箱等校验
      final data = assetType.defaultData;
      if (data != null) {
        // 桁数チェック
        if (optionObject?.maxlength != null && data is String) {
          final maxLength = int.tryParse(optionObject!.maxlength!) ?? 0;
          if (data.length > maxLength) {
            await dialogService.show(content: '${assetType.itemName}を${maxLength}文字以内に入力してください。');
            return;
          }
        }
        // 数字或通货类型输入位数检查
        if (assetType.itemType == 'number' || assetType.itemType == 'currency') {
          final validateResult = NumberUtils.validateNumericAndCurrency(data);
          if (validateResult.isNotEmpty) {
            final name = assetType.itemDisplayName ?? assetType.itemName;
            await dialogService.show(content: '$nameは$validateResult');
            return;
          }
        }
        // メールアドレスの有効性チェック
        if (assetType.itemType == 'email') {
          final reg = RegExp(r'^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$');
          if (data is String && !reg.hasMatch(data)) {
            await dialogService.show(content: '${assetType.itemName}のフォーマットが間違っています。');
            return;
          }
        }
        baseAction('ピッキング', false, state.wfDict, () async {
          final veriables = getVariables(state.wfDict, state.comments);
          final params = {'processInstanceId': state.processInstanceId, 'taskId': state.taskId, 'variables': veriables};
          // processInstanceId commentItem taskId
          final result = await workflowApprovalService.claimWorkFlow(params);
          await updateDataAfterClaim(result);
        }, () async {});
      }
    }
  }

  Future<void> updateDataAfterClaim(WorkflowClaimResponseModel model) async {
    state.wfModel.id = model.currentTaskId;
    state.wfModel.processInstanceId = model.processInstanceId;
    await loadData(model.processInstanceId ?? '', model.currentTaskId ?? '', doubleRequest: true);
    if (state.updateAssetAction != null) {
      await state.updateAssetAction!(model.processInstanceId ?? '', model.currentTaskId ?? '');
    }
  }

  /// 承认
  void approvalButton() async {
    if (!checkDynamicTaskCanSubmit(state.dynamicTaskInfo, state.dynamicTantousha)) {
      await dialogService.show(content: '各ステップの承認者を選択してください。', confirmText: 'はい');
      return;
    } else {
      if (state.dynamicTaskInfo.isNotEmpty && state.dynamicTantousha.isNotEmpty) {
        for (var i = 0; i < state.dynamicTaskInfo.length; i++) {
          final element = state.dynamicTaskInfo[i];
          if (element.assignDynamicType == 'group') {
            element.authorizerId = state.dynamicTantousha[i]['roleId'];
            element.authorizerName = state.dynamicTantousha[i]['roleName'];
          } else {
            element.authorizerId = state.dynamicTantousha[i]['userId'];
            element.authorizerName = state.dynamicTantousha[i]['lastName'] + state.dynamicTantousha[i]['firstName'];
          }
        }
      }
    }
    try {
      await showLoading();
      final isCheck = await checkAndGetData(state.wfDict);
      if (!isCheck) return;
    } finally {
      hideLoading();
    }
    if (state.rawFormData?.taskType == 'SCAN') {
      final isExecutionMarker = await confirmWithScanCompleteNoSubprocessWithAllMust(
        assetDict: state.wfDict,
        comments: state.comments,
        rawFormData: state.rawFormData,
        processInstanceId: state.processInstanceId,
        taskId: state.taskId,
        isApproval: true,
        assignDynamicData: state.dynamicTaskInfo,
        buttonName: '',
        workflowScript: state.workflowScript,
        isFromAssetListPage: false,
      );
      approvalBtnDisabled(isExecutionMarker);
    } else {
      confirmWithScanComplete(
        isAssetListEditable: false,
        assetCount: state.workflowExecData.value.totalAssets.toString(),
        assetDict: state.wfDict,
        comments: state.comments,
        rawFormData: state.rawFormData,
        processInstanceId: state.processInstanceId,
        taskId: state.taskId,
        isApproval: true,
        assignDynamicData: state.dynamicTaskInfo,
        buttonName: '',
        workflowScript: state.workflowScript,
        isFromAssetListPage: true,
      );
    }
  }

  void saveTemporaryFromScanTask() async {
    await saveTemporaryFromScanTaskCommon(
      isCountingType: state.isCountingType.value,
      assetDict: state.wfDict,
      comments: state.comments,
      processInstanceId: state.processInstanceId,
      assetListId: (state.rawFormData?.assetListId ?? 0).toString(),
      taskId: state.taskId,
      assignDynamicData: state.dynamicTaskInfo,
      isUserTask: state.rawFormData?.taskType == 'USER' || state.rawFormData?.taskType == 'GROUP',
    );
  }

  /// 否决
  void rejectionButton() async {
    try {
      await showLoading();
      final isCheck = await checkAndGetData(state.wfDict);
      if (!isCheck) return;
    } finally {
      hideLoading();
    }
    confirmWithAssets(
      state.wfDict,
      state.comments,
      state.processInstanceId,
      state.taskId,
      state.rawFormData?.assetListDatas,
      state.rawFormData?.assetListTitle ?? '',
      state.wfModel.assetTypeIdWithFirstWf ?? 0,
      state.workflowScript,
      state.stepName,
      approvalBtnDisabled,
      true,
      false,
      '',
    );
  }

  /// 差戻し
  void sendBackButton() async {
    try {
      await showLoading();
      final isCheck = await checkAndGetData(state.wfDict);
      if (!isCheck) return;
    } finally {
      hideLoading();
    }
    workflowSendBack(
      state.wfDict,
      state.comments,
      state.backDestination,
      state.taskId,
      state.workflowScript,
      state.stepName,
      approvalBtnDisabled,
      '',
      backTabIndex: 2,
    );
  }

  /// 解除
  void unclaimButton() async {
    try {
      await showLoading();
      final isCheck = await checkAndGetData(state.wfDict);
      if (!isCheck) return;
    } finally {
      hideLoading();
    }
    unClaimFormApproval(state.wfDict, state.comments, state.processInstanceId, state.taskId, approvalBtnDisabled);
  }

  /// 手动调节数量核心方法
  Future<void> updateAssetScanStatus(RegisteredAssetListModel item, ManualType editType, {String event = ''}) async {
    final assetListId = state.rawFormData?.assetListId.toString();
    final formData = <String, dynamic>{
      'processInstanceId': state.processInstanceId,
      'taskId': state.taskId,
      'assetListId': assetListId,
      'assetId': item.assetId.toString(),
      'operation': editType.index.toString(),
    };
    item.isApprovalSys = true;
    final oldValue = item;
    final dataNumAndNextPageClone = state.dataNumAndNextPage;
    dataNumAndNextPageClone.skip = 0;
    await showLoading();
    try {
      switch (editType) {
        case ManualType.subtraction:
          await workflowApprovalService.amountAssignScan(formData);
          workflowActionService.minusClick(item);
          break;
        case ManualType.addition:
          await workflowApprovalService.amountAssignScan(formData);
          workflowActionService.plusClick(item);
          break;
        case ManualType.input:
          final isTrue = await workflowActionService.updateCount(item, event, needResetAmount: false);
          if (!isTrue) {
            if (item.assetScanedCount.value != 0) {
              item.assetScanedCount.value = oldValue.assetScanedCount.value;
            }
          }
          formData['amount'] = item.assetScanedCount.value;
          await workflowApprovalService.amountAssignScan(formData);
          break;
      }
    } catch (e) {
      if (editType == ManualType.input) {
        item.assetScanedCount.value = oldValue.assetScanedCount.value;
      }
      return;
    } finally {
      await assetListPagePaginationTarget();
      getScannedCount();
      hideLoading();
    }
    // 匹配到原始数据
    // 判断扫描的资产是否在当前页中
    if (item.assetId != null) {
      final isContainsAssetData = state.filterListTemp.any(
        (assetItem) => assetItem.assetId.toString() == item.assetId.toString(),
      );
      final oldChosenAsset = state.chosenAssetMap[item.assetId.toString()] ?? null;
      // 是否操作过这条资产数量的扫描（非手动增加数量，只是扫描增加）
      final hoenAsset = ChosenAsseDataInterface(
        updatedNum: item.assetScanedCount.value,
        isCurrentPageTheAsset: isContainsAssetData,
      );
      if (oldChosenAsset != null) {
        hoenAsset.initScanCount = oldChosenAsset.initScanCount;
      } else {
        hoenAsset.initScanCount = oldValue.assetScanedCount.value;
        if (editType == ManualType.input) {
          hoenAsset.initScanCount = oldValue.oldAssetAmount;
        }
      }
      state.chosenAssetMap[item.assetId.toString()] = hoenAsset;
      if (state.setHasBeenChangedAsset != null) state.setHasBeenChangedAsset!(state.chosenAssetMap);
    }

    item.isSelect = item.assetScanedCount.value > 0;

    if (item.assetAmount.value == item.assetScanedCount.value) {
      item.step = 'スキャン済';
      item.status = '完了';
      item.scanState?.value = '1';
    } else {
      item.step = 'スキャン中';
      item.status = ' - ';
      item.scanState?.value = '0';
    }

    if (!state.allMustScan && !state.isRentalWorkflow.value) {
      if (item.assetScanedCount.value > 0) {
        item.status = '完了';
        item.scanState?.value = '1';
      } else {
        item.scanState?.value = '0';
      }
    }
  }

  /// MとPの並び順調整
  void sortAssetListData() {
    // Mの部分を取得
    final mainAssetList = state.filterListTemp.where((item) => item.mpFlag == 'M').toList();
    // Pの部分を取得
    final partAssetList = state.filterListTemp.where((item) => item.mpFlag == 'P').toList();
    // Nの部分を取得
    final nGroupAssetList = state.filterListTemp.where((item) => item.mpFlag == 'N').toList();
    // N,M,Pではない部分
    final noneGroupAssetList = state.filterListTemp
        .where((item) => item.mpFlag != 'N' && item.mpFlag != 'M' && item.mpFlag != 'P')
        .toList();

    state.filterListTemp
      ..clear()
      ..addAll([...nGroupAssetList, ...noneGroupAssetList, ...mainAssetList, ...partAssetList]);
  }

  bool get isPassedSubProcessWithSubForm {
    return (state.rawFormData?.hasSubProcess ?? false) && (state.rawFormData?.hasPassedSubProcess ?? false);
  }

  /// フッター表示されるかを読み込む
  bool get isShowFooter {
    final isScanType = state.rawFormData?.taskType == 'SCAN';
    return isScanType &&
        (claimStatus ||
            confirmStatus ||
            state.showRejectButton.value ||
            state.showSendBackButton.value ||
            state.showUnclaimButton.value);
  }

  /// 受取ボタン状態読み込む
  bool get claimStatus {
    final showClaim = state.rawFormData?.actions?.possibleOfClaim ?? false;
    return showClaim;
  }

  /// 承認ボタン状態読み込む
  bool get confirmStatus {
    if (claimStatus) return false;
    return state.isPermission && isExecComplete;
  }

  /// 実行された資産がある
  bool get hasExec {
    if (state.filterListTemp.isEmpty) return false;
    final possibleOfClaim = state.rawFormData?.actions?.possibleOfClaim ?? false;
    final isScanType = state.rawFormData?.taskType == 'SCAN';
    return state.isPermission && !possibleOfClaim && isScanType;
  }

  /// すべて実行完了
  bool get isExecComplete {
    final type = state.rawFormData?.taskType;
    if (type != 'SCAN') return state.rawFormData?.actions?.isPermission ?? false;
    final totalAssets = state.workflowExecData.value.totalAssets;
    final numberOfUnScanned = state.workflowExecData.value.numberOfUnScanned;
    if (totalAssets == 0) return false;
    if (state.allMustScan) {
      // 全資産をスキャン
      return numberOfUnScanned == 0;
    } else {
      // 非全資産をスキャン
      bool isWFStatus;
      if (state.isRentalWorkflow.value) {
        // Rental类型WF必须得扫描过其中一个资产才可以承认
        isWFStatus = totalAssets > numberOfUnScanned;
      } else {
        isWFStatus = totalAssets >= numberOfUnScanned;
      }
      return isWFStatus;
    }
  }

  void approvalBtnDisabled(bool result) {
    state.isDisabled.value = result;
  }

  void _onScroll() {
    if (scrollController.hasClients) {
      // 添加检查
      final offset = scrollController.offset;
      state.showScrollToTop.value = offset > 100;
    }
  }
}
