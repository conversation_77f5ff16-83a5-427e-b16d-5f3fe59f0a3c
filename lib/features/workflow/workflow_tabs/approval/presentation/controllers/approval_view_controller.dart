import 'dart:convert';

import 'package:asset_force_mobile_v2/core/env/env_helper.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/models/approval_view_arguments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/state/new_application_form_ui_state.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_task_form_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/usecases/workflow_get_permission_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ApprovalViewController extends WorkflowCommonController {
  final WorkflowGetPermissionUseCase workflowGetPermissionUseCase;
  ApprovalViewController({
    required super.dialogService,
    required super.getUserRoleUseCaseUseCase,
    required super.workflowActionService,
    required this.workflowGetPermissionUseCase,
  }) {
    final pageParams = Get.arguments;

    if (pageParams is ApprovalViewArgumentsModel) {
      state.workflowName.value = pageParams.model.workflowName ?? '';
      state.fromPage = pageParams.fromPage;
    }
  }

  final state = NewApplicationFormUiState();
  final navigationService = Get.find<NavigationService>();
  @override
  void onReady() {
    super.onReady();
    if (state.firstLoad) {
      _initQueryParams();
      state.firstLoad = false;
    }
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }

  void _initQueryParams() async {
    if (Get.arguments is ApprovalViewArgumentsModel) {
      final rawModel = (Get.arguments as ApprovalViewArgumentsModel).model;
      state.wfModel = rawModel;
      // 入力情報
      state.stepName.value = rawModel.name ?? '';
      state.processInstanceId = rawModel.processInstanceId ?? '';
      state.processDefinitionId = rawModel.processDefinitionId ?? '';
      state.taskId = rawModel.id ?? '';
      state.assetTypeId = (rawModel.assetTypeIdWithFirstWf ?? 0).toString();
      state.assetTypeName.value = rawModel.firstWorkflowAssetTypeName ?? '';

      final type = await StorageUtils.get<String>(StorageUtils.keyFontSize);
      state.isBigFont.value = type != null && type.isNotEmpty ? type == 'big' : false;
      await loadData();

      state.hasAssetTypeName.value =
          rawModel.firstWorkflowAssetTypeName != null && rawModel.firstWorkflowAssetTypeName!.isNotEmpty;
      state.isAllowTalk.value = await workflowGetPermissionUseCase(389);
      if (state.isAllowTalk.value) {
        final accessToken = await StorageUtils.get<String>(StorageUtils.keyToken);
        final zoneId = await StorageUtils.get<String>(StorageUtils.keyZoneId);
        final baseLiveUrl = EnvHelper.getLiveBaseUrl();
        state.liveTalkListUrl.value =
            '$baseLiveUrl/af-integrate/room-list/workflow/${state.processInstanceId}?accessToken=${accessToken}&zoneId=${zoneId}';
      }
    } else {
      return;
    }
  }

  void loadDataDidEnter() {}

  Future<void> loadData() async {
    await showLoading();
    try {
      final result = await workflowActionService.getWorkflowTaskForm(state.processInstanceId, state.taskId, state: '1');
      state.permissions.value = result['permissions'];

      final resultData = WorkflowTaskFormResponseModel.fromJson(result['resultData']);
      final actions = resultData.actions;
      state.rawFormData = resultData;
      final cjs = resultData.commonJS ?? '';
      // 如果js从后台取得后不为空那么才把commonJS拼接到js
      if (resultData.workflowLogicScript != null && resultData.workflowLogicScript!.isNotEmpty) {
        state.jsString = cjs + resultData.workflowLogicScript!;
      }
      if (resultData.workflowScript != null && resultData.workflowScript!.isNotEmpty) {
        state.workflowScript = cjs + resultData.workflowScript!;
      }
      state.isCountingType.value = resultData.updateAmountShow ?? false;
      state.assetListId = (resultData.assetListId ?? '').toString();
      state.isPermission.value = actions?.isPermission ?? false;
      state.dataCount.value = (resultData.allAssetCount ?? 0).toString();
      state.isHasTantoushaF.value =
          resultData.assignDynamicFlag == true &&
          resultData.wfAssignDynamicTasks != null &&
          resultData.wfAssignDynamicTasks!.length > 0 &&
          state.isPermission.value;
      state.dynamicTaskInfo.value = resultData.wfAssignDynamicTasks ?? <WorkflowAssignDynamicTaskModel>[];
      state.dynamicTantousha.value = [];
      if (state.dynamicTantousha.length > 0) {
        for (final dynamicTaskInfoElement in state.dynamicTaskInfo) {
          if (dynamicTaskInfoElement.authorizerId != null) {
            if (dynamicTaskInfoElement.assignDynamicType == 'user') {
              //  user
              state.dynamicTantousha.add({
                'userId': dynamicTaskInfoElement.authorizerId,
                'lastName': dynamicTaskInfoElement.authorizerName,
                'firstName': '',
              });
            } else {
              //  group
              state.dynamicTantousha.add({
                'roleId': dynamicTaskInfoElement.authorizerId,
                'roleName': dynamicTaskInfoElement.authorizerName,
              });
            }
          }
        }
      }
      state.isFirstWfWithAssetList = resultData.isFirstWfWithAssetList ?? false;
      state.inputAssetListFlag.value = resultData.inputAssetListFlag ?? '';

      state.showClamin.value = actions?.possibleOfClaim ?? false;
      if (isAssetListEditable && resultData.assetListDatas != null) {
        for (var item in resultData.assetListDatas!) {
          if (state.isCountingType.value) {
            item.assetScannedCount = item.assetAmount;
          } else {
            item.assetScannedCount = 1;
          }
        }
      }
      updateFinishedScanAssetsCount();
      if (allMustScan) {
        state.isAllScanned.value = state.numberOfUnscanned == 0 ? true : false;
      } else {
        state.isAllScanned.value = state.totalAssets > state.numberOfUnscanned ? true : false;
      }
      await refreshButtons(resultData);
      state.isRentalWorkflow = resultData.isRentalWorkflow ?? false;
      // オリジナルフォームデータを保存する
      state.rawComment = jsonEncode(state.comments);
      state.rawWfDict = jsonEncode(result['secitonDic']);
      final workflowButtonNameDic = resultData.workflowButtonName;
      final extractedButtonResult = extractWFApprovalOrApplicationRaiseButtons(workflowButtonNameDic, true);
      state.buttonNameList.value = extractedButtonResult.raiseButtonList;
      state.turnBackButtonInfo = extractedButtonResult.turnBackButtonDic;
      state.vetoButtonInfo = extractedButtonResult.vetoButtonDic;
      // 取按钮数量
      state.buttonNum.value = numberButtonsFooterSection;
      state.assetDict.value = result['sectionDic'];

      state.previewPageLoadComplete.value = () {
        hideLoading();
      };
    } catch (e) {
      hideLoading();
    }
  }

  /// 戻る
  void backButton({bool saveData = false}) async {
    if (state.hasAssetTypeName.value) {
      // 修改过的资产汇总数组
      final assetListDataArray = <Map<String, dynamic>>[];

      state.chosenAssetMap.forEach((key, value) {
        // 检查 initScanCount 是否存在且不为空
        if (value.initScanCount != null) {
          assetListDataArray.add({
            'assetId': int.parse(key), // 将字符串键转换为数字
            'initSavedAssetAmount': value.initScanCount,
          });
        }
      });
      var taskDefKey = '';
      // 是否为复数人判断
      bool isPluralPeople = false;
      if (state.rawFormData?.actions != null) {
        taskDefKey = state.rawFormData?.actions?.taskDefKey ?? '';
        isPluralPeople = state.rawFormData?.actions?.isMyselfInputTask ?? false;
      }
      final assetList = await StorageUtils.get<String>(StorageUtils.keyTempEditAssetList);
      final isRestoreAllAssets = assetList != null && assetList != '[]';
      final isChangedAsset = state.editAssetList.length > 0;
      final isAssetListDataArray = assetListDataArray.length > 0;
      final isAllListCho = isRestoreAllAssets || isChangedAsset || isAssetListDataArray;
      // 是否为差し戻し
      final isSendBack = state.wfModel.isMyselfInputTask ?? false;
      // 在scan task的时候，确认按钮
      Future<void> backOkayFun() async {
        // 当用户操作了或者扫描了资产才去调用重置API，否则直接返回到上一个页面
        final formData = <String, dynamic>{};
        formData['processInstanceId'] = state.processInstanceId;
        formData['taskId'] = state.taskId;
        formData['assetListDataArrayJSON'] = jsonEncode(assetListDataArray);
        await workflowActionService.restoreAssetScanStateForAssignScan(formData);
      }

      // 在user task的时候，确认按钮
      Future<void> backOkayFunAtUserTask() async {
        final formData = <String, dynamic>{};
        formData['processInstanceId'] = state.processInstanceId;
        formData['taskId'] = state.taskId;
        formData['assetListDataJson'] = jsonEncode(state.editAssetList);
        if (isRestoreAllAssets) {
          final assetList = await StorageUtils.get<String>(StorageUtils.keyTempEditAssetList);
          formData['clearedAssetListDataJson'] = assetList;
        } else {
          formData['clearedAssetListDataJson'] = jsonEncode([]);
        }
        await workflowActionService.restoreAssetScanState(formData);
      }

      if (isAllListCho || (didFormChanged && state.isPermission.value)) {
        dialogService.show(
          content: '保存せずに終了しますか？入力したデータ・及びスキャンデータは破棄されます',
          cancelText: 'キャンセル',
          confirmText: 'OK',
          onConfirm: () async {
            if (isPluralPeople || taskDefKey.isEmpty || !isAllListCho) {
              Get.back();
            }
            if (taskDefKey.startsWith('UserTask') ||
                taskDefKey.startsWith('GroupTask') ||
                (taskDefKey.startsWith('InputTask') && isSendBack)) {
              await backOkayFunAtUserTask();
              return;
            }
            if (taskDefKey.startsWith('ScanTask')) {
              await backOkayFun();
              return;
            }
            Get.back();
          },
        );
      } else {
        Get.back();
      }
    } else {
      if (saveData && didFormChanged && !isTaskEnd) {
        saveTemporaryFormData(
          state.assetDict,
          state.comments,
          state.processDefinitionId,
          state.taskId,
          state.dynamicTaskInfo,
        );
      } else {
        navigationService.goBack(id: SharedNavBarEnum.workflow.navigatorId);
      }
    }
  }

  void rejectionButton(String buttonName) async {
    final isCheck = await checkAndGetData(state.assetDict);
    if (!isCheck) return;
    if (state.hasAssetTypeName.value) {
      confirmWithAssets(
        state.assetDict,
        state.comments,
        state.processInstanceId,
        state.taskId,
        state.rawFormData?.assetListDatas,
        state.rawFormData?.assetListTitle ?? '',
        state.wfModel.assetTypeIdWithFirstWf ?? 0,
        state.workflowScript,
        state.stepName.value,
        approvalBtnDisabled,
        false,
        false,
        buttonName,
      );
    } else {
      rejection(
        state.assetDict,
        state.comments,
        state.processInstanceId,
        state.taskId,
        state.workflowScript,
        state.stepName.value,
        approvalBtnDisabled,
        buttonName,
      );
    }
  }

  /// 差し戻し
  void sendBackButton(String buttonName) async {
    final isCheck = await checkAndGetData(state.assetDict);
    if (!isCheck) return;
    workflowSendBack(
      state.assetDict,
      state.comments,
      state.backDestination.value,
      state.taskId,
      state.workflowScript,
      state.stepName.value,
      approvalBtnDisabled,
      buttonName,
      backTabIndex: 2,
    );
  }

  void approvalBtnDisabled(bool result) {
    state.isDisabled.value = result;
  }

  /// 入力項目（フォーム値）を保存
  void saveTemporaryFromScanTask() async {
    if (state.hasAssetTypeName.value) {
      if (state.dynamicTantousha.length > 0 && state.dynamicTaskInfo.length > 0) {
        for (var i = 0; i < state.dynamicTaskInfo.length; i++) {
          final element = state.dynamicTaskInfo[i];
          if (element.assignDynamicType == 'group') {
            element.authorizerId = state.dynamicTantousha[i]['roleId'];
            element.authorizerName = state.dynamicTantousha[i]['roleName'];
          } else {
            element.authorizerId = state.dynamicTantousha[i]['userId'];
            element.authorizerName =
                state.dynamicTantousha[i]['lastName'] + ' ' + state.dynamicTantousha[i]['firstName'];
          }
        }
      }
    }
    await saveTemporaryFromScanTaskCommon(
      isCountingType: state.isCountingType.value,
      assetDict: state.assetDict,
      comments: state.comments,
      processInstanceId: state.processInstanceId,
      assetListId: state.assetListId,
      taskId: state.taskId,
      assignDynamicData: state.dynamicTaskInfo,
      isUserTask: state.rawFormData?.taskType == 'USER' || state.rawFormData?.taskType == 'GROUP',
    );
  }

  /// 資産一覧画面に遷移する
  void toAssetList(bool shouldClaim) async {
    if (shouldClaim) {
      final isValid = await validateAssetItems(state.assetDict);
      if (!isValid) return;
    }
    if (isAssetListEditable) {
      if (state.isPermission.value) {
        final navigationExtras = {
          'registeredAssetList': [],
          'assetDict': state.assetDict,
          'isUpdateAmount': state.isUpdateAmount,
          'isCountingType': state.isCountingType.value,
          'assetTypeId': state.assetTypeId.toString(),
          'assetTypeName': state.assetTypeName.value,
          'taskName': state.stepName.value,
          'workflowName': state.workflowName.value,
          'stepName': state.stepName.value,
          'processDefinitionId': state.processDefinitionId,
          'processInstanceId': state.processInstanceId,
          'taskId': state.taskId,
          'isUserTask': true,
          'workflowId': state.workflowId,
          'comments': state.comments,
          'workflowRaiseButtonNameList': state.buttonNameList,
          'dynamicTaskInfo': state.dynamicTaskInfo,
          'dynamicTantousha': state.dynamicTantousha,
          'workflowScript': state.workflowScript,
          'isFromNew': state.isFromNew,
          'fromPage': AutoRoutes.approvalView,
        };
        final result = await navigationService.navigateTo(
          AutoRoutes.workflowScanList,
          id: SharedNavBarEnum.workflow.navigatorId,
          arguments: navigationExtras,
        );
        state.dataCount.value = result['assetCount'].toString();
      } else {
        final queryParams = {
          'simplyMode': true,
          'allMustScan': allMustScan,
          'rawWorkFlowModel': state.wfModel,
          'shouldClaim': shouldClaim,
          'comments': state.comments,
          'dynamicTaskInfo': state.dynamicTaskInfo,
          'dynamicTantousha': state.dynamicTantousha,
          'wfDict': state.assetDict,
        };
        navigationService.navigateTo(
          AutoRoutes.approvalScanList,
          id: SharedNavBarEnum.workflow.navigatorId,
          arguments: queryParams,
        );
      }
      return;
    }
    if (state.rawFormData?.taskType != 'SCAN' && int.parse(state.dataCount.value) < 1) {
      dialogService.show(content: '参照可能な資産がございません。');
    }
    final queryParams = {
      'allMustScan': allMustScan,
      'rawWorkFlowModel': state.wfModel,
      'rawFormData': state.rawFormData,
      'shouldClaim': shouldClaim,
      'comments': state.comments,
      'dynamicTaskInfo': state.dynamicTaskInfo,
      'dynamicTantousha': state.dynamicTantousha,
      'wfDict': state.assetDict,
      'stepName': state.stepName.value,
      'confirmData': {},
    };
    if (state.rawFormData?.actions?.isMultiScanTask ?? false) {
      queryParams['assetList'] = state.rawFormData?.assetListDatas;
      navigationService.navigateTo(
        AutoRoutes.approvalNoClaimScanList,
        arguments: queryParams,
        id: SharedNavBarEnum.workflow.navigatorId,
      );
    } else {
      navigationService.navigateTo(
        AutoRoutes.approvalScanList,
        id: SharedNavBarEnum.workflow.navigatorId,
        arguments: queryParams,
      );
    }
  }

  /// 現物確認を解除
  void restartScanTask() async {
    updateScanTaskLock(
      state.taskId,
      '',
      '完了状態を解除',
      'not-yet',
      state.assetDict,
      state.comments,
      state.processInstanceId,
    );
  }

  /// 現物確認完了
  void scanTaskToFinish() async {
    updateScanTaskLock(
      state.taskId,
      'LOCK',
      '現物確認完了に変更',
      'not-yet',
      state.assetDict,
      state.comments,
      state.processInstanceId,
    );
  }

  /// スキャンした資産個数を更新する
  void updateFinishedScanAssetsCount() {
    state.totalAssets = state.rawFormData?.allAssetCount ?? 0;
    state.numberOfUnscanned = state.rawFormData?.noScanningAssetCount ?? 0;
    state.totalSavedAssetAmountCount = state.rawFormData?.totalSavedAssetAmountCount ?? 0;
    state.allAssetCountInKeywordSearch = state.rawFormData?.allAssetCount ?? 0;
    state.noScanningAssetCountInKeywordSearch = state.rawFormData?.noScanningAssetCount ?? 0;

    // 完成的资产数
    final finishScanningAssetCount = state.totalAssets - state.numberOfUnscanned;
    if (!state.isCountingType.value) {
      state.totalSavedAssetAmountCount = finishScanningAssetCount;
    }
    state.finishedScannedAssetCount = finishScanningAssetCount;
  }

  /// ボタンを更新する
  Future<void> refreshButtons(WorkflowTaskFormResponseModel resultData) async {
    state.rawFormData = resultData;
    state.isPermission.value = state.rawFormData?.actions?.isPermission ?? false;
    state.showClamin.value = state.rawFormData?.actions?.possibleOfClaim ?? false;
    if (resultData.taskType == 'SCAN') {
      state.showUnclamin.value = state.isPermission.value && !state.isMyselfInputTask;
    }
    if (state.rawFormData?.actions?.rejectionStatus == '1') {
      state.showRejectButton.value = state.rawFormData?.actions?.isPermission ?? false;
    }
    if (state.rawFormData?.actions?.sendBackStatus == '1' || state.rawFormData?.actions?.sendBackStatus == '2') {
      state.showSendBackButton.value = true && (state.rawFormData?.actions?.isPermission ?? false);
    }

    if (state.rawFormData?.actions?.sendBackDestination != null) {
      state.backDestination.value = state.rawFormData!.actions!.sendBackDestination!;
    }

    if (resultData.taskType != null) {
      state.isUserTask.value = resultData.taskType == 'USER' ? true : false;
    }
    state.footerButtonStatus.value =
        enableConfirmButton ||
        state.showRejectButton.value ||
        state.showSendBackButton.value ||
        state.showClamin.value ||
        state.showUnclamin.value;

    final status = getButtonStatus(resultData);
    state.showButton.value = status;
  }

  String getTantoushaInfoWithIndex(int index) {
    final dynamicTaskInfo = state.dynamicTaskInfo;
    final dynamicTantousha = state.dynamicTantousha;
    if (dynamicTaskInfo.length > 0 && dynamicTantousha.length == dynamicTaskInfo.length) {
      final chooseType = dynamicTaskInfo[index].assignDynamicType;
      final datum = dynamicTantousha[index] ?? null;
      if (datum != null) {
        if (chooseType == 'group') {
          return datum['roleName'];
        } else {
          return datum['lastName'] + ' ' + datum['firstName'];
        }
      } else {
        return '未選択';
      }
    }
    return '未選択';
  }

  String getOriginalForm(Map<String, List<AssetItemListModel>> assetDict, dynamic comments) {
    // 深拷贝assetDict
    final cloneAssetDict = Map<String, List<AssetItemListModel>>.from(
      assetDict.map(
        (key, value) => MapEntry(key, value.map((item) => AssetItemListModel.fromJson(item.toJson())).toList()),
      ),
    );

    // 深拷贝comments
    final cloneComments = jsonDecode(jsonEncode(comments));

    // 获取表单数据
    final formData = getFormData(cloneAssetDict, cloneComments);

    // 将FormData转换为Map
    final Map<String, dynamic> object = {};
    formData.forEach((key, value) {
      object[key] = value;
    });

    // 转换为JSON字符串
    return jsonEncode(object);
  }

  void approvalButton(WorkflowRaiseButtonModel? raiseDic) async {
    final isCheck = await checkAndGetData(state.assetDict);
    if (!isCheck) return;
    // 提交时用来判断
    if (!checkDynamicTaskCanSubmit(state.dynamicTaskInfo, state.dynamicTantousha)) {
      dialogService.show(content: '各ステップの承認者を選択してください。', cancelText: 'はい');
      return;
    } else {
      if (state.dynamicTaskInfo.isNotEmpty && state.dynamicTantousha.isNotEmpty) {
        for (var i = 0; i < state.dynamicTaskInfo.length; i++) {
          final element = state.dynamicTaskInfo[i];
          if (element.assignDynamicType == 'group') {
            element.authorizerId = state.dynamicTantousha[i]['roleId'];
            element.authorizerName = state.dynamicTantousha[i]['roleName'];
          } else {
            element.authorizerId = state.dynamicTantousha[i]['userId'];
            element.authorizerName =
                state.dynamicTantousha[i]['lastName'] + ' ' + state.dynamicTantousha[i]['firstName'];
          }
        }
      }
    }
    if (state.hasAssetTypeName.value) {
      if (state.rawFormData?.actions?.isMultiScanTask ?? false) {
        confirmWithMultiScanCompleteNoSubprocess(
          assetDict: state.assetDict,
          comments: state.comments,
          rawFormData: state.rawFormData,
          processInstanceId: state.processInstanceId,
          taskId: state.taskId,
          isApproval: true,
          assignDynamicData: state.dynamicTaskInfo,
          buttonName: raiseDic?.nameRaise ?? '',
          workflowScript: state.workflowScript,
          isFromAssetListPage: false,
          callBack: approvalBtnDisabled,
        );
      } else {
        if ((state.rawFormData?.actions?.scanTaskIsLocked ?? false) && state.rawFormData?.taskType == 'SCAN') {
          final isExecutionMarker = await confirmWithScanCompleteNoSubprocessWithAllMust(
            assetDict: state.assetDict,
            comments: state.comments,
            rawFormData: state.rawFormData,
            processInstanceId: state.processInstanceId,
            taskId: state.taskId,
            isApproval: true,
            assignDynamicData: state.dynamicTaskInfo,
            buttonName: raiseDic?.nameRaise ?? '',
            workflowScript: state.workflowScript,
            isFromAssetListPage: false,
            numberOfUnscanned: state.rawFormData?.noScanningAssetCount ?? 0,
            totalAssets: state.rawFormData?.allAssetCount ?? 0,
          );
          approvalBtnDisabled(isExecutionMarker);
        } else {
          confirmWithScanComplete(
            isAssetListEditable: isAssetListEditable,
            assetCount: state.dataCount.value,
            assetDict: state.assetDict,
            comments: state.comments,
            rawFormData: state.rawFormData,
            processInstanceId: state.processInstanceId,
            taskId: state.taskId,
            isApproval: true,
            assignDynamicData: state.dynamicTaskInfo,
            buttonName: raiseDic?.nameRaise ?? '',
            workflowScript: state.workflowScript,
            isFromAssetListPage: false,
          );
        }
      }
    } else {
      approval(
        state.assetDict,
        state.comments,
        state.processInstanceId,
        state.dynamicTaskInfo,
        state.taskId,
        state.workflowScript,
        raiseDic,
        approvalBtnDisabled,
      );
    }
  }

  void unClaim() async {
    try {
      await showLoading();
      final isCheck = await checkAndGetData(state.assetDict);
      if (!isCheck) return;
    } finally {
      hideLoading();
    }
    unClaimFormApproval(state.assetDict, state.comments, state.processInstanceId, state.taskId, approvalBtnDisabled);
  }

  /// その他
  void otherButtonSwitchOpenModal(BuildContext context) {
    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        CupertinoActionSheetAction _buildActionSheetAction(BuildContext context, WorkflowRaiseButtonModel raiseDic) {
          return CupertinoActionSheetAction(
            isDefaultAction: true,
            onPressed: () {
              Navigator.pop(context);
              approvalButton(raiseDic);
            },
            child: Text(
              raiseDic.nameRaise,
              style: const TextStyle(fontSize: 16, color: Colors.blueAccent, fontWeight: FontWeight.w300),
            ),
          );
        }

        final actionButtons = state.buttonNameList.isEmpty
            ? <WorkflowRaiseButtonModel>[]
            : state.buttonNameList.sublist(0, state.buttonNameList.length - 1);
        return CupertinoActionSheet(
          title: const Column(
            children: [
              Text('その他', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
              Text('その他の実行処理を選択してください'),
            ],
          ),
          actions: actionButtons.map((button) => _buildActionSheetAction(context, button)).toList(),
          cancelButton: CupertinoActionSheetAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text(
              'キャンセル',
              style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue, fontSize: 18.0),
            ),
          ),
        );
      },
    );
  }

  bool get didFormChanged {
    if (state.rawFormData == null) {
      return false;
    }
    final originalForm = getOriginalForm(state.assetDict, state.comments);
    final rawItemData = getOriginalForm(jsonDecode(state.rawWfDict), jsonDecode(state.rawComment));
    final isFormChanged = !(originalForm == rawItemData);
    var hasDynamic = false;
    if (state.isHasTantoushaF.value) {
      if (state.dynamicTantousha.length > 0 && state.dynamicTaskInfo.length > 0) {
        for (var i = 0; i < state.dynamicTaskInfo.length; i++) {
          final element = state.dynamicTaskInfo[i];
          if (element.assignDynamicType == 'group') {
            element.authorizerId = state.dynamicTantousha[i]['roleId'];
            element.authorizerName = state.dynamicTantousha[i]['roleName'];
          } else {
            element.authorizerId = state.dynamicTantousha[i]['userId'];
            element.authorizerName =
                state.dynamicTantousha[i]['lastName'] + ' ' + state.dynamicTantousha[i]['firstName'];
          }
        }
        hasDynamic = true;
      }
    }
    return isFormChanged || hasDynamic;
  }

  ///  タスク終わり
  bool get isTaskEnd {
    if (state.rawFormData == null) {
      return false;
    }
    return state.rawFormData?.taskType == 'END' || !(state.rawFormData?.actions?.isPermission ?? false);
  }

  /// 複数人スキャンか
  bool get isMultiScanTask {
    return state.rawFormData?.actions?.isMultiScanTask ?? false;
  }

  bool get scanTaskIsLocked {
    return state.rawFormData?.actions?.scanTaskIsLocked ?? false;
  }

  bool get isMyselfInputTask {
    return state.rawFormData?.actions?.isMyselfInputTask ?? false;
  }

  /// 資産リスト編集できるか
  bool get isAssetListEditable {
    return state.isFirstWfWithAssetList &&
        state.inputAssetListFlag.value == '1' &&
        (state.isUserTask.value || isGroupTask);
  }

  bool get isGroupTask {
    if (state.rawFormData != null) return state.rawFormData?.taskType == 'GROUP';
    return false;
  }

  /// 全て資産スキャン必要か
  bool get allMustScan {
    return state.rawFormData?.actions?.allMustScan ?? false;
  }

  /// 承認ボタンを更新する
  bool get enableConfirmButton {
    if (state.rawFormData?.actions?.isMultiScanTask == true) {
      return state.rawFormData != null &&
          (state.isUserTask.value || state.isAllScanned.value) &&
          (state.rawFormData?.actions?.isPermission ?? false);
    }

    if (state.rawFormData == null) {
      return state.isPermission.value;
    }

    final type = state.rawFormData?.taskType ?? '';

    if (type == 'USER') {
      return state.isPermission.value;
    }

    // 处理资产数量为 0 的情况
    if (int.parse(state.dataCount.value) == 0) {
      return state.isPermission.value;
    }

    if (allMustScan) {
      return state.isPermission.value && state.finishedScannedAssetCount == int.parse(state.dataCount.value);
    }

    if (state.isRentalWorkflow) {
      final isWFStatus = state.totalAssets > state.numberOfUnscanned;
      return state.isPermission.value && isWFStatus;
    }

    return state.isPermission.value && state.finishedScannedAssetCount >= 0;
  }

  /// 底部按钮总数
  int get numberButtonsFooterSection {
    // 提出/承认
    final raiseButtonList = state.buttonNameList;
    // 差戻し
    final turnBackButtonDic = state.turnBackButtonInfo;
    // 否决
    final vetoButtonDic = state.vetoButtonInfo;
    if (raiseButtonList.length == 0 && turnBackButtonDic == null && vetoButtonDic == null) {
      // 正常情况下不可能为空
      return 0;
    }
    int allButtonNum = 0;
    // 默认第一个是一时保存
    if (state.isPermission.value) {
      allButtonNum += 1;
    }
    if (turnBackButtonDic != null && state.showSendBackButton.value) {
      // 差し戻し
      allButtonNum += 1;
    }
    if (vetoButtonDic != null && state.showRejectButton.value) {
      // 否决
      allButtonNum += 1;
    }
    // 承认或者提出按钮总数
    allButtonNum += raiseButtonList.length;

    return allButtonNum;
  }

  @override
  bool shouldShowNavigationBar() {
    return false;
  }

  @override
  void onInit() {
    super.onInit();

    configureNavigationBarVisibility();
  }

  void onBack() {
    if (state.fromPage == ApprovalSource.deeplink) {
      navigationService.navigateUntil(
        AutoRoutes.appTabWorkflowTabs,
        id: SharedNavBarEnum.workflow.navigatorId,
        arguments: {'initialIndex': 2},
      );
      return;
    }
    if (Get.isRegistered<TabsController>()) {
      Get.find<TabsController>().showBar();
    }
    navigationService.goBack(id: SharedNavBarEnum.workflow.navigatorId);
  }
}

/// 记录用户增加资产数量
class ChosenAsseDataInterface {
  int updatedNum; // updatedNum修改后数量
  int? initScanCount; // 初期回数
  bool isCurrentPageTheAsset; // 资产是否在当前分页中（为了防止扫描时候的资产不在当前分页中）true=>当前页包含资产
  ChosenAsseDataInterface({this.initScanCount, required this.isCurrentPageTheAsset, required this.updatedNum});
}
