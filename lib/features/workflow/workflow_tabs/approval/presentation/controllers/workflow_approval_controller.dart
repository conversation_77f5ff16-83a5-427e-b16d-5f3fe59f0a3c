import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/domain/usecases/get_approval_workflow_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/models/approval_view_arguments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_search_task_condition.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/state/workflow_common_ui_state.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_tabs/presentation/controllers/workflow_tabs_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WorkflowApprovalController extends WorkflowCommonController {
  final GetApprovalWorkflowListUseCase getApprovalWorkflowListUseCase;
  WorkflowApprovalController(
    this.getApprovalWorkflowListUseCase, {
    required super.dialogService,
    required super.getUserRoleUseCaseUseCase,
    required super.workflowActionService,
  });

  final scrollController = ScrollController();
  final WorkflowCommonUIState state = WorkflowCommonUIState();

  @override
  void onInit() {
    super.onInit();
    scrollController.addListener(_onScroll);
    // 监听排序状态变化
    final workflowTabsController = Get.find<WorkflowTabsController>();
    ever(workflowTabsController.isApprovalAscending, (bool isAscending) {
      if (isAscending) {
        state.order.value = Order.ascend;
      } else {
        state.order.value = Order.descend;
      }
      state.filteredItems.value = sortByDeadlineDate(state.items, state.order.value);
    });
  }

  @override
  void onReady() {
    super.onReady();
    final params = Get.arguments;
    if (params != null) {
      defaultForDoneSearchCondition('approval');
      defaultForAllSearchCondition('approval');
      final name = params['name'];
      if (name != null && name != '') {
        Get.snackbar('', '$nameしました。', duration: const Duration(seconds: 1));
      }
    }
    loadData();
  }

  @override
  void onClose() {
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    super.onClose();
  }

  Future<void> doRefresh() async {
    await refreshData();
  }

  Future<void> refreshData() async {
    await loadData();
  }

  void onSearchChanged(String query) {
    state.currentSearchQuery.value = query;
    _applySearchAndFilter(query);
  }

  void openNotYet() {
    state.progress.value = 'not-yet';
    loadData();
  }

  void openDone() {
    state.progress.value = 'done';
    loadData();
  }

  void openAll() {
    state.progress.value = 'all';
    loadData();
  }

  void onItemTap(WorkflowEngineTaskModel item) async {
    // 跳转到详情页面，可根据需求调整路由和参数
    // Get.toNamed('/workflow/details', arguments: item);
    if (item.taskDefKey != null && item.taskDefKey!.contains('-')) {
      final taskName = item.taskDefKey!.split('-')[0];
      if (taskName == 'ActionTask' ||
          taskName == 'RPATask' ||
          taskName == 'ServiceTask' ||
          taskName == 'AmountActionTask' ||
          taskName == 'JavaDelegateServiceTask') {
        dialogService.show(title: '提示', content: 'システム実行中につき、時間をおいてから再度ご参照ください。');
        return;
      }
    }
    workflowActionService.clearLocalData();

    final navigationService = Get.find<NavigationService>();
    await navigationService.navigateTo(
      AutoRoutes.approvalView,
      arguments: ApprovalViewArgumentsModel(model: item),
      id: SharedNavBarEnum.workflow.navigatorId,
    );
    await refreshData();
  }

  Future<void> loadData() async {
    try {
      state.isInitialLoading.value = true;
      final isFromPage = this.isFromPage();
      final searchConditionDataList =
          StorageUtils.get<String>(StorageUtils.keySearchConditionList + isFromPage) ?? '[]';
      // 使用 use case 获取数据
      final workflowEngineSearchTaskCondition = WorkflowEngineSearchTaskCondition();
      workflowEngineSearchTaskCondition.from = '1';
      String status;
      //　作成中
      if (state.progress.value == 'not-yet') {
        workflowEngineSearchTaskCondition.processStateCondition = 'UNAPPROVE';
        status = 'UNAPPROVE';
        // 申請済み
      } else if (state.progress.value == 'done') {
        status = 'APPROVED';
        workflowEngineSearchTaskCondition.processStateCondition = 'APPROVED';
        // 全て
      } else {
        status = 'ALL';
        workflowEngineSearchTaskCondition.processStateCondition = 'ALL';
      }
      List<WorkflowEngineTaskModel> tasks;
      final formDta = {'processStateCondition': status, 'searchConditions': searchConditionDataList, 'from': '1'};
      if (state.progress.value == 'not-yet') {
        tasks = await getApprovalWorkflowListUseCase(workflowEngineSearchTaskCondition);
      } else {
        tasks = await getApprovalWorkflowListUseCase(formDta);
      }
      state.items.value = tasks;
      state.filteredItems.value = sortByDeadlineDate(state.items, state.order.value);
      if (state.progress.value == 'not-yet') {
        state.unApprovalCount.value = state.items.length;
      }
    } catch (e, stackTrace) {
      handleException(e, stackTrace);
    } finally {
      state.isInitialLoading.value = false;
    }
  }

  /// 匹配搜索内容
  bool _matchesQuery(WorkflowEngineTaskModel item, String query) {
    final lowerQuery = query.toLowerCase();
    final workflowTypeMatches =
        item.workflowType != null && formatSearch(item.workflowType!.toLowerCase()).contains(lowerQuery);
    final processInstanceIdMatches =
        item.processInstanceId != null && formatSearch(item.processInstanceId!.toLowerCase()).contains(lowerQuery);
    final wfNameMatches = formatSearch((item.wfName ?? '-').toLowerCase()).contains(lowerQuery);
    final stateMatches = formatSearch((item.state ?? '').toLowerCase()).contains(lowerQuery);
    final deadlineDateMatches =
        item.deadlineDate != null && formatSearch(item.deadlineDate!.toLowerCase()).contains(lowerQuery);

    return workflowTypeMatches || processInstanceIdMatches || wfNameMatches || stateMatches || deadlineDateMatches;
  }

  /// 输出匹配的内容
  void _applySearchAndFilter(String query) {
    List<WorkflowEngineTaskModel> sortList;
    if (query.isEmpty) {
      sortList = state.filteredItems.value = List.from(state.items);
    } else {
      sortList = state.items.where((item) => _matchesQuery(item, query)).toList();
    }
    state.isEmptyResult.value = sortList.isEmpty;
    state.filteredItems.value = sortByDeadlineDate(sortList, state.order.value);
  }

  /// どの画面から遷移してきた
  String isFromPage() {
    String isFromPage = '';
    if (state.progress.value == 'done') {
      isFromPage = 'approval';
    } else if (state.progress.value == 'all') {
      isFromPage = 'approval-all';
    }
    return isFromPage;
  }

  void _onScroll() {
    if (scrollController.hasClients) {
      // 添加检查
      final offset = scrollController.offset;
      state.showScrollToTop.value = offset > 100;
    }
  }
}
