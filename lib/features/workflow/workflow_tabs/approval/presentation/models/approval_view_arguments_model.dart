import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_task_model.dart';

/// 承认画面跳转参数模型
class ApprovalViewArgumentsModel {
  /// 工作流任务模型
  WorkflowEngineTaskModel model;

  /// 来源页面
  ApprovalSource fromPage;

  /// 构造函数
  /// [model] 必传，工作流任务对象
  /// [fromPage] 来源页面，默认为 [ApprovalSource.notSpecified]
  ApprovalViewArgumentsModel({required this.model, this.fromPage = ApprovalSource.notSpecified});
}

/// 承认页面来源枚举
enum ApprovalSource {
  /// 从 deeplink 打开
  deeplink('deeplink'),

  /// 未指定来源
  notSpecified('notSpecified');

  /// 枚举对应的字符串值
  final String value;

  /// 构造函数
  const ApprovalSource(this.value);
}
