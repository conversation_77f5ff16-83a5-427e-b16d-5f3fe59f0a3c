import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/scroll_to_top_button.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/bindings/approval_scan_list_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/controllers/approval_scan_list_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/base_scan_list_widget.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_build_row.dart';
import 'package:asset_force_mobile_v2/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/approval/scan_list', isConst: false, bindings: [ApprovalScanListBinding])
class ApprovalScanListPage extends GetView<ApprovalScanListController> {
  final RxInt selectedIndex = 0.obs;
  final _isExpanded = false.obs;
  @override
  Widget build(BuildContext context) {
    final TextEditingController _searchController = TextEditingController();
    final _showClearButton = false.obs;
    final List<String> titles = ['すべて', 'スキャン済', '未スキャン'];
    final List<ScanTabType> tabTypes = [ScanTabType.all, ScanTabType.scanned, ScanTabType.unScanned];

    return Scaffold(
      appBar: AppBar(
        title: const Text('資産情報'),
        leading: IconButton(
          icon: const Icon(size: 37, Icons.chevron_left),
          onPressed: () {
            Get.back(id: SharedNavBarEnum.workflow.navigatorId);
          },
        ),
        centerTitle: true,
        elevation: 0,
        actions: [
          Obx(() {
            if (!controller.state.showActions.value) return const SizedBox.shrink();
            return Row(
              children: [
                controller.state.showUnclaimButton.value
                    ? InkWell(
                        onTap: controller.unclaimButton,
                        child: SvgPicture.asset(
                          Assets.iconsIconSendBack,
                          width: 24,
                          height: 24,
                          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                        ),
                      )
                    : const SizedBox(),
                controller.state.showSendBackButton.value
                    ? InkWell(
                        onTap: controller.sendBackButton,
                        child: SvgPicture.asset(
                          Assets.iconsIconRelease,
                          width: 24,
                          height: 24,
                          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                        ),
                      )
                    : const SizedBox.shrink(),
                InkWell(
                  onTap: controller.toNoSubprocessScan,
                  child: SvgPicture.asset(
                    Assets.iconsIconCamera,
                    width: 24,
                    height: 24,
                    colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                  ),
                ),
                const SizedBox(width: 8),
              ],
            );
          }),
        ],
      ),
      body: Stack(
        children: [
          NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification scrollInfo) {
              if (!controller.state.moreThenLimit.value &&
                  !controller.loadStatus.value &&
                  scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
                // 触发加载更多
                controller.onLoadMoreData();
              }
              return false;
            },
            child: CustomScrollView(
              controller: controller.scrollController,
              slivers: [
                SliverToBoxAdapter(
                  child: Obx(() {
                    final double tabBarWidth = MediaQuery.of(context).size.width - 20;
                    final double tabWidth = tabBarWidth / 3;
                    return controller.state.showFooter.value
                        ? Container(
                            margin: const EdgeInsets.only(top: 10, right: 10, left: 10),
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.white, width: 1),
                            ),
                            child: Stack(
                              children: [
                                AnimatedPositioned(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.ease,
                                  left: tabWidth * selectedIndex.value,
                                  top: 0,
                                  bottom: 0,
                                  child: Container(
                                    width: tabWidth,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(selectedIndex.value == 0 ? 8 : 0),
                                        bottomLeft: Radius.circular(selectedIndex.value == 0 ? 8 : 0),
                                        topRight: Radius.circular(selectedIndex.value == 2 ? 8 : 0),
                                        bottomRight: Radius.circular(selectedIndex.value == 2 ? 8 : 0),
                                      ),
                                    ),
                                  ),
                                ),
                                Row(
                                  children: List.generate(5, (i) {
                                    if (i.isOdd) {
                                      // 分隔线
                                      return Container(width: 1, height: 72, color: Colors.white);
                                    } else {
                                      final int index = i ~/ 2;
                                      final bool isSelected = selectedIndex.value == index;
                                      return Expanded(
                                        child: GestureDetector(
                                          behavior: HitTestBehavior.opaque,
                                          onTap: () {
                                            controller.onTabChanged(tabTypes[index]);
                                            selectedIndex.value = index;
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(vertical: 10),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  titles[index],
                                                  style: TextStyle(
                                                    color: isSelected ? const Color(0xFF0B3E86) : Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Container(
                                                  width: 28,
                                                  height: 28,
                                                  alignment: Alignment.center,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                      color: isSelected ? const Color(0xFF0B3E86) : Colors.white,
                                                    ),
                                                  ),
                                                  child: Text(
                                                    controller.state.tabCounts[index].toString(),
                                                    style: TextStyle(
                                                      color: isSelected ? const Color(0xFF0B3E86) : Colors.black,
                                                      fontSize: 14,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );
                                    }
                                  }),
                                ),
                              ],
                            ),
                          )
                        : const SizedBox.shrink();
                  }),
                ),
                // 搜索框
                SliverToBoxAdapter(
                  child: Container(
                    margin: const EdgeInsets.only(top: 10, left: 10, right: 10),
                    height: 40,
                    width: double.infinity,
                    child: Obx(() {
                      return TextField(
                        controller: _searchController,
                        onSubmitted: (value) {
                          controller.doSearch();
                        },
                        decoration: InputDecoration(
                          hintText: 'キーワードで検索',
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              if (_showClearButton.value)
                                IconButton(
                                  icon: const Icon(Icons.clear, size: 20),
                                  splashRadius: 20,
                                  onPressed: () {
                                    _searchController.clear();
                                    _showClearButton.value = false;
                                    controller.clearInputKey();
                                  },
                                ),
                              Container(
                                decoration: const BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(8),
                                    bottomRight: Radius.circular(8),
                                  ),
                                  color: Color(0xFF0B3E86),
                                ),
                                padding: const EdgeInsets.all(8),
                                child: GestureDetector(
                                  onTap: controller.doSearch,
                                  child: SvgPicture.asset(
                                    Assets.iconsIconSearch,
                                    colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        style: const TextStyle(color: Colors.black, fontSize: 16),
                        onChanged: (value) {
                          _showClearButton.value = value.isNotEmpty;
                          controller.state.dataNumAndNextPage.keyword = value;
                        },
                      );
                    }),
                  ),
                ),
                const SliverToBoxAdapter(child: SizedBox(height: 10)),
                // 列表
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  sliver: Obx(
                    () => SliverList(
                      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
                        if (index < controller.state.filterListTemp.length) {
                          final TextEditingController editingController = TextEditingController();
                          editingController.text = controller.state.filterListTemp[index].assetScanedCount.value
                              .toString();
                          return Stack(
                            clipBehavior: Clip.none,
                            children: [
                              BaseScanListWidget(
                                asset: controller.state.filterListTemp[index],
                                isExpanded: _isExpanded,
                                scannedColor: Colors.white,
                                hasScanTask: controller.state.hasScanTask.value,
                                isCountingType: controller.state.isCountingType.value,
                                leadingWidget: const SizedBox(),
                                bottomWidget:
                                    controller.state.isCountingType.value &&
                                        controller.state.hasScanTask.value &&
                                        controller.state.showUnclaimButton.value
                                    ? _bottomNumber(index)
                                    : const SizedBox(),
                                endWidget: [
                                  if (controller.state.isRentalWorkflow.value ||
                                      (controller.state.isCountingType.value && controller.state.hasScanTask.value))
                                    Obx(() {
                                      return WorkflowBuildRow(
                                        title: '数量',
                                        value:
                                            '${controller.state.filterListTemp[index].assetScanedCount.value} / ${controller.state.filterListTemp[index].assetAmount.value}',
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                      );
                                    }),
                                  if (!controller.state.isRentalWorkflow.value &&
                                      (controller.state.isCountingType.value && !controller.state.hasScanTask.value))
                                    Obx(() {
                                      return WorkflowBuildRow(
                                        title: '数量',
                                        value: '${controller.state.filterListTemp[index].assetScanedCount.value}',
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                      );
                                    }),
                                  // 占位
                                  const SizedBox.shrink(),
                                ],
                              ),
                              Positioned(
                                top: -6, // 允许溢出
                                right: -8,
                                child: Obx(() {
                                  return Container(
                                    width: 26,
                                    height: 26,
                                    decoration: BoxDecoration(
                                      color: controller.state.filterListTemp[index].scanState == '1'
                                          ? const Color(0xFF0B3E86)
                                          : Colors.grey, // 圆圈背景色
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Center(
                                      child: Icon(
                                        Icons.check,
                                        color: Colors.white,
                                        size: 18, // 对号icon大小
                                      ),
                                    ),
                                  );
                                }),
                              ),
                            ],
                          );
                        } else if (!controller.state.moreThenLimit.value) {
                          // 显示加载中
                          return const Padding(
                            padding: EdgeInsets.symmetric(vertical: 16),
                            child: Center(child: CircularProgressIndicator()),
                          );
                        } else {
                          // 显示footer
                          return const Padding(
                            padding: EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                              '全件表示されました',
                              style: TextStyle(color: Colors.white),
                              textAlign: TextAlign.center,
                            ),
                          );
                        }
                      }, childCount: controller.state.filterListTemp.length + 1),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            right: 16,
            bottom: 16,
            child: Obx(
              () => AnimatedOpacity(
                opacity: controller.state.showScrollToTop.value ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 200),
                child: Visibility(
                  visible: controller.state.showScrollToTop.value,
                  child: ScrollToTopButton(
                    scrollToTop: () {
                      controller.scrollToTop(controller.scrollController);
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Obx(() {
        final List<Widget> buttons = [
          if (controller.state.isPermissionWF.value)
            WorkflowButton(text: '一時保存', onTapCallback: controller.saveTemporaryFromScanTask),
          if (controller.state.showRejectButton.value)
            WorkflowButton(text: '否決', onTapCallback: controller.rejectionButton),
          if (controller.state.showSendBackButton.value)
            WorkflowButton(text: '差戻し', onTapCallback: controller.sendBackButton),
          if (controller.state.showUnclaimButton.value)
            WorkflowButton(text: '解除', onTapCallback: controller.unclaimButton),
          if (controller.state.showClaimButton.value)
            WorkflowButton(
              text: '実行',
              textColor: Colors.white,
              buttonColor: const Color(0xFF0B3E86),
              onTapCallback: controller.claim,
            ),
          if (controller.state.showConfirmButton.value)
            WorkflowButton(
              text: '承認',
              textColor: Colors.white,
              buttonColor: const Color(0xFF0B3E86),
              onTapCallback: controller.approvalButton,
            ),
        ];

        return controller.state.showFooter.value
            ? Container(
                color: Colors.white,
                child: SafeArea(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Row(
                      mainAxisAlignment: buttons.length == 1 ? MainAxisAlignment.end : MainAxisAlignment.spaceBetween,
                      children: buttons,
                    ),
                  ),
                ),
              )
            : const SizedBox.shrink();
      }),
    );
  }

  Widget _bottomNumber(int index) {
    final asset = controller.state.filterListTemp[index];
    final editController = TextEditingController(text: asset.assetScanedCount.value.toString());
    ever(asset.assetScanedCount, (count) {
      if (editController.text != count.toString()) {
        editController.text = count.toString();
        // 保持光标在末尾
        editController.selection = TextSelection.fromPosition(TextPosition(offset: editController.text.length));
      }
    });
    return Container(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Row(
            children: [
              InkWell(
                onTap: () async {
                  await controller.updateAssetScanStatus(asset, ManualType.subtraction);
                  // todo 旧
                  // editController.text = '${controller.state.filterListTemp[index].assetScanedCount.value}';
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.blue.shade800),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(child: Icon(Icons.remove, color: Colors.blue.shade800)),
                ),
              ),
              Expanded(
                child: Container(
                  height: 50,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  child: Center(
                    child: TextField(
                      controller: editController,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      textAlignVertical: TextAlignVertical.center,
                      style: const TextStyle(fontSize: 16),
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(vertical: 0),
                        filled: true,
                        fillColor: Colors.white,
                        border: _inputBorderStyle,
                        enabledBorder: _inputBorderStyle,
                        focusedBorder: _inputBorderStyle,
                      ),
                      onChanged: (value) async {
                        await controller.updateAssetScanStatus(asset, ManualType.input, event: value);
                      },
                    ),
                  ),
                ),
              ),
              InkWell(
                onTap: () async {
                  await controller.updateAssetScanStatus(asset, ManualType.addition);
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppTheme.lightTheme.primaryColor),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(child: Icon(Icons.add, color: Colors.blue.shade800)),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  InputBorder get _inputBorderStyle => OutlineInputBorder(
    borderRadius: BorderRadius.circular(8),
    borderSide: BorderSide(color: Colors.grey.shade400, width: 1),
  );
}
