import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/controllers/approval_scan_list_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_asset_column_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_task_form_response_model.dart';
import 'package:get/get.dart';

typedef UpdateAssetActionCallback = Future<void> Function(String processInstanceId, String currentTaskId);
typedef UpdateIDActionCallback = Future<void> Function(String processInstanceId, String currentTaskId);
typedef SetHasBeenChangedAssetCallback = Future<void> Function(Map<String, ChosenAsseDataInterface> chosenAssetMap);

class ApprovalScanListState {
  //页面回调
  UpdateAssetActionCallback? updateIDAction;
  UpdateAssetActionCallback? updateAssetAction;
  SetHasBeenChangedAssetCallback? setHasBeenChangedAsset;
  // UI
  RxList<RegisteredAssetListModel> filterListTemp = <RegisteredAssetListModel>[].obs;

  RxBool moreThenLimit = false.obs; // 是否还有下一页更多数据

  WorkflowEngineTaskModel wfModel = WorkflowEngineTaskModel();

  RxString subformTitle = 'フォーム入力'.obs;

  // 扫描用
  bool isFromLocationSettingPage = false;
  String scanType = '';

  Rx<WorkflowExecDataInterface> workflowExecData = WorkflowExecDataInterface(
    totalAssets: 0,
    numberOfUnScanned: 0,
    totalSavedAssetAmountCount: 0,
    noScanningAssetCountInKeywordSearch: 0,
    allAssetCountInKeywordSearch: 0,
    scannedAssetCount: 0,
  ).obs; // wf中的资产数量

  // 担当者選択情報
  RxList<WorkflowAssignDynamicTaskModel> dynamicTaskInfo = <WorkflowAssignDynamicTaskModel>[].obs;
  RxList dynamicTantousha = [].obs;

  bool allMustScan = false;

  WorkflowTaskFormResponseModel? rawFormData = null;

  String workflowScript = '';

  // コメント
  RxList<CommentsModel> comments = <CommentsModel>[].obs;

  String stepName = '';

  RxBool simplyMode = false.obs; // 是否隐藏右侧图标

  String processInstanceId = '';

  String taskId = '';

  List<WorkflowAssetColumnModel> assetColumns = [];

  Map<int, AssetItemListModel> assetTypeItemMap = {};

  Map<String, List<AssetItemListModel>> wfDict = <String, List<AssetItemListModel>>{};

  String identityCodeDisplayName = '';
  String assetNameDisplayName = '';

  RxBool isCountingType = false.obs; // 個数管理場合

  RxBool hasScanTask = false.obs; // スキャンタスクあるか

  RxBool isPermissionWF = false.obs; // 是否有权限一时保存
  bool isPermission = false;

  /// button状态
  RxBool showSendBackButton = false.obs;
  RxBool showRejectButton = false.obs;
  RxBool showUnclaimButton = false.obs;
  RxBool showClaimButton = false.obs;
  RxBool showConfirmButton = false.obs;

  /// 滚动到顶部button
  RxBool showScrollToTop = false.obs;

  String backDestination = '';

  RxBool isRentalWorkflow = false.obs;

  RxBool showFooter = true.obs; // 展示底部按钮

  RxBool showActions = false.obs;

  final RxList<int> tabCounts = <int>[0, 0, 0].obs; // 扫描数量

  final backupEnterScannedCount = <String, int>{};

  final WFDataNumAndNextPageType dataNumAndNextPage = WFDataNumAndNextPageType(
    processInstanceId: '',
    taskId: '',
    skip: 0,
    rows: 20,
    keyword: '',
    scanState: ScanTabType.all,
  );

  // 按钮不可点击态
  RxBool isDisabled = false.obs;

  // 只存被选中的资产（扫描选中或者手动增加.key => assetId资产ID,isScan是否为扫描增加的资产，
  // initialNum初始数量,updatedNum修改后数量,scanNum如果isScan为true将记录扫描了多少次
  final chosenAssetMap = <String, ChosenAsseDataInterface>{};
}

/// input search 最终api传递数据
class WFDataNumAndNextPageType {
  String processInstanceId;
  String taskId;
  int skip;
  int rows;
  String keyword;
  ScanTabType scanState;
  WFDataNumAndNextPageType({
    required this.processInstanceId,
    required this.taskId,
    required this.skip,
    required this.rows,
    required this.keyword,
    this.scanState = ScanTabType.all,
  });
}

class WorkflowExecDataInterface {
  int totalAssets; // 总共多少条资产
  int numberOfUnScanned; // 还未处理多少条资产
  int totalSavedAssetAmountCount; // 所有已扫描过的资产中数量总和
  int noScanningAssetCountInKeywordSearch; // 未スキャン資産の件数（キーワード検索結果）
  int allAssetCountInKeywordSearch; // 全ての資産の件数（キーワード検索結果）
  int scannedAssetCount; // 已扫描的资产数

  WorkflowExecDataInterface({
    required this.totalAssets,
    required this.numberOfUnScanned,
    required this.totalSavedAssetAmountCount,
    required this.noScanningAssetCountInKeywordSearch,
    required this.allAssetCountInKeywordSearch,
    required this.scannedAssetCount,
  });
}

class ChosenAsseDataInterface {
  int updatedNum; // updatedNum修改后数量
  int? initScanCount; // 初期回数
  bool isCurrentPageTheAsset; // 资产是否在当前分页中（为了防止扫描时候的资产不在当前分页中）true=>当前页包含资产
  ChosenAsseDataInterface({required this.updatedNum, this.initScanCount, required this.isCurrentPageTheAsset});
}
