import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_button_name_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_amount_process_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_form_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'workflow_new_form_model.g.dart';

@JsonSerializable(explicitToJson: true)
class WorkflowNewFormModel {
  int? code;
  String? msg;
  List<WorkflowEngineInputTaskFormItem>? form;
  int? autoFetchSearchId;
  String? stepName;
  WorkflowEngineAmountProcessModel? workflowEngineAmountProcess;
  String? inputAssetListFlag;
  bool? groupSectionFlg;
  String? assignDynamicType;
  bool? updateAmountShow;
  WorkflowButtonNameModel? workflowButtonName;
  String? workflowScript;
  String? workflowLogicScript;
  String? commonJS;

  WorkflowNewFormModel({
    this.assignDynamicType,
    this.autoFetchSearchId,
    this.groupSectionFlg,
    this.form,
    this.inputAssetListFlag,
    this.stepName,
    this.updateAmountShow,
    this.workflowEngineAmountProcess,
    this.commonJS,
    this.workflowButtonName,
    this.workflowLogicScript,
    this.workflowScript,
  });

  factory WorkflowNewFormModel.fromJson(Map<String, dynamic> json) => _$WorkflowNewFormModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkflowNewFormModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class WorkflowEngineInputTaskFormItem {
  bool? formField;
  WorkflowFormModel? workflowForm;
  dynamic variable;
  WorkflowEngineInputTaskFormItem({this.formField, this.workflowForm, this.variable});

  factory WorkflowEngineInputTaskFormItem.fromJson(Map<String, dynamic> json) =>
      _$WorkflowEngineInputTaskFormItemFromJson(json);

  Map<String, dynamic> toJson() => _$WorkflowEngineInputTaskFormItemToJson(this);
}
