// import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
// import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/data/models/process_definition_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/data/models/workflow_new_form_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/domain/repositories/workflow_new_application_repository.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';

class WorkflowNewApplicationRepositoryImpl with RepositoryErrorHandler implements WorkflowNewApplicationRepository {
  final DioUtil dioUtil;

  WorkflowNewApplicationRepositoryImpl({required this.dioUtil});

  @override
  Future<List<ProcessDefinitionModel>> getNewWorkflowList() async {
    return executeRepositoryTask<List<ProcessDefinitionModel>>(() async {
      final response = await dioUtil.get('${GlobalVariable.newApplicationWorkflowList}?workflowType=0');
      final data = response.data as Map<String, dynamic>?;
      if (data == null) {
        LogUtil.w('getNewWorkflowList response data is null');
        return [];
      }
      final List<dynamic>? processDefinitionsJson = data['processDefinitions'] as List<dynamic>?;
      if (processDefinitionsJson == null) {
        LogUtil.w('processDefinitions not found or is not a list in response');
        return [];
      }
      return processDefinitionsJson.map((json) => ProcessDefinitionModel.fromJson(json)).toList();
    }, 'Error fetching workflow list');
  }

  @override
  Future<WorkflowNewFormModel> getNewWorkflowForm(String processDefinitionId) async {
    return executeRepositoryTask<WorkflowNewFormModel>(() async {
      final result = await dioUtil.get(
        GlobalVariable.workFlowStartForm,
        queryParams: {'processDefinitionId': processDefinitionId},
      );
      final data = result.data;
      return WorkflowNewFormModel.fromJson(data);
    }, 'Error fetching new workflow form');
  }
}
