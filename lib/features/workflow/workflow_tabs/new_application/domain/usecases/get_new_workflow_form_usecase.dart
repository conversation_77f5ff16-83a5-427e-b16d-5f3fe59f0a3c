import 'dart:convert';

import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/data/models/workflow_new_form_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/domain/repositories/workflow_new_application_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_form_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:get/get.dart';

class GetNewWorkflowFormUseCase implements UseCase<GetNewWorkflowFormResponse, String> {
  final WorkflowNewApplicationRepository workflowNewApplicationRepository;
  final WorkflowListRepository workflowListRepository;
  final UserRepository userRepository;
  GetNewWorkflowFormUseCase({
    required this.workflowNewApplicationRepository,
    required this.workflowListRepository,
    required this.userRepository,
  });
  final controller = Get.find<WorkflowCommonController>();
  @override
  Future<GetNewWorkflowFormResponse> call(processDefinitionId) async {
    final resultForNewApplication = await workflowNewApplicationRepository.getNewWorkflowForm(processDefinitionId);
    final resultForgetTaskInfo = await workflowListRepository.getWorkflowAssignDynamicTaskListByTaskId(
      processDefinitionId,
    );
    await userRepository.getUserRole();
    final form = resultForNewApplication.form;
    final dict = <String, List<AssetItemListModel>>{};
    final tempArray = <String>[];
    if (form != null && form.isNotEmpty) {
      form.map((workflow) {
        //Optionオブジェクト設定
        workflow.workflowForm?.optionObject = jsonDecode(workflow.workflowForm?.option ?? '{}');
        if (workflow.workflowForm?.mobileFlg == '1') {
          //マスタ初期値のIDをmasterIdに保存する
          if (workflow.workflowForm?.itemType == 'master' &&
              workflow.workflowForm?.defaultData != null &&
              workflow.workflowForm?.defaultData != '') {
            workflow.workflowForm?.masterId = workflow.workflowForm?.defaultData ?? '';
          }
          //データ設定
          if (dict.containsKey(workflow.workflowForm?.sectionName)) {
            dict[workflow.workflowForm?.sectionName]?.add(workflowFormToSectionDic(workflow.workflowForm));
          } else {
            final itemList = <AssetItemListModel>[];
            itemList.add(workflowFormToSectionDic(workflow.workflowForm));
            if (workflow.workflowForm?.sectionName != null && workflow.workflowForm?.sectionName != '') {
              dict[workflow.workflowForm?.sectionName ?? ''] = itemList;
            }
          }

          // 編集権限チェック
          if (workflow.workflowForm?.itemType != 'commentItem') {
            if (workflow.formField == true) {
              workflow.workflowForm?.optionObject?['readonly'] = '0';
            } else if (workflow.formField == null) {
              workflow.workflowForm!.optionObject?['readonly'] = '1';
            }
          }
          // 概覧権限チェック
          if (workflow.workflowForm?.optionObject == null ||
              workflow.workflowForm?.optionObject?['sectionPrivateGroups'] == null) {
            if (workflow.workflowForm?.optionObject?['readonly'] == '0')
              workflow.workflowForm?.optionObject?['sectionPrivateGroups'] = '';
            // optionObject 不存在 或 sectionPrivateGroups 不存在
            if (workflow.workflowForm?.sectionName != null &&
                workflow.workflowForm?.sectionName != '' &&
                !tempArray.contains(workflow.workflowForm?.sectionName)) {
              tempArray.add(workflow.workflowForm?.sectionName ?? '');
            }
          } else {
            // optionObject 和 sectionPrivateGroups 都存在，检查权限
            final isView = controller.checkIsView(workflow.workflowForm?.optionObject?['sectionPrivateGroups']);
            if (isView) {
              if (workflow.workflowForm?.sectionName != null &&
                  workflow.workflowForm?.sectionName != '' &&
                  !tempArray.contains(workflow.workflowForm?.sectionName)) {
                tempArray.add(workflow.workflowForm?.sectionName ?? '');
              }
            }
          }
        }
      }).toList();
    }
    return GetNewWorkflowFormResponse(
      sectionDic: dict,
      sectionList: tempArray,
      resultData: resultForNewApplication,
      taskList: resultForgetTaskInfo,
    );
  }

  AssetItemListModel workflowFormToSectionDic(WorkflowFormModel? model) {
    final AssetItemListModel assetItemListModel = AssetItemListModel(
      tenantId: model?.tenantId,
      itemId: model?.itemId,
      itemName: model?.itemName,
      itemDisplayName: model?.itemDisplayName,
      itemType: model?.itemType,
      option: model?.option,
      defaultData: model?.defaultData,
      inputFlg: model?.inputFlg,
      mobileFlg: model?.mobileFlg,
      sectionName: model?.sectionName,
      sectionType: model?.sectionType,
      sectionSort: model?.sectionSort,
      positionX: model?.positionX,
      positionY: model?.positionY,
      width: model?.width,
      height: model?.height,
      createdById: model?.createdById,
      createdDate: model?.createdDate,
      modifiedById: model?.modifiedById,
      modifiedDate: model?.modifiedDate,
      itemIds: model?.itemIdStr,
      uneditableFlg: model?.uneditableFlg,
      dbtype: model?.DBType,
      itemIdStr: model?.itemIdStr,
    );
    final sectionPrivateGroups = model?.optionObject?['sectionPrivateGroups'] ?? '';
    final isEditable = controller.checkIsView(sectionPrivateGroups);
    assetItemListModel.isEditPermissions = isEditable ? '1' : '0';
    if (model?.optionObject != null) {
      final optionObject = OptionObjModel.fromJson(model?.optionObject ?? {});
      assetItemListModel.optionObject = optionObject;
    }
    return assetItemListModel;
  }
}

class GetNewWorkflowFormResponse {
  List<String> sectionList;
  Map<String, List<AssetItemListModel>> sectionDic;
  WorkflowNewFormModel resultData;
  List<WorkflowAssignDynamicTaskModel> taskList;
  GetNewWorkflowFormResponse({
    required this.sectionList,
    required this.sectionDic,
    required this.resultData,
    required this.taskList,
  });
}
