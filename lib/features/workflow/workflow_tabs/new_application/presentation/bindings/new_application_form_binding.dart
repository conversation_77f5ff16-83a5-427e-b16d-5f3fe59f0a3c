import 'package:asset_force_mobile_v2/core/extensions/getx_extension.dart';
import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/calc_asset_dict_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/check_validate_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/prepare_asset_data_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/controllers/expandable_section_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/s3_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/data/repositories/workflow_new_application_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/domain/repositories/workflow_new_application_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/domain/usecases/get_new_workflow_form_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/new_application_form_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/repositories/workflow_list_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_scan_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:get/get.dart';

class NewApplicationFormBinding extends Bindings {
  @override
  void dependencies() {
    // 注册 JsExecutor 相关依赖（使用统一的服务）
    final jsExecutorContext = JsExecutorBindingService.registerWithoutTag();

    Get.lazyPutFenix(() => PrepareAssetDataUseCase());
    Get.lazyPutFenix(() => CalcAssetDictUseCase(Get.find<JsExecutor>()));
    Get.lazyPutFenix(() => CheckValidateUseCase());
    Get.lazyPut(() => ExpandableSectionController());
    Get.lazyPutFenix(
      () => AfCustomizeViewController(
        prepareAssetDataUseCase: Get.find<PrepareAssetDataUseCase>(),
        calcAssetDictUseCase: Get.find<CalcAssetDictUseCase>(),
        checkValidateUseCase: Get.find(),
        navigationService: Get.find<NavigationService>(),
        jsExecutor: Get.find<JsExecutor>(),
        jsExecutorContext: jsExecutorContext,
      ),
    );
    Get.lazyPutFenix<WorkflowListRepository>(() => WorkflowListRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix<WorkflowNewApplicationRepository>(
      () => WorkflowNewApplicationRepositoryImpl(dioUtil: Get.find<DioUtil>()),
    );
    Get.lazyPutFenix<GetNewWorkflowFormUseCase>(
      () => GetNewWorkflowFormUseCase(
        workflowNewApplicationRepository: Get.find<WorkflowNewApplicationRepository>(),
        workflowListRepository: Get.find<WorkflowListRepository>(),
        userRepository: Get.find<UserRepository>(),
      ),
    );
    Get.lazyPutFenix<WorkflowCommonScanService>(
      () => WorkflowCommonScanService(repository: Get.find<WorkflowListRepository>()),
    );
    Get.lazyPutFenix<UserRepository>(() => UserRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix(() => WorkflowActionService(workflowListRepository: Get.find<WorkflowListRepository>()));
    Get.lazyPutFenix<GetUserRoleUseCaseUseCase>(
      () => GetUserRoleUseCaseUseCase(userRepository: Get.find<UserRepository>()),
    );
    Get.lazyPutFenix(
      () => NewApplicationFormController(
        getNewWorkflowFormUseCase: Get.find<GetNewWorkflowFormUseCase>(),
        navigationService: Get.find<NavigationService>(),
        dialogService: Get.find<DialogService>(),
        workflowCommonScanService: Get.find<WorkflowCommonScanService>(),
        getUserRoleUseCaseUseCase: Get.find<GetUserRoleUseCaseUseCase>(),
        workflowActionService: Get.find<WorkflowActionService>(),
      ),
    );
    Get.lazyPutFenix(
      () => WorkflowCommonController(
        dialogService: Get.find<DialogService>(),
        getUserRoleUseCaseUseCase: Get.find<GetUserRoleUseCaseUseCase>(),
        workflowActionService: Get.find<WorkflowActionService>(),
      ),
    );

    Get.lazyPutFenix(() => GetTurlUseCase(s3Repository: Get.find<S3Repository>()));
    Get.lazyPutFenix<S3Repository>(() => S3RepositoryImpl(dioUtil: Get.find<DioUtil>()));
  }
}
