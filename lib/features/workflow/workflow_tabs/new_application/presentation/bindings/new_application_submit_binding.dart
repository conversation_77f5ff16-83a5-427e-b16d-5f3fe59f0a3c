import 'package:asset_force_mobile_v2/core/extensions/getx_extension.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/controllers/expandable_section_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/layout_setting_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/s3_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/layout_setting_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/new_application_submit_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/repositories/workflow_list_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:get/get.dart';

class NewApplicationSubmitBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<UserRepository>(() => UserRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPut<GetUserRoleUseCaseUseCase>(() => GetUserRoleUseCaseUseCase(userRepository: Get.find<UserRepository>()));

    Get.lazyPut<WorkflowListRepository>(() => WorkflowListRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix<LayoutSettingRepository>(() => LayoutSettingRepositoryImpl(Get.find<DioUtil>()));
    Get.lazyPut(() => WorkflowActionService(workflowListRepository: Get.find<WorkflowListRepository>()));
    Get.lazyPut(
      () => WorkflowCommonController(
        dialogService: Get.find<DialogService>(),
        getUserRoleUseCaseUseCase: Get.find<GetUserRoleUseCaseUseCase>(),
        workflowActionService: Get.find<WorkflowActionService>(),
      ),
    );
    Get.lazyPutFenix<S3Repository>(() => S3RepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPutFenix(
      () => NewApplicationSubmitController(
        dialogService: Get.find<DialogService>(),
        getUserRoleUseCaseUseCase: Get.find<GetUserRoleUseCaseUseCase>(),
        workflowActionService: Get.find<WorkflowActionService>(),
        navigationService: Get.find<NavigationService>(),
      ),
    );

    Get.lazyPutFenix(() => ExpandableSectionController());
  }
}
