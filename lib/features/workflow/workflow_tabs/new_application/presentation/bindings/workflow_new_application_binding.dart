import 'package:asset_force_mobile_v2/core/extensions/getx_extension.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/data/repositories/workflow_new_application_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/domain/repositories/workflow_new_application_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/domain/usecases/get_workflow_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/workflow_new_application_controller.dart';
import 'package:get/get.dart';

class WorkflowNewApplicationBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPutFenix<WorkflowNewApplicationRepository>(
      () => WorkflowNewApplicationRepositoryImpl(dioUtil: Get.find<DioUtil>()),
    );

    Get.lazyPutFenix(() => GetNewWorkflowListUseCase(Get.find<WorkflowNewApplicationRepository>()));

    Get.lazyPutFenix(
      () => WorkflowNewApplicationController(
        getNewWorkflowListUseCase: Get.find<GetNewWorkflowListUseCase>(),
        navigationService: Get.find<NavigationService>(),
      ),
    );
  }
}
