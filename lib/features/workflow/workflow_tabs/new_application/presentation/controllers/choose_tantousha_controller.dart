import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/list_selector/domain/models/list_selector_params.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/state/new_application_form_ui_state.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:get/get.dart';

class ChooseTantoushaController extends BaseController {
  final WorkflowActionService workflowActionService;
  final DialogService dialogService;
  final NavigationService navigationService;
  ChooseTantoushaController({
    required this.workflowActionService,
    required this.dialogService,
    required this.navigationService,
  });
  final NewApplicationFormUiState state = NewApplicationFormUiState();
  bool isNoAsset = false;
  bool isApplication = false;
  final RxMap<int, String> chooseTitles = <int, String>{}.obs;

  @override
  void onInit() {
    super.onInit();
    try {
      final tabsController = Get.find<TabsController>();
      if (Get.isRegistered<TabsController>()) {
        tabsController.showBottomNavBar.value = false;
      }
    } catch (e) {
      // 处理找不到 Controller 的情况，可能 TabsPage 还没加载
      LogUtil.e('ChooseTantoushaController error find tabController: $e');
    }
    if (!state.firstLoad) return;
    state.firstLoad = false;

    final args = (Get.arguments as Map<String, dynamic>?) ?? {};

    isNoAsset = args['isNoAsset'] ?? false;
    state
      ..inputAssetListFlag.value = args['inputAssetListFlag'] ?? ''
      ..workflowScript = args['workflowScript'] ?? ''
      ..jsString = args['jsString'] ?? ''
      ..autoFetchSearchId = args['autoFetchSearchId']
      ..workflowId = args['workflowId']
      ..isMyselfInputTask = args['isMyselfInputTask'] ?? false
      ..permissions.value = args['permissions'] ?? <String, bool>{}
      ..buttonNameList.value = args['buttonNameList'] ?? [];
    if (isNoAsset) {
      _initReturnedForm(args);
    } else {
      _initNewForm(args);
    }

    initDynamicTaskInfo();
  }

  /// 退回到表单输入时的额外赋值
  void _initReturnedForm(Map<String, dynamic> args) {
    isApplication = args['isApplication'] ?? false;
    if (isApplication) {
      // 只有当 isApplication 为 true 时才读这些字段
      state
        ..workflowName.value = args['workflowName'] ?? ''
        ..stepName.value = args['stepName'] ?? ''
        ..processInstanceId = args['processInstanceId']
        ..taskId = args['taskId'];
    }
    state
      ..assetDict.value = args['assetDict'] ?? {}
      ..comments.value = args['comments'] ?? []
      ..processDefinitionId = args['processDefinitionId'] ?? ''
      ..dynamicTaskInfo.value = args['dynamicTaskInfo'] ?? [];
  }

  /// 正常新建/编辑表单时的赋值
  void _initNewForm(Map<String, dynamic> args) {
    state
      ..dynamicTaskInfo.value = args['dynamicTaskInfo'] ?? <WorkflowAssignDynamicTaskModel>[]
      ..assetDict.value = args['assetDict'] ?? {}
      ..assetListId = args['assetListId'] ?? ''
      ..registeredAssetList = args['registeredAssetList'] ?? <RegisteredAssetListModel>[]
      ..processDefinitionId = args['processDefinitionId'] ?? ''
      ..processInstanceId = args['processInstanceId'] ?? ''
      ..isNew = args['isNew'] ?? true
      ..taskId = args['taskId'] ?? ''
      ..assetTypeName = args['assetTypeName'] ?? ''
      ..assetTypeId = args['assetTypeId'] ?? ''
      ..workflowName.value = args['workflowName'] ?? ''
      ..comments.value = args['comment'] ?? <dynamic>[]
      ..isUpdateAmount = args['isUpdateAmount']
      ..isCountingType.value = args['isCountingType'] ?? false
      ..stepName.value = args['stepName'] ?? ''
      ..autoFetchSearchId = args['autoFetchSearchId'];

    final assetCount = args['assetCount']?.toString();
    state.dataCount.value = assetCount != null && assetCount.isNotEmpty
        ? assetCount
        : state.registeredAssetList.length.toString();

    if (args.containsKey('editAssetList')) {
      state.editAssetList = args['editAssetList'];
    }
  }

  Future<void> apply() async {
    // 提交时用来判断
    if (!checkDynamicTaskCanSubmit()) {
      CommonDialog.show(content: '各ステップの承認者を選択してください。', confirmText: 'はい');
      return;
    } else {
      if (state.dynamicTaskInfo.isNotEmpty && state.dynamicTantousha.isNotEmpty) {
        for (int i = 0; i < state.dynamicTaskInfo.length; i++) {
          final element = state.dynamicTaskInfo[i];
          if (element.assignDynamicType == 'group') {
            element.authorizerId = state.dynamicTantousha[i]['roleId'];
            element.authorizerName = state.dynamicTantousha[i]['roleName'];
          } else {
            element.authorizerId = state.dynamicTantousha[i]['userId'];
            element.authorizerName =
                '${state.dynamicTantousha[i]['lastName']} ${state.dynamicTantousha[i]['firstName']}';
          }
        }
      }
    }
    if (isNoAsset) {
      if (isApplication) {
        final queryParams = {
          'isSubmit': true,
          'workflowName': state.workflowName.value,
          'stepName': state.stepName.value,
          'processInstanceId': state.processInstanceId,
          'taskId': state.taskId,
          'assetDict': state.assetDict,
          'comments': state.comments,
          'processDefinitionId': state.processDefinitionId,
          'dynamicTaskInfo': state.dynamicTaskInfo,
          'dynamicTantousha': state.dynamicTantousha,
          'permissions': state.permissions,
          'workflowRaiseButtonNameList': state.buttonNameList,
        };

        navigationService.navigateTo(AutoRoutes.applicationView, arguments: queryParams);
      } else {
        final queryParams = {
          'isSubmit': true,
          'assetDict': state.assetDict,
          'comments': state.comments,
          'processDefinitionId': state.processDefinitionId,
          'dynamicTaskInfo': state.dynamicTaskInfo,
          'dynamicTantousha': state.dynamicTantousha,
          'permissions': state.permissions,
          'workflowRaiseButtonNameList': state.buttonNameList,
        };
        Get.back(id: SharedNavBarEnum.workflow.navigatorId, result: queryParams);
      }
    } else {
      // 跳转提交一览页面
      final queryParams = {
        'isSubmit': true,
        'registeredAssetList': state.registeredAssetList,
        'formData': state.assetDict,
        // 'sectionArray': state.sectionArray,
        // 'scanConditionList': state.scanConditionList,
        'assetTypeName': state.assetTypeName,
        'assetTypeId': state.assetTypeId,
        'inputAssetListFlag': state.inputAssetListFlag,
        // 'workflowTypeCode': state.workflowTypeCode,
        'processDefinitionId': state.processDefinitionId,
        'processInstanceId': state.processInstanceId,
        'workflowName': state.workflowName,
        'isNew': state.isNew,
        'taskId': state.taskId,
        'comment': state.comments,
        'isUpdateAmount': state.isUpdateAmount,
        // 'progress': state.progress,
        'backUrl': '/workflow/new/w1-choose-tantousha',
        'assetListId': state.assetListId,
        'isCountingType': state.isCountingType,
        'dynamicTaskInfo': state.dynamicTaskInfo,
        'dynamicTantousha': state.dynamicTantousha,
        'stepName': state.stepName,
        'isFromNew': state.isNew,
        'workflowScript': state.workflowScript,
        'jsString': state.jsString,
        'editAssetList': state.editAssetList,
        'assetCount': state.dataCount,
        'autoFetchSearchId': state.autoFetchSearchId,
        'workflowId': state.workflowId,
        // 'engineId': state.engineId,
        'newPageInstance': true,
        'isMyselfInputTask': state.isMyselfInputTask,
        'permissions': state.permissions,
        'workflowRaiseButtonNameList': state.buttonNameList,
      };
      navigationService.navigateTo(
        AutoRoutes.newFormSubmit,
        id: SharedNavBarEnum.workflow.navigatorId,
        arguments: queryParams,
      );
    }
  }

  Future<void> chooseTantousha(int index) async {
    if (state.dynamicTaskInfo.length > 0) {
      final params = ListSelectorParams(
        index: 0,
        type: state.dynamicTaskInfo[index].assignDynamicType,
        dynamicLeader: {
          'procDefId': state.dynamicTaskInfo[index].procDefId,
          'taskDefKey': state.dynamicTaskInfo[index].taskDefKey,
        },
        isFromPage: 'choose-tantousha-view',
        autoFetchSearchId: state.autoFetchSearchId,
        permissions: state.permissions,
        searchConditionDataList: [],
        title: '',
        items: [],
      );
      final result = await navigationService.navigateTo(AutoRoutes.userGroupSelector, arguments: params);
      if (result != null && result['info'] != null) {
        if (index >= 0 && index < state.dynamicTantousha.length) {
          state.dynamicTantousha[index] = result['info'];
        } else {
          state.dynamicTantousha.add(result['info']);
        }
        getTantoushaInfoWithIndex(index);
      }
    }
  }

  // 获取担当者文本用于html页面显示
  String getTantoushaInfoWithIndex(int index) {
    if (state.dynamicTaskInfo.length > 0) {
      final chooseType = state.dynamicTaskInfo[index].assignDynamicType ?? '';
      final datum = (index >= 0 && index < state.dynamicTantousha.length) ? state.dynamicTantousha[index] : null;
      if (datum != null) {
        final title = chooseType == 'group' ? datum['roleName'] : '${datum['lastName']} ${datum['firstName']}';
        // 更新对应索引的标题
        chooseTitles[index] = title;
        return title;
      } else {
        chooseTitles[index] = '未選択';
        return '未選択';
      }
    }
    chooseTitles[index] = '未選択';
    return '未選択';
  }

  void initDynamicTaskInfo() {
    // 如果存在已选中的担当者信息，则初始化用于显示的对象
    state.dynamicTantousha.value = [];
    if (state.dynamicTaskInfo.isNotEmpty) {
      for (var dynamicTaskInfoElement in state.dynamicTaskInfo) {
        // 若存在已存的动态担当者选择，则判断任务类型后创建对象存入数组
        if (dynamicTaskInfoElement.authorizerId != null) {
          if (dynamicTaskInfoElement.assignDynamicType == 'user') {
            // user 类型
            state.dynamicTantousha.add({
              'userId': dynamicTaskInfoElement.authorizerId,
              'lastName': dynamicTaskInfoElement.authorizerName,
              'firstName': '',
            });
          } else {
            // group 类型
            state.dynamicTantousha.add({
              'roleId': dynamicTaskInfoElement.authorizerId,
              'roleName': dynamicTaskInfoElement.authorizerName,
            });
          }
        }
      }
    }
  }

  /// 获取动态担当者选择task的信息
  Future<void> getDynamicTaskInfo() async {
    try {
      await showLoading();
      final result = await workflowActionService.getDynamicTaskInfo(state.processDefinitionId);
      state.dynamicTaskInfo.value = result;
      initDynamicTaskInfo();
    } finally {
      hideLoading();
    }
  }

  // 判断是否符合提交条件
  // 返回true时满足条件可以提交
  bool checkDynamicTaskCanSubmit() {
    if (state.dynamicTaskInfo.length > 0) {
      if (state.dynamicTantousha.length > 0) {
        return state.dynamicTaskInfo.length == state.dynamicTantousha.length;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
