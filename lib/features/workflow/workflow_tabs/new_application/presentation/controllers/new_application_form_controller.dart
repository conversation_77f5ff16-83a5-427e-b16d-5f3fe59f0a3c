import 'dart:convert';

import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/list_selector/domain/models/list_selector_params.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_ai_ocr_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/data/models/workflow_new_form_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/domain/usecases/get_new_workflow_form_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/workflow_new_application_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/state/new_application_form_ui_state.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_button_name_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_scan_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:get/get.dart';

class NewApplicationFormController extends WorkflowCommonController {
  GetNewWorkflowFormUseCase getNewWorkflowFormUseCase;
  final NavigationService navigationService;
  final WorkflowCommonScanService workflowCommonScanService;
  final NewApplicationFormUiState state = NewApplicationFormUiState();

  NewApplicationFormController({
    required this.getNewWorkflowFormUseCase,
    required this.navigationService,
    required super.dialogService,
    required this.workflowCommonScanService,
    required super.getUserRoleUseCaseUseCase,
    required super.workflowActionService,
  });
  @override
  void onReady() async {
    super.onReady();
    final params = Get.arguments;
    if (params is WorkflowArgumentModel) {
      _initializeState(params);
      if (state.firstLoad) {
        await _loadInitialData(params);
      }
    }
  }

  void _initializeState(WorkflowArgumentModel params) {
    state.hasAssetTypeName.value = params.hasAssetTypeName;
    if (params.hasAssetTypeName && state.isChose.value) {
      state
        ..isSubmit.value = params.isSubmit ?? false
        ..assetDict.value = params.assetDict ?? {}
        ..comments.value = params.comments ?? []
        ..processDefinitionId = params.processDefinitionId
        ..dynamicTaskInfo.value = params.dynamicTaskInfo ?? []
        ..dynamicTantousha.value = params.dynamicTantousha ?? []
        ..assetTypeName.value = params.assetTypeName ?? '';
      StorageUtils.set(StorageUtils.keyWfPdID, params.processDefinitionId);
    }
  }

  Future<void> _loadInitialData(WorkflowArgumentModel params) async {
    await StorageUtils.set(StorageUtils.keyWfPdID, params.processDefinitionId);
    state
      ..workflowName.value = params.workflowName
      ..processDefinitionId = params.processDefinitionId
      ..assetTypeIdWithFirstWf = params.assetTypeIdWithFirstWf
      ..workflowId = params.workflowId;
    await loadData(params.processDefinitionId);
    state.firstLoad = false;
  }

  Future<void> loadData(String processDefinitionId) async {
    await showLoading();
    try {
      final result = await getNewWorkflowFormUseCase(processDefinitionId);
      _updateStateWithResult(result);
    } finally {
      hideLoading();
    }
  }

  void _updateStateWithResult(GetNewWorkflowFormResponse result) {
    state
      ..assetDict.value = result.sectionDic
      ..tempArray.value = result.sectionList
      ..autoFetchSearchId = result.resultData.autoFetchSearchId
      ..stepName.value = result.resultData.stepName ?? ''
      ..isUpdateAmount = result.resultData.workflowEngineAmountProcess?.isUpdateAmount;
    _updateJsString(result);
    state
      ..inputAssetListFlag.value = result.resultData.inputAssetListFlag ?? ''
      ..isCountingType.value = result.resultData.updateAmountShow ?? false
      ..amountActionTaskUpdateItemId = result.resultData.workflowEngineAmountProcess?.amountActionTaskUpdateItemId
      ..amountActionTaskUpdateItemName = result.resultData.workflowEngineAmountProcess?.amountActionTaskUpdateItemName;
    _updatePermissions(result.resultData.form ?? []);
    state
      ..buttonNameList.value = extractNewWFRaiseButtons(result.resultData.workflowButtonName)
      ..isHasTantoushaF.value = result.taskList.isNotEmpty
      ..isNextEnable.value = isNextEnable()
      ..buttonNum.value = numberButtonsFooterSection(state.buttonNameList, state.isNextEnable.value)
      ..dynamicTaskInfo.value = result.taskList;
  }

  void _updateJsString(GetNewWorkflowFormResponse result) {
    final cjs = result.resultData.commonJS ?? '';
    if (result.resultData.workflowLogicScript?.isNotEmpty ?? false) {
      state.jsString = cjs + result.resultData.workflowLogicScript!;
    }
    if (result.resultData.workflowScript?.isNotEmpty ?? false) {
      state.jsString = cjs + result.resultData.workflowScript!;
    }
  }

  void _updatePermissions(List<WorkflowEngineInputTaskFormItem> form) {
    for (var item in form) {
      final enabled = item.formField;
      if (item.workflowForm?.itemName != null) {
        state.permissions[item.workflowForm!.itemName!] = enabled ?? false;
      }
    }
  }

  /// 新规申请重组提出按钮
  /// @param jsonData 后台json
  /// @returns 返回提出数组
  List<WorkflowRaiseButtonModel> extractNewWFRaiseButtons(WorkflowButtonNameModel? model) {
    if (model == null) {
      return [WorkflowRaiseButtonModel(isCustomizedButtonName: false, nameRaise: '提出')];
    }

    final List<WorkflowRaiseButtonModel> buttonsNameArray = [];
    void pushButton(String? buttonName) {
      if (buttonName != null && buttonName.isNotEmpty) {
        buttonsNameArray.add(WorkflowRaiseButtonModel(isCustomizedButtonName: true, nameRaise: buttonName));
      }
    }

    pushButton(model.addedButtonSecond);
    pushButton(model.addedButtonFirst);
    pushButton(model.transitButtonName);

    return buttonsNameArray.isEmpty
        ? [WorkflowRaiseButtonModel(isCustomizedButtonName: false, nameRaise: '提出')]
        : buttonsNameArray;
  }

  Future<void> next() async {
    if (state.hasAssetTypeName.value) {
      // 資産入力必要ではない、かつ、担当者の指定必要場合、担当者画面に遷移する
      if (state.inputAssetListFlag == '0' && state.isHasTantoushaF.value) {
        // 担当者選択
        navigationService.navigateTo(AutoRoutes.chooseTantousha, id: SharedNavBarEnum.workflow.navigatorId);
        return;
      }
      final isCheck = await checkAndGetDataWithTURL(state.assetDict);
      if (!isCheck) {
        return;
      }
      final wfNameObj = state.assetDict.values
          .expand((element) => element)
          .firstWhere((item) => item.itemName == 'WF名');
      final wfName = wfNameObj.defaultData ?? '';
      final dataScan = WorkflowScanBarcodeData(
        assetTypeId: state.assetTypeIdWithFirstWf.toString(),
        locationInfo: state.locationInfo,
        jobType: 'createNewWF',
        isFromNew: true,
        scannedAssetIdList: [],
        // assetID(同じbarcode、違う場所)
        isCountingType: state.isUpdateAmount == '1' ? false : state.isCountingType.value,
        searchId: state.autoFetchSearchId,
        workflowId: state.workflowId,
        engineId: state.processDefinitionId,
        scannedAssetCount: 0,
        processInstanceId: '',
        editAssetList: [],
        taskId: '',
        whatKindWF: NewApplicationWorkflow.newW1FormPage,
        state: null,
        // どちらの一覧画面から： 0:申請； 1:承認； 2:全て；
        scanType: null,
        isFromLocationSettingPage: false,
        assetTypeName: state.assetTypeName.value,
        taskName: state.stepName.value,
        workflowName: wfName,
      );
      void goToNextPage(List<dynamic> registeredAssetList, ScanType scanType) {
        final navigationExtras = {
          'registeredAssetList': registeredAssetList,
          'scanType': scanType,
          'assetDict': state.assetDict,
          'isUpdateAmount': state.isUpdateAmount,
          'isCountingType': state.isCountingType.value,
          'assetTypeId': state.assetTypeIdWithFirstWf.toString(),
          'assetTypeName': state.assetTypeName.value,
          'taskName': state.stepName.value,
          'workflowName': wfName,
          'stepName': state.stepName.value,
          'processDefinitionId': state.processDefinitionId,
          'workflowId': state.workflowId,
          'comments': state.comments,
          'workflowRaiseButtonNameList': state.buttonNameList,
          'dynamicTaskInfo': state.dynamicTaskInfo,
          'dynamicTantousha': state.dynamicTantousha,
          'workflowScript': state.workflowScript,
          'fromPage': AutoRoutes.newForm,
        };
        navigationService.navigateTo(
          AutoRoutes.workflowScanList,
          id: SharedNavBarEnum.workflow.navigatorId,
          arguments: navigationExtras,
        );
      }

      workflowCommonScanService.startWorkflowScanBarcode(dataScan, (returnValueAfterScan) {
        goToNextPage(returnValueAfterScan['registeredAssetList'], returnValueAfterScan['scanType']);
      });
    } else {
      applyNewApplication(null);
    }
  }

  void updateFormSubmit(Map<String, dynamic> editData) {
    state.assetTypeIdWithFirstWf = editData['assetTypeIdWithFirstWf'];
    state.assetTypeName = editData['assetTypeName'];
  }

  Future<void> applyNewApplication(WorkflowRaiseButtonModel? raiseDic) async {
    if (state.hasAssetTypeName.value) {
      final map = await checkAndGetDataWithTURL(state.assetDict);
      if (!map) return;
      await showConfirmAlert(raiseDic);
    } else {
      if (state.isHasTantoushaF.value) {
        if (state.isSubmit.value) {
          if (!checkDynamicTaskCanSubmit(state.dynamicTaskInfo, state.dynamicTantousha)) {
            dialogService.show(content: '各ステップの承認者を選択してください。', confirmText: 'はい');
            return;
          } else {
            if (state.dynamicTaskInfo.isNotEmpty && state.dynamicTantousha.isNotEmpty) {
              for (var i = 0; i < state.dynamicTaskInfo.length; i++) {
                final element = state.dynamicTaskInfo[i];
                if (element.assignDynamicType == 'group') {
                  element.authorizerId = state.dynamicTantousha[i]?['roleId'];
                  element.authorizerName = state.dynamicTantousha[i]?['roleName'];
                } else {
                  element.authorizerId = state.dynamicTantousha[i]?['userId'];
                  element.authorizerName =
                      state.dynamicTantousha[i]?['lastName'] + state.dynamicTantousha[i]?['firstName'];
                }
              }
            }
            state.processDefinitionId = await StorageUtils.get<String>(StorageUtils.keyWfPdID) ?? '';
            apply(
              state.assetDict,
              state.comments,
              state.processDefinitionId,
              state.workflowScript,
              state.dynamicTaskInfo,
              raiseDic,
            );
          }
        } else {
          final navigationExtras = {
            'isNoAsset': true,
            'assetDict': state.assetDict,
            'comments': state.comments,
            'processDefinitionId': state.processDefinitionId,
            'dynamicTaskInfo': state.dynamicTaskInfo,
          };
          final result = await navigationService.navigateTo(
            AutoRoutes.chooseTantousha,
            id: SharedNavBarEnum.workflow.navigatorId,
            arguments: navigationExtras,
          );
          if (result != null) {
            state.dynamicTantousha.value = result['dynamicTantousha'] ?? [];
            state.isSubmit.value = result['isSubmit'] ?? false;
            state.isNextEnable.value = isNextEnable();
          }
          return;
        }
      } else {
        state.processDefinitionId = await StorageUtils.get<String>(StorageUtils.keyWfPdID) ?? '';
        apply(
          state.assetDict,
          state.comments,
          state.processDefinitionId,
          state.workflowScript,
          state.dynamicTaskInfo,
          raiseDic,
        );
      }
    }
  }

  Future<void> showConfirmAlert(WorkflowRaiseButtonModel? raiseDic) async {
    try {
      await showLoading();
      await applyWithList(
        state.assetDict,
        state.comments,
        state.processDefinitionId,
        '',
        [],
        state.workflowName.value,
        state.assetTypeId,
        true,
        '',
        '',
        state.isCountingType.value,
        [],
        false,
        false,
        raiseDic,
        'startNew',
        '',
        state.workflowScript,
      );
    } finally {
      hideLoading();
    }
  }

  bool isNextEnable() {
    if (state.hasAssetTypeName.value) {
      return state.isHasTantoushaF.value || state.inputAssetListFlag.value == '1';
    } else {
      return state.isHasTantoushaF.value && !state.isSubmit.value;
    }
  }

  /// 底部按钮的总数
  int numberButtonsFooterSection(List<WorkflowRaiseButtonModel> buttonList, bool isNextEnable) {
    int allButtonNum = 1; // "キャンセル" 按钮
    if (state.hasAssetTypeName.value || !state.isSubmit.value) allButtonNum += 1; // "一時保存" 按钮
    allButtonNum += isNextEnable ? 1 : buttonList.length; // 否决或提出按钮
    return allButtonNum;
  }

  void newApplicationSave() async {
    state.processDefinitionId = StorageUtils.get<String>(StorageUtils.keyWfPdID) ?? '';
    saveWorkflowData(state.assetDict, state.comments, state.processDefinitionId, true, AutoRoutes.newForm);
  }

  // 选择担当者页面 担当者の選択のペイジ
  void chooseTantousha(int index) async {
    state.isChose.value = false;
    if (state.dynamicTaskInfo.length > index) {
      final params = ListSelectorParams(
        index: 0,
        type: state.dynamicTaskInfo[index].assignDynamicType,
        dynamicLeader: {
          'procDefId': state.dynamicTaskInfo[index].procDefId,
          'taskDefKey': state.dynamicTaskInfo[index].taskDefKey,
        },
        isFromPage: 'choose-tantousha-view',
        autoFetchSearchId: state.autoFetchSearchId,
        searchConditionDataList: [],
        title: '',
        items: [],
      );
      final result = await navigationService.navigateTo(AutoRoutes.userGroupSelector, arguments: params);
      if (result != null && result['info'] != null) {
        if (index >= 0 && index < state.dynamicTantousha.length) {
          state.dynamicTantousha[index] = result['info'];
        } else {
          state.dynamicTantousha.add(result['info']);
        }
      }
    }
  }

  // 入力したデータのObjectを返す ionic 返回object，但用于if判断 改为了返回bool进行判断
  Future<bool> checkAndGetDataWithTURL(Map<String, List<AssetItemListModel>> itemDataDict) async {
    try {
      // ionic 深拷贝原始数据
      final itemDataDictCopy = jsonDecode(jsonEncode(itemDataDict)) as Map<String, dynamic>;
      final assetTypeList = <AssetItemListModel>[];
      for (final sectionName in itemDataDictCopy.keys) {
        assetTypeList.addAll(itemDataDict[sectionName] ?? []);
      }
      final isCheck = await checkValidate(assetTypeList, state.isFromDetailOrEditPage);
      if (!isCheck) {
        return false;
      }
      // 处理数据项
      final Map<String, dynamic> resultMap = {};
      for (final assetType in assetTypeList) {
        if (assetType.method != '2') {
          assetType.itemValue = assetType.defaultData;
        }
        if (assetType.itemName != null) {
          resultMap[assetType.itemName!] = assetType.defaultData is List
              ? List<dynamic>.from(assetType.defaultData)
              : assetType.defaultData;
        }
      }
      return resultMap.length > 0 ? true : false;
    } catch (e) {
      LogUtil.e('Error in checkAndGetDataWithTURL: $e');
      return false;
    }
  }

  // todo 检查 editAssetList 是否是字符串，如果是则转换为数组 (ionic代码 配合扫描回值， 后面看是否需要)
  void checkEditAssetListClassType() {
    if (state.editAssetList is String) {
      final assetList = jsonDecode(state.editAssetList as String);
      state.editAssetList = assetList;
    }
  }

  // 获取担当者文本用于页面显示
  String getTantoushaInfoWithIndex(int index) {
    final dynamicTaskInfo = state.dynamicTaskInfo;
    final dynamicTantousha = state.dynamicTantousha;
    if (dynamicTaskInfo.length > 0 && dynamicTantousha.length == dynamicTaskInfo.length) {
      final chooseType = dynamicTaskInfo[index].assignDynamicType;
      final datum = dynamicTantousha[index] ?? null;
      if (datum != null) {
        if (chooseType == 'group') {
          return datum['roleName'];
        } else {
          return datum['lastName'] + ' ' + datum['firstName'];
        }
      } else {
        return '未割当';
      }
    }
    return '未割当';
  }

  void back() {
    presentCancelConfirm();
  }
}
