import 'dart:convert';

import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/workflow_new_application_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/state/new_application_form_ui_state.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:get/get.dart';

class NewApplicationSubmitController extends WorkflowCommonController {
  final NavigationService navigationService;
  final NewApplicationFormUiState state = NewApplicationFormUiState();
  NewApplicationSubmitController({
    required this.navigationService,
    required super.dialogService,
    required super.getUserRoleUseCaseUseCase,
    required super.workflowActionService,
  }) {
    final params = Get.arguments;
    state.assetDict = params['assetDict'] ?? {};
    state.comments = params['comments'] ?? [];
  }

  @override
  void onReady() async {
    super.onReady();
    if (!state.firstLoad) return;
    state.firstLoad = false;

    _initStateFromArgs();
    _loadDynamicTaskInfo();
    ever(state.dynamicTaskInfo, (_) {
      state.isHasTantoushaF.value = state.dynamicTaskInfo.isNotEmpty;
    });
  }

  /// 从 Get.arguments 中批量初始化 state
  void _initStateFromArgs() {
    final args = (Get.arguments as Map<String, dynamic>?) ?? {};
    state
      ..stepName.value = args['stepName'] ?? ''
      ..isFromNew = args['isFromNew'] ?? true
      ..isCountingType.value = args['isCountingType'] ?? false
      ..isUpdateAmount = args['isUpdateAmount'] ?? ''
      ..processDefinitionId = args['processDefinitionId'] ?? ''
      ..processInstanceId = args['processInstanceId'] ?? ''
      ..taskId = args['taskId'] ?? ''
      ..workflowName.value = args['workflowName'] ?? ''
      ..assetTypeName.value = args['assetTypeName'] ?? ''
      ..workflowId = args['workflowId']
      ..inputAssetListFlag.value = args['inputAssetListFlag'] ?? ''
      ..registeredAssetList = args['registeredAssetList'] ?? []
      ..buttonNameList = args['buttonNameList'] ?? <WorkflowRaiseButtonModel>[].obs
      ..assetTypeId = args['assetTypeId'] ?? ''
      ..taskId = args['taskId'] ?? ''
      ..assetListId = args['assetListId'] ?? ''
      ..isMyselfInputTask = args['isMyselfInputTask'] ?? false
      ..workflowScript = args['workflowScript'] ?? ''
      ..processInstanceId = args['processInstanceId'] ?? ''
      ..dynamicTaskInfo = args['dynamicTaskInfo'] ?? <WorkflowAssignDynamicTaskModel>[].obs
      ..dynamicTantousha = args['dynamicTantousha'] ?? [].obs
      ..isSubmit.value = args['isSubmit'] ?? false;

    // 计算 dataCount
    state.dataCount.value = args['assetCount']?.toString() ?? state.registeredAssetList.length.toString();

    // 计算底部按钮数量
    state.buttonNum.value = numberButtonsFooterSection(state.buttonNameList);
  }

  /// 异步加载动态任务信息，并处理 loading
  Future<void> _loadDynamicTaskInfo() async {
    await showLoading();
    try {
      if (!state.isSubmit.value) {
        final result = await workflowActionService.getDynamicTaskInfo(state.processDefinitionId);
        state.dynamicTaskInfo.assignAll(result);
      }
    } finally {
      hideLoading();
    }
  }

  Future<void> applyForSubmit(WorkflowRaiseButtonModel? raiseDic) async {
    if (state.inputAssetListFlag != '0' && state.dataCount == '0') {
      dialogService.showToast('資産をスキャンしてください');
      return;
    }
    if (!checkDynamicTaskCanSubmit(state.dynamicTaskInfo, state.dynamicTantousha)) {
      dialogService.show(content: '各ステップの承認者を選択してください。', confirmText: 'はい');
      return;
    } else {
      if (state.dynamicTaskInfo.isNotEmpty && state.dynamicTantousha.isNotEmpty) {
        for (var i = 0; i < state.dynamicTaskInfo.length; i++) {
          final element = state.dynamicTaskInfo[i];
          if (element.assignDynamicType == 'group') {
            element.authorizerId = state.dynamicTantousha[i]['roleId'];
            element.authorizerName = state.dynamicTantousha[i]['roleName'];
          } else {
            element.authorizerId = state.dynamicTantousha[i]['userId'];
            element.authorizerName = state.dynamicTantousha[i]['lastName'] + state.dynamicTantousha[i]['firstName'];
          }
        }
      }
    }
    try {
      await showLoading();
      final bool isCheck = await checkAndGetData(state.assetDict);
      if (!isCheck) return;
    } finally {
      hideLoading();
    }
    if (state.isUpdateAmount == '1') {
      applyWithMaihamaList(
        state.assetDict,
        state.comments,
        state.processDefinitionId,
        state.processInstanceId,
        state.registeredAssetList,
        state.isNew,
        raiseDic,
      );
    } else {
      var WFActionType;
      if (state.actionType == 'restart') {
        WFActionType = 'restart';
      } else {
        WFActionType = 'startNew';
      }
      try {
        await applyWithList(
          state.assetDict,
          state.comments,
          state.processDefinitionId,
          state.processInstanceId,
          state.registeredAssetList,
          state.workflowName.value,
          state.assetTypeId,
          state.isNew,
          state.taskId,
          state.assetListId,
          state.isCountingType.value,
          state.dynamicTaskInfo,
          true,
          state.isMyselfInputTask,
          raiseDic,
          WFActionType,
          state.stepName.value,
          state.workflowScript,
        );
        // actionWhenFinish('tichu', '');
      } finally {}
    }
  }

  void toAssetListPage() async {
    final queryParams = {
      'registeredAssetList': state.registeredAssetList,
      'assetTypeId': state.assetTypeId,
      'formData': Map.from(state.assetDict),
      'isUpdateAmount': state.isUpdateAmount,
      'assetTypeName': state.assetTypeName,
      'workflowName': state.workflowName.value,
      'stepName': state.stepName.value,
      'workflowScript': state.workflowScript,
      'processDefinitionId': state.processDefinitionId,
      'processInstanceId': state.processInstanceId,
      'inputAssetListFlag': state.inputAssetListFlag.value,
      'taskId': state.taskId,
      'buttonNameList': state.buttonNameList,
      'isMyselfInputTask': state.isMyselfInputTask,
      'workflowId': state.workflowId,
      'dynamicTantousha': state.dynamicTantousha,
      'dynamicTaskInfo': state.dynamicTaskInfo,
      'isCountingType': state.isCountingType.value,
      'comments': state.comments,
      'fromSubmit': true,
      'fromPage': AutoRoutes.newFormSubmit,
    };
    final result = await Get.toNamed(
      AutoRoutes.workflowScanList,
      id: SharedNavBarEnum.workflow.navigatorId,
      preventDuplicates: false,
      arguments: queryParams,
    );
    state.registeredAssetList = result?['registeredAssetList'] ?? [];
    state.dataCount.value = state.registeredAssetList.length.toString();
  }

  void applyWithMaihamaList(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processDefinitionId,
    String processInstanceId,
    List registeredAssetList,
    bool isNew,
    WorkflowRaiseButtonModel? raiseDic,
  ) {
    final formData = getFormData(assetDict, comments);
    final amountActionAssetList = [];
    if (registeredAssetList.length > 0) {
      registeredAssetList.map((item) {
        var map;
        map['assetId'] = item['assetId'];
        map['amount'] = item['amount'];
        amountActionAssetList.add(map);
      }).toList();
    }
    formData['amountActionAssetList'] = jsonEncode(amountActionAssetList);
    if (isNew) {
      baseAction(raiseDic?.nameRaise != null ? raiseDic!.nameRaise : '提出', false, assetDict, () async {
        formData['processDefinitionId'] = processDefinitionId;
        final result = await workflowActionService.startWithThirdFlow(formData);
        // 点击保存按钮后删除S3文件 todo
        actionWhenFinish('提出', '');
        LogUtil.d(result);
      }, () async {});
    } else {
      baseAction(raiseDic?.nameRaise != null ? raiseDic!.nameRaise : '提出', false, assetDict, () async {
        formData['status'] = 'active';
        formData['processDefinitionId'] = processDefinitionId;
        final result = await workflowActionService.workflowStartSaveTemporary(formData);
        // 点击保存按钮后删除S3文件 todo
        LogUtil.d(result);
        StorageUtils.set(StorageUtils.keyIsDeleteAllAssetAtBefore, 0); // 重置资产状态后flag也要重置一下
        StorageUtils.set(StorageUtils.keyTempEditAssetList, '[]'); // 重置资产状态后备份用的资产list也要重置一下
        Future.delayed(const Duration(milliseconds: 500), () {
          Get.until((route) => route.isFirst);
        });
      }, () async {});
    }
  }

  // 底部按钮总数
  int numberButtonsFooterSection(List<WorkflowRaiseButtonModel> buttonList) {
    if (state.buttonNameList.length == 0) {
      return 0;
    }
    // 默认第一个是取消
    int allButtonNum = 0;
    // キャンセル
    allButtonNum = allButtonNum + 1;
    allButtonNum = allButtonNum + buttonList.length;
    return allButtonNum;
  }

  Future<void> back() async {
    await dialogService.show(
      content: '保存せずに終了しますか？入力したデータ・及びスキャンデータは破棄されます。',
      cancelText: 'キャンセル',
      onConfirm: () async {
        final queryParams = WorkflowArgumentModel(
          processDefinitionId: state.processDefinitionId,
          hasAssetTypeName: state.assetTypeName.value.isNotEmpty,
          workflowName: state.workflowName.value,
          assetTypeName: state.assetTypeName.value,
          assetTypeIdWithFirstWf: int.tryParse(state.assetTypeId),
          workflowId: state.workflowId,
        );
        if (state.isFromNew)
          await Get.offNamedUntil(
            AutoRoutes.newForm,
            (route) => route.settings.name == AutoRoutes.appTabWorkflowTabs,
            id: SharedNavBarEnum.workflow.navigatorId,
            arguments: queryParams,
          );
        else
          backOkayFunAtUserTask();
      },
    );
  }

  /// 在user task的时候，确认按钮
  Future<void> backOkayFunAtUserTask() async {
    try {
      showLoading();
      final formData = <String, dynamic>{};
      formData['processInstanceId'] = state.processInstanceId;
      formData['taskId'] = state.taskId;
      formData['assetListDataJson'] = jsonEncode(state.editAssetList);
      final assetList = await StorageUtils.get<String>(StorageUtils.keyTempEditAssetList);
      final result = assetList != null && assetList != '[]';
      if (result) {
        formData['clearedAssetListDataJson'] = assetList;
      } else {
        formData['clearedAssetListDataJson'] = jsonEncode([]);
      }
      await workflowActionService.restoreAssetScanState(formData);
    } finally {
      hideLoading();
      Get.until(
        id: SharedNavBarEnum.workflow.navigatorId,
        (route) => route.settings.name == AutoRoutes.applicationView,
      );
      // await Get.offNamedUntil(
      //   AutoRoutes.applicationView,
      //   (route) => route.settings.name == AutoRoutes.appTabWorkflowTabs,
      //   id: SharedNavBarEnum.workflow.navigatorId,
      // );
    }
  }
}
