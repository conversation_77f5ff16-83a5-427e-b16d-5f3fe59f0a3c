import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/data/models/process_definition_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/domain/usecases/get_workflow_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/state/workflow_new_application_ui_state.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_tabs/presentation/controllers/workflow_tabs_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 工作流新申请页面的控制器
/// 负责处理工作流新申请列表的数据加载、搜索和交互逻辑
class WorkflowNewApplicationController extends BaseController {
  /// 依赖注入
  final GetNewWorkflowListUseCase getNewWorkflowListUseCase;

  final NavigationService navigationService;

  /// 构造函数
  WorkflowNewApplicationController({required this.getNewWorkflowListUseCase, required this.navigationService});

  final WorkflowNewApplicationUIState state = WorkflowNewApplicationUIState();

  /// 状态变量
  /// 滚动控制器，用于监听和控制列表滚动
  ScrollController? scrollController;
  void initializeListener(ScrollController controller) {
    if (scrollController != null) {
      scrollController!.removeListener(_onScroll);
    }
    scrollController = controller;
    scrollController!.addListener(_onScroll);
  }

  /// 生命周期方法
  @override
  void onInit() {
    super.onInit();
    if (scrollController != null) {
      scrollController!.addListener(_onScroll);
    }

    _loadData();

    // 监听排序状态变化
    final workflowTabsController = Get.find<WorkflowTabsController>();
    ever(workflowTabsController.isNewApplicationAscending, (bool isAscending) {
      sortItems(isAscending: isAscending);
    });
  }

  @override
  void onClose() {
    if (scrollController != null) {
      scrollController!.dispose();
    }
    super.onClose();
  }

  /// 公共方法
  /// 刷新数据，由下拉刷新触发
  Future<void> refreshData() async {
    await _loadData();
  }

  // 添加排序方法
  void sortItems({required bool isAscending}) {
    // 对原始列表排序
    state.items.sort((a, b) {
      if (isAscending) {
        return a.workflowName.compareTo(b.workflowName);
      } else {
        return b.workflowName.compareTo(a.workflowName);
      }
    });

    // 对过滤后的列表排序
    // 注意：我们需要创建一个新的列表来触发 Obx 的更新
    final currentFilteredItems = state.filteredItems.toList();
    currentFilteredItems.sort((a, b) {
      if (isAscending) {
        return a.workflowName.compareTo(b.workflowName);
      } else {
        return b.workflowName.compareTo(a.workflowName);
      }
    });
    state.filteredItems.value = currentFilteredItems;
  }

  // 修改搜索方法，保持排序状态
  void onSearchChanged(String query) {
    state.currentSearchQuery.value = query;
    _applySearchAndSort(query);
  }

  /// 提取搜索和排序逻辑到单独的方法
  void _applySearchAndSort(String query) {
    if (query.isEmpty) {
      state.filteredItems.value = state.items.toList();
    } else {
      state.filteredItems.value = state.items
          .where(
            (item) =>
                (item.workflowName.toLowerCase().contains(query.toLowerCase())) ||
                (item.assetTypeName?.toLowerCase().contains(query.toLowerCase()) ?? false),
          )
          .toList();
    }

    // 应用当前的排序状态
    final workflowTabsController = Get.find<WorkflowTabsController>();
    sortItems(isAscending: workflowTabsController.isNewApplicationAscending.value);

    _updateEmptyState();
  }

  /// 列表项点击事件处理
  Future<void> onItemTap(ProcessDefinitionModel item) async {
    // TODO: Implement item tap handling
    final workflowTypeCode = item.workflowTypeCode;
    final hasAssetTypeName = item.assetTypeName != null && item.assetTypeName != '';
    if (workflowTypeCode == null) {
      await CommonDialog.show(content: 'ワークフロー壊れた！！', confirmText: 'はい');
      LogUtil.e('ワークフロー壊れた！');
      return;
    }
    await navigationService.navigateTo(
      AutoRoutes.newForm,
      id: SharedNavBarEnum.workflow.navigatorId,
      arguments: WorkflowArgumentModel(
        hasAssetTypeName: hasAssetTypeName,
        processDefinitionId: item.processDefinitionId ?? '',
        workflowId: item.workflowId,
        workflowName: item.workflowName,
        assetTypeIdWithFirstWf: item.assetTypeIdWithFirstWf,
        assetTypeName: item.assetTypeName,
      ),
    );
    await refreshData();
  }

  /// 滚动到列表顶部
  void scrollToTop() {
    if (scrollController != null) {
      scrollController!.animateTo(0, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    }
  }

  /// 私有方法
  /// 监听滚动事件
  void _onScroll() {
    if (scrollController == null) return;
    state.showScrollToTop.value = scrollController!.offset > 100;
  }

  /// 加载工作流列表数据
  Future<void> _loadData() async {
    try {
      state.items.value = await getNewWorkflowListUseCase(const NoParams());
      // 应用当前的搜索词和排序
      _applySearchAndSort(state.currentSearchQuery.value);
    } catch (e, stackTrace) {
      handleException(e, stackTrace);
    } finally {
      state.isInitialLoading.value = false;
    }
  }

  /// 更新列表空状态
  void _updateEmptyState() {
    state.isEmpty.value = state.filteredItems.isEmpty;
  }
}

class WorkflowArgumentModel {
  String processDefinitionId;
  String workflowName;
  bool hasAssetTypeName;
  Map<String, List<AssetItemListModel>>? assetDict;
  List<CommentsModel>? comments;
  List<WorkflowAssignDynamicTaskModel>? dynamicTaskInfo;
  List? dynamicTantousha;
  bool? isSubmit;
  int? assetTypeIdWithFirstWf;
  int? workflowId;
  String? assetTypeName;
  WorkflowArgumentModel({
    required this.processDefinitionId,
    required this.hasAssetTypeName,
    required this.workflowName,
    this.assetDict,
    this.comments,
    this.dynamicTaskInfo,
    this.dynamicTantousha,
    this.isSubmit,
    this.assetTypeIdWithFirstWf,
    this.workflowId,
    this.assetTypeName,
  });
}
