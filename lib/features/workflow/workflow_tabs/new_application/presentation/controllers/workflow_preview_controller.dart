import 'dart:convert';

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/datetime_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/number_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/master_display_option_model.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:get/get.dart';

class WorkflowPreviewController extends BaseController {
  Map<String, List<AssetItemListModel>> itemData;
  List<CommentsModel> comments;
  final bool isFromAction;
  final bool isShowDefaultValue;
  final WorkflowActionService workflowActionService;
  final Function? onLoadComplete;
  WorkflowPreviewController({
    required this.itemData,
    required this.comments,
    this.isFromAction = false,
    this.isShowDefaultValue = false,
    required this.workflowActionService,
    this.onLoadComplete,
  }) {}
  bool _isDisposed = false;
  final RxList<String> sectionNameArray = <String>[].obs;
  final Map<String, dynamic>? dynamicItemDicData = null;
  final RxBool isShowCommit = true.obs;
  final RxString sectionTitle = '入力情報'.obs;
  final RxMap<String, List<AssetItemListModel>> processedItemData = <String, List<AssetItemListModel>>{}.obs;
  @override
  void onReady() {
    super.onReady();
    setInitData(itemData);
  }

  @override
  void onClose() {
    _isDisposed = true;
    super.onClose();
  }

  /// 引渡されたデータより、画面起動が必要な変数を初期化する
  void setInitData(Map<String, List<AssetItemListModel>> itemData) async {
    try {
      final commonController = Get.find<WorkflowCommonController>();
      final Map<String, List<AssetItemListModel>> newItemData = {};
      // final List<String> sectionNameArray = [];
      final List<Map<String, dynamic>> sectionSortItemArray = [];
      final getTUrlUseCase = Get.put(GetTurlUseCase(s3Repository: Get.find()));
      await commonController.getUserRoleUseCaseUseCase(const NoParams());
      for (var sectionName in itemData.entries) {
        final entries = sectionName.value;
        for (var items in entries)
          if (items.itemType == 'homeImage' || items.itemType == 'image') {
            // items.loaded = false;
            if (items.itemType == 'image' && items.defaultData is List) {
              for (var data in items.defaultData) {
                data['loaded'] = false;
              }
            }
          }
        if (entries.length == 0) continue;
        if (sectionName != 'null') {
          final firstItem = entries.first;
          final isView = commonController.checkIsView(firstItem.optionObject?.sectionPrivateGroups ?? '');
          if (isView && firstItem.sectionType != 'spread') {
            // 添加排序信息
            sectionSortItemArray.add({'sectionName': sectionName.key, 'sectionSort': firstItem.sectionSort});
            // セクション名配列に保持する
            sectionNameArray.add(sectionName.key);
            newItemData[sectionName.key] = [];
            for (var item in entries) {
              if (!item.itemValue.toString().contains('assetItemName:')) {
                item.valueForShow = item.defaultData;
              }
              // ダイナミックな動的値
              if (item.itemType == 'input' && item.method == '2') {
                if (!item.itemValue.toString().contains('assetItemName:')) {
                  if (dynamicItemDicData != null) {
                    final str = item.itemValue.toString();
                    final index = str.split('_').length;
                    if (index > 1) {
                      final itemId = str.split('_').last;
                      item.valueForShow = dynamicItemDicData![itemId]?.toString() ?? '';
                    } else {
                      item.valueForShow = dynamicItemDicData![str]?.toString() ?? '';
                    }
                  }
                }
              }
              if ((item.itemType == 'input' && item.method != '2') ||
                  item.itemType == 'email' ||
                  (item.itemValue == 'currentUserInput' && item.itemType == 'input' && item.method == '2')) {
                if (item.itemValue is String || item.defaultData is String) {
                  final name = await commonController.getDynamicSettingName(item.itemValue ?? item.defaultData);
                  if (name != null && name != '') {
                    item.defaultData = name;
                  }
                }
              }
              // 日付項目の初期設定
              if (item.itemType == 'date' && item.defaultData != null) {
                if (item.defaultData == 'currentUserInput') {
                  item.defaultData = '';
                  item.valueForShow = '';
                }
                final dataForInit = getDateFromDateType(
                  item,
                  item.defaultData,
                  item.optionObject?.dateType ?? '',
                  itemData,
                  sectionName.key,
                );
                final splits = item.defaultData.toString().split('_');
                if (item.defaultData != '') {
                  if (item.defaultData == 'now' || item.defaultData == '<now>') {
                    item.defaultData = '現在';
                    item.valueForShow = '現在';
                  } else if (item.defaultData == 'today' || item.defaultData == '<today>') {
                    item.defaultData = '今日';
                    item.valueForShow = '今日';
                  } else if (splits[0] == 'after') {
                    item.defaultData = dataForInit;
                  }
                } else {
                  if (item.itemValue != null) {
                    item.defaultData = item.itemValue;
                  }
                  if (item.defaultData == 'currentUserInput') {
                    item.defaultData = '';
                  }
                }
                if (item.defaultData == 'now' || item.defaultData == '<now>') {
                  item.valueForShow = '現在';
                } else if (item.defaultData == 'today' || item.defaultData == '<today>') {
                  item.valueForShow = '今日';
                }
              }
              // 実行時に入力
              if (item.itemValue == 'currentUserInput' && item.method == '2') {
                if (item.defaultData != '個別に設定されています') {
                  if (item.defaultData == null ||
                      item.defaultData == 'undefined' ||
                      item.defaultData == 'currentUserInput') {
                    item.defaultData = '';
                  }
                }
                if (item.valueForShow == null ||
                    item.valueForShow == 'undefined' ||
                    item.valueForShow == 'currentUserInput') {
                  item.valueForShow = '';
                }
              }
              // ブランクに設定
              const blankItemTypeList = [
                'groupSelect',
                'userSelect',
                'input',
                'textarea',
                'currency',
                'number',
                'list',
                'date',
              ];
              if (item.itemValue == 'valueBlank' && blankItemTypeList.contains(item.itemType)) {
                setShowValue(item);
              }

              // Master 类型处理
              if (item.itemType == 'master') {
                //defaultData.displayにマスタ初期値を入れる
                if (isShowDefaultValue) {
                  if (item.masterId != null && item.defaultData['display'] == null) {
                    final display = {};
                    if (item.optionObject?.masterDisplayItems != null)
                      for (var tmpItem in item.optionObject!.masterDisplayItems!) {
                        final masterDefaultValue = await getMasterDefaultValue(
                          item.optionObject!.masterTypeId,
                          item.masterId ?? '',
                          tmpItem.itemName ?? '',
                        );
                        display[tmpItem.itemId] = masterDefaultValue;
                      }
                    item.defaultData = {'masterId': item.masterId, 'display': display};
                  }
                }
                showMasterDisplayItems(item);
              }

              // 数字类型处理
              if (item.itemType == 'number') {
                final option = item.optionObject;
                final decimalPoint = option?.numberDecimalPoint ?? '0';
                final commaDecimalPoint = option?.numberCommaDecimalPoint ?? '0';
                final unitFlg = option?.unitFlg;
                final percentage = option?.percentage;

                if (unitFlg == '1' && item.unit == null) {
                  item.unit = option?.unit;
                }
                if (percentage == '1' && item.percentage == null) {
                  item.percentage = NumberUtils.getPercentage(item.defaultData, decimal: int.parse(decimalPoint));
                } else if (commaDecimalPoint != '0') {
                  item.itemValue = NumberUtils.numberFormat(
                    number: item.defaultData.toString(),
                    comma: commaDecimalPoint,
                    decimal: decimalPoint,
                  );
                }
              }

              // 图片和签名处理
              if (item.itemType == 'image' || item.itemType == 'homeImage' || item.itemType == 'digitalSign') {
                if (item.defaultData != null && item.defaultData is List) {
                  for (var defaultData in item.defaultData) {
                    defaultData['turl'] = await getTUrlUseCase(defaultData['url'] ?? '');
                  }
                }
              }
              if (newItemData[sectionName.key] != null) newItemData[sectionName.key]!.add(item);
            }
          }
        }
      }
      sectionNameArray.value = createSectionNameArray(sectionSortItemArray);
      processedItemData.value = newItemData;
      onLoadComplete?.call();
    } catch (e) {
      LogUtil.d('workflow preview load error: $e');
      onLoadComplete?.call();
    }
  }

  void clearLists() {
    if (!_isDisposed) {
      // 使用 Future.microtask 确保在正确的时机更新状态
      Future.microtask(() {
        if (!_isDisposed) {
          sectionNameArray.clear();
          processedItemData.clear();
        }
      });
    }
  }

  /// セクションの並び順を調整
  List<String> createSectionNameArray(List<Map<String, dynamic>> itemArray) {
    itemArray.sort((a, b) => a['sectionSort'].compareTo(b['sectionSort']));
    final List<String> sectionNameArray = [];
    for (var item in itemArray) {
      sectionNameArray.add(item['sectionName'] as String);
    }
    return sectionNameArray;
  }

  Future<void> toEditPage() async {
    if (isFromAction && itemData.isEmpty) {
      return;
    } else {
      final queryParams = {'assetDict': itemData, 'comments': comments, 'workflowName': sectionTitle};
      final navigationService = Get.find<NavigationService>();
      final Map<String, dynamic>? result =
          await navigationService.navigateTo(AutoRoutes.editPage, arguments: queryParams) as Map<String, dynamic>?;
      if (result?['comments'] != null) {
        comments = result?['comments'];
      }
      if (result?['assetDict'] != null) {
        itemData = result?['assetDict'];
        processedItemData.refresh();
      }
    }
  }

  bool isNeedCheck(AssetItemListModel item) {
    if (item.inputFlg == '1' || (item.editable ?? false)) {
      return true;
    }
    return false;
  }

  /*
   * 一括更新処理の更新項目の更新先は資産項目かどうか
   */
  bool isAssetItemValue(AssetItemListModel assetTypeItem) {
    return assetTypeItem.itemValue.toString().contains('assetItemName:');
  }

  String getUserNameForShow(AssetItemListModel item) {
    var userInfo = item.defaultData ?? {};
    if (userInfo.isNotEmpty && isJsonString(userInfo)) {
      userInfo = jsonDecode(userInfo);
    } else if (userInfo == '' && userInfo is String && !isJsonString(userInfo)) {
      userInfo = {};
    }
    return userInfo['userName'] ?? '';
  }

  bool conversionOfValue(dynamic defaultData, dynamic checkItem) {
    // 如果是 List，直接处理
    if (defaultData is List) {
      return defaultData.contains(checkItem);
    }
    // 非 String 类型，先转换为 String
    if (defaultData is! String) {
      return defaultData.toString().contains(checkItem);
    }
    return defaultData.contains(checkItem);
  }

  bool isJsonString(dynamic str) {
    try {
      if (str is String && jsonDecode(str) is Map) {
        return true;
      }
    } catch (e) {
      return false;
    }
    return false;
  }

  Future<void> showMasterDisplayItems(AssetItemListModel assetTypeItem) async {
    List<Map<String, dynamic>> displayList = [];
    final masterItems = assetTypeItem.optionObject?.masterDisplayItems;
    final dynamic originalDefaultData = assetTypeItem.defaultData;

    if (masterItems == null || masterItems.isEmpty) {
      assetTypeItem.displayList = displayList;
      return;
    }
    if (originalDefaultData is Map &&
        originalDefaultData.containsKey('display') &&
        originalDefaultData['display'] is Map &&
        originalDefaultData['display'] != null) {
      final Map defaultDisplayMap = originalDefaultData['display'];
      for (var temp in masterItems) {
        final item = <String, dynamic>{};
        item['itemValue'] = defaultDisplayMap[temp.itemId];
        item['itemName'] = temp.itemDisplayName ?? temp.itemName;
        if (temp.option != null && temp.option!.isNotEmpty) {
          try {
            final option = jsonDecode(temp.option!);
            if (temp.itemType == 'checkbox' && option['checkboxMultiFlg'] == '0') {
              item['type'] = 'checkbox';
            } else if (temp.itemType == 'number') {
              item['type'] = 'number';
              final String? commaDecimalPoint = option['numberCommaDecimalPoint']?.toString();
              final String? decimalPoint = option['numberDecimalPoint']?.toString();
              final String? percentageFlag = option['percentage']?.toString();
              final String? unitFlg = option['unitFlg']?.toString();

              if (unitFlg == '1') {
                item['unit'] = option['unit']?.toString();
              }
              if (percentageFlag == '1') {
                item['percentage'] = NumberUtils.getPercentage(
                  item['itemValue'] ?? '',
                  decimal: NumberUtils.getNum(decimalPoint),
                );
              } else if (commaDecimalPoint != null && commaDecimalPoint != '0' && decimalPoint != null) {
                item['itemValue'] = NumberUtils.numberFormat(
                  number: item['itemValue'] ?? '',
                  comma: commaDecimalPoint,
                  decimal: decimalPoint,
                );
              }
            }
          } catch (e) {
            LogUtil.d('解析 Master Item Option 出错 (itemId: ${temp.itemId}): $e');
          }
        }
        displayList.add(item);
      }
    } else if (originalDefaultData is Map &&
        originalDefaultData.containsKey('display') &&
        originalDefaultData['display'] is String &&
        originalDefaultData['display'] != null &&
        (originalDefaultData['display'] as String).isNotEmpty) {
      if (masterItems.isNotEmpty) {
        final item = <String, dynamic>{};
        final temp = masterItems.first;
        item['itemValue'] = originalDefaultData['display'];
        item['itemName'] = temp.itemDisplayName ?? temp.itemName;
        displayList.add(item);
      }
    } else if (originalDefaultData is String && originalDefaultData.isNotEmpty) {
      if (masterItems.isNotEmpty) {
        final item = <String, dynamic>{};
        final temp = masterItems.first;
        item['itemValue'] = originalDefaultData;
        item['itemName'] = temp.itemDisplayName ?? temp.itemName;
        displayList.add(item);
      }
    } else {
      for (var temp in masterItems) {
        final item = <String, dynamic>{};
        item['itemValue'] = '';
        item['itemName'] = temp.itemDisplayName ?? temp.itemName;
        item['itemId'] = temp.itemId;
        displayList.add(item);
      }
    }
    if (displayList.length > 5) {
      displayList = displayList.take(5).toList();
    }
    assetTypeItem.displayList = displayList;
  }

  bool masterType(AssetItemListModel assetTypeItem) {
    if (assetTypeItem.optionObject != null) {
      if (assetTypeItem.optionObject?.masterDisplayItems != null &&
          assetTypeItem.optionObject!.masterDisplayItems!.length > 0)
        return true;
    }
    return false;
  }

  String getDateFromDateType(
    AssetItemListModel item,
    String defaultDate,
    String? dateType,
    Map<String, List<AssetItemListModel>> allItemData,
    String sectionName,
  ) {
    String returnData = defaultDate;
    // 根据 dateType 确定 TS 日期格式 (用于传递给 getFormatDateHasTime)
    final String tsDateFormat = dateType == 'dateTime' ? 'YYYY/MM/DD HH:mm' : 'YYYY/MM/DD';
    // 将输入转为小写以便比较
    final String lowerDefaultDate = defaultDate.toLowerCase();
    // 分割字符串以检查 "after_" 格式
    final splits = defaultDate.split('_');
    if (splits.length > 0 && splits[0].toLowerCase() == 'after') {
      // 检查 splits 长度是否为 3，以匹配 dateDeal 的预期
      if (splits.length != 3) {
        return '';
      }
      dynamic loginDate;
      bool isHave = false; // 标记是否找到了对应的 item
      final sectionItems = allItemData[sectionName]; // 获取当前 section 的所有 item
      if (sectionItems != null) {
        for (final tempItem in sectionItems) {
          if (tempItem.itemName == 'createdDate' || tempItem.itemName == '新規登録時間') {
            isHave = true;
            if (tempItem.defaultData != null) {
              // 获取找到的日期值
              loginDate = tempItem.defaultData;
              returnData = dateDeal(loginDate, defaultDate, tsDateFormat);
              return returnData;
            } else {
              returnData = dateDeal('new', defaultDate, tsDateFormat);
              return returnData;
            }
          } else {
            returnData = dateDeal('new', defaultDate, tsDateFormat);
            return returnData;
          }
        }
      }
      // 如果遍历完整个 section 仍未找到 'createdDate' 或 '新規登録時間'
      if (!isHave) {
        return '';
      }
    } else if (lowerDefaultDate == 'today' ||
        lowerDefaultDate == '<today>' ||
        lowerDefaultDate == 'now' ||
        lowerDefaultDate == '<now>') {
      returnData = DateTimeUtils.getFormatDateHasTime(date: DateTime.now(), format: tsDateFormat);
    } else {
      returnData = DateTimeUtils.getFormatDateHasTime(date: defaultDate, format: tsDateFormat);
    }
    return returnData;
  }

  String dateDeal(String date, String str, dynamic dateFormat) {
    String value = '';
    String number = '';
    String time = '';
    final splits = str.split('_');
    if (splits.length == 3) {
      number = splits[1];
      time = splits[2];
    }
    switch (time) {
      case 'day':
        if (date == 'new') {
          value = DateTimeUtils.addDaysAndFormat(date: null, numberOfDays: number, dateFormat: '');
        } else {
          value = DateTimeUtils.addDaysAndFormat(date: null, numberOfDays: number, dateFormat: '');
        }
        break;
      case 'month':
        if (date == 'new') {
          value = DateTimeUtils.addMonthsAndFormat(date: null, dateFormat: dateFormat, numberOfMonths: number);
        } else {
          value = DateTimeUtils.addMonthsAndFormat(date: null, dateFormat: dateFormat, numberOfMonths: number);
        }
        break;
      case 'year':
        if (date == 'new') {
          value = DateTimeUtils.addYearsAndFormat(date: null, dateFormat: dateFormat, numberOfYears: number);
        } else {
          value = DateTimeUtils.addYearsAndFormat(date: null, dateFormat: dateFormat, numberOfYears: number);
        }
        break;
      default:
        break;
    }

    return value;
  }

  List<String> getGroupSelectForShow(AssetItemListModel assetTypeItem) {
    dynamic groupInfo = assetTypeItem.defaultData ?? {};
    if (groupInfo is String && groupInfo.isNotEmpty) {
      groupInfo = jsonDecode(groupInfo);
    }
    final List<String> groupNameList = [];
    if (groupInfo != null && groupInfo.isNotEmpty) {
      if (groupInfo[0] is int) {
        groupInfo = workflowActionService.getGroupSelectInfo(assetTypeItem.defaultData);
      }
      for (var group in groupInfo) {
        groupNameList.add(group['groupName']);
      }
    }
    return groupNameList;
  }

  Future<String> getMasterDefaultValue(int? masterTypeId, String masterId, String itemName) async {
    String masterListDefaultValue = '';
    if (masterTypeId != null && masterId.isNotEmpty && itemName.isNotEmpty) {
      final masterDetail = await workflowActionService.getMasterInfoById(masterTypeId);
      for (var master in masterDetail) {
        if (masterId.isNotEmpty && itemName.isNotEmpty) {
          if (master.masterId.toString() == masterId && master.masterText != null && master.masterText!.isNotEmpty) {
            final Map<String, dynamic> masterText = jsonDecode(master.masterText!);
            masterListDefaultValue = masterText[itemName] ?? '';
          }
        }
      }
    }
    return masterListDefaultValue;
  }

  String showMasterValue(MasterDisplayItemModel masterDisplayItem) {
    final optionStr = jsonDecode(masterDisplayItem.option ?? '{}');
    final masterDisplayOptionModel = MasterDisplayOptionModel.fromJson(optionStr);
    final itemValue = masterDisplayItem.itemValue;

    // Checkbox
    if (masterDisplayItem.itemType == 'checkbox') {
      // 单选
      if (masterDisplayOptionModel.checkboxMultiFlg == '0') {
        return itemValue == '1' ? 'あり' : 'なし';
      }
      // 多选
      if (itemValue != null && itemValue != '') {
        final itemValues = itemValue is String ? jsonDecode(itemValue) as List<dynamic>? : itemValue;
        return itemValues?.join(',') ?? '';
      }
    }
    final result = itemValue.toString().replaceAll('\n', ' ');
    return result;
  }

  /**
   * 値を得る
   */
  String? getShowValue(AssetItemListModel item) {
    String str = item.defaultData?.toString() ?? '';
    if (str.isEmpty || str == 'undefined' || str == 'null') {
      str = item.itemValue?.toString() ?? '';
    }
    final index = str.split('_').length;
    if (index > 1) {
      final String itemId = str.split('_').last;
      return dynamicItemDicData?[itemId]?.toString();
    } else {
      return str.isEmpty ? '' : dynamicItemDicData?[str]?.toString();
    }
  }

  void setShowValue(AssetItemListModel item) {
    final String? resultShowData = getShowValue(item);
    if (resultShowData != null && resultShowData != 'undefined') {
      item.valueForShow = resultShowData;
      item.defaultData = resultShowData;
    }
  }

  String valueForShow(AssetItemListModel assetItem, {valueType = 'default'}) {
    String value = '';
    // 货币类型处理
    if (assetItem.itemType == 'currency') {
      final option = jsonDecode(assetItem.option ?? '');
      if (option is Map) {
        // 有没有逗号分隔符0没有、1有
        final int numberCommaDecimalPoint = int.tryParse(option['numberCommaDecimalPoint'] ?? '3') ?? 3;
        // 小数点的位数
        final int numberDecimalPoint =
            int.tryParse(option['numberDecimalPoint'] ?? option['currencyDecimalPoint']) ?? 2;

        // 处理货币格式
        value =
            option['currencyType'] +
            ' ' +
            NumberUtils.formatNumber(assetItem.defaultData, numberCommaDecimalPoint, numberDecimalPoint);
      }
    } else {
      if (valueType == 'default') {
        if (assetItem.itemValue is String && assetItem.itemValue.contains('assetItemName:')) {
          value = assetItem.valueForShow ?? '';
        } else if (assetItem.percentage != null && assetItem.defaultData.isNotEmpty) {
          value = assetItem.percentage!;
        } else {
          try {
            final option = jsonDecode(assetItem.option ?? '');
            if (option is Map) {
              final int numberCommaDecimalPoint = int.tryParse(option['numberCommaDecimalPoint'] ?? '0') ?? 0;
              final int numberDecimalPoint = int.tryParse(option['numberDecimalPoint'] ?? '0') ?? 0;

              if (assetItem.itemType == 'number' && (numberDecimalPoint != 0 || numberCommaDecimalPoint != 0)) {
                value = NumberUtils.formatNumber(assetItem.defaultData, numberCommaDecimalPoint, numberDecimalPoint);
              } else {
                value = assetItem.defaultData ?? '';
              }
            }
          } catch (e) {
            value = assetItem.defaultData ?? '';
          }
        }
      } else if (valueType == 'show') {
        if (assetItem.itemValue is String && assetItem.itemValue.contains('assetItemName:')) {
          value = assetItem.valueForShow ?? '';
        } else if (assetItem.percentage != null && assetItem.valueForShow != '') {
          value = assetItem.percentage!;
        } else {
          try {
            final option = jsonDecode(assetItem.option ?? '');
            if (option is Map) {
              final int numberCommaDecimalPoint = int.tryParse(option['numberCommaDecimalPoint'] ?? '0') ?? 0;
              final int numberDecimalPoint = int.tryParse(option['numberDecimalPoint'] ?? '0') ?? 0;

              if (assetItem.itemType == 'number' && (numberDecimalPoint != 0 || numberCommaDecimalPoint != 0)) {
                value = NumberUtils.formatNumber(assetItem.valueForShow, numberCommaDecimalPoint, numberDecimalPoint);
              } else {
                value = assetItem.valueForShow ?? '';
              }
            }
          } catch (e) {
            value = assetItem.valueForShow ?? '';
          }
        }
      }
    }

    // 如果有单位，添加单位
    if (assetItem.unit != null && value.isNotEmpty) {
      value = value + ' ' + assetItem.unit!;
    }
    return value;
  }
}
