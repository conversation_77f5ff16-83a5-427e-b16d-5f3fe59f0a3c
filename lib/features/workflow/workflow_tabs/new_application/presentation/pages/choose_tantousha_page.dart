import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/choose_tantousha_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/choose_tantousha_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/chooseTantousha', isConst: false, binding: ChooseTantoushaBinding)
class ChooseTantoushaPage extends GetView<ChooseTantoushaController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('担当者選択')),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 10),
            for (int index = 0; index < controller.state.dynamicTaskInfo.length; index++) ...{chooseItem(index)},
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                WorkflowButton(
                  text: 'OK',
                  buttonColor: const Color(0xFF0B3E86),
                  textColor: Colors.white,
                  onTapCallback: controller.apply,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget chooseItem(int index) {
    return InkWell(
      onTap: () async {
        await controller.chooseTantousha(index);
      },
      child: Container(
        // constraints: const BoxConstraints(minHeight: 70),
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 4, offset: const Offset(0, 2))],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: controller.state.dynamicTaskInfo[index].taskDefName ?? '',
                      style: const TextStyle(fontFamily: 'NotoSansJP', fontSize: 16, color: Colors.black),
                    ),
                    const TextSpan(
                      text: '※',
                      style: TextStyle(
                        fontFamily: 'NotoSansJP',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      constraints: const BoxConstraints(maxWidth: 150), // 限制最大宽度
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Obx(() {
                        final title = controller.chooseTitles[index] ?? '未選択';
                        return Text(
                          title,
                          maxLines: 1,
                          style: TextStyle(
                            fontFamily: 'NotoSansJP',
                            overflow: TextOverflow.ellipsis,
                            fontSize: 12,
                            color: title == '未選択' ? Colors.red : Colors.black,
                            fontWeight: FontWeight.w700,
                          ),
                        );
                      }),
                    ),
                    const Icon(Icons.chevron_right, color: Colors.black),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
