import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/bindings/af_customize_view_bindings.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/widgets/expandable_section.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/new_application_form_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/new_application_form_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_comments_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_appbar.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_comments_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

/// WF新规
@GetRoutePage('/newForm', isConst: false, bindings: [NewApplicationFormBinding, AfCustomizeViewBindings])
class NewApplicationFormPage extends GetView<NewApplicationFormController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: WorkflowAppBar(
          title: controller.state.workflowName.value,
          leading: IconButton(icon: const Icon(size: 37, Icons.chevron_left), onPressed: controller.back),
        ),
        body: SafeArea(
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Obx(() {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // 没放入下面的padding 防止刚进入时コメント突兀
                      const SizedBox(height: 16),
                      controller.state.stepName.value.isNotEmpty
                          ? Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 30.0),
                              child: ConstrainedBox(
                                // 设置最大宽度限制
                                constraints: BoxConstraints(maxWidth: Get.width - 60),
                                // 为了让下划线跟随文字长度变化
                                child: IntrinsicWidth(
                                  child: Column(
                                    children: [
                                      Padding(
                                        // 下划线比文字稍长
                                        padding: const EdgeInsets.only(left: 4, right: 4, bottom: 6),
                                        child: Text(
                                          controller.state.stepName.value,
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      ),
                                      Container(color: Colors.white, height: 2),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : const SizedBox.shrink(),
                      if (controller.state.assetDict.isNotEmpty)
                        AfCustomizeView(
                          needEnterValidate: false,
                          instance: '',
                          assetDict: controller.state.assetDict,
                          scene: AfCustomizeViewScene.assetDetail,
                        ),
                      ExpandableSection(
                        title: 'コメント',
                        hasPaddingTop: false,
                        child: GetBuilder<WorkflowCommentsController>(
                          init: WorkflowCommentsController(comments: controller.state.comments),
                          builder: (controller) {
                            return WorkflowCommentsWidget();
                          },
                        ),
                      ),
                      controller.state.isHasTantoushaF == true &&
                              controller.state.dynamicTaskInfo.length > 0 &&
                              controller.state.isSubmit == true
                          ? ExpandableSection(
                              title: '担当者',
                              hasPaddingTop: false,
                              child: Container(
                                width: double.infinity,
                                padding: const EdgeInsets.only(right: 5, top: 5, bottom: 5),
                                child: Column(
                                  children: controller.state.dynamicTaskInfo.asMap().entries.map((entries) {
                                    final int i = entries.key;
                                    final item = entries.value;
                                    return InkWell(
                                      onTap: () {
                                        controller.chooseTantousha(i);
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 10.0),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: RichText(
                                                text: TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text: item.taskDefName ?? '',
                                                      style: const TextStyle(
                                                        fontFamily: 'NotoSansJP',
                                                        fontSize: 16,
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                    const TextSpan(
                                                      text: '※',
                                                      style: TextStyle(
                                                        fontFamily: 'NotoSansJP',
                                                        fontSize: 16,
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.red,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                softWrap: true,
                                                overflow: TextOverflow.visible,
                                              ),
                                            ),
                                            const SizedBox(width: 4),
                                            Expanded(
                                              child: Obx(() {
                                                return Text(
                                                  controller.getTantoushaInfoWithIndex(i),
                                                  overflow: TextOverflow.visible,
                                                );
                                              }),
                                            ),
                                            const SizedBox(width: 4),
                                            const Icon(Icons.arrow_forward_ios, size: 14, color: Colors.black),
                                          ],
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                            )
                          : const SizedBox(),
                    ],
                  );
                }),
              ),
            ],
          ),
        ),
        bottomNavigationBar: Obx(() {
          return WorkflowBottomWidget(
            process: WorkflowProcess.newApplication,
            workflowRaiseButtonNameList: controller.state.buttonNameList,
            buttonNum: controller.state.buttonNum.value,
            isNextEnable: controller.state.isNextEnable.value,
            isSubmit: controller.state.isSubmit.value,
            apply: controller.applyNewApplication,
            save: controller.newApplicationSave,
            next: controller.next,
          );
        }),
      );
    });
  }
}
