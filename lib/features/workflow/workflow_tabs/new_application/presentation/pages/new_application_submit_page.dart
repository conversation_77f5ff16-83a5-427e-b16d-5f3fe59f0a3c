import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/widgets/expandable_section.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/new_application_submit_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/new_application_submit_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/pages/workflow_preview_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

/// WF新规
@GetRoutePage('/newForm/submit', isConst: false, bindings: [NewApplicationSubmitBinding])
class NewApplicationSubmitPage extends GetView<NewApplicationSubmitController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          return Text(
            controller.state.workflowName.value,
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w700, fontSize: 20),
            // maxLines: 3,
            textAlign: TextAlign.center,
          );
        }),
        leading: IconButton(icon: const Icon(size: 37, Icons.chevron_left), onPressed: controller.back),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Obx(() {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                controller.state.stepName.value.isNotEmpty
                    ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 30.0, vertical: 16),
                        child: ConstrainedBox(
                          // 设置最大宽度限制
                          constraints: BoxConstraints(maxWidth: Get.width - 60),
                          // 为了让下划线跟随文字长度变化
                          child: IntrinsicWidth(
                            child: Column(
                              children: [
                                Padding(
                                  // 下划线比文字稍长
                                  padding: const EdgeInsets.only(left: 4, right: 4, bottom: 6),
                                  child: Text(
                                    controller.state.stepName.value,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ),
                                Container(color: Colors.white, height: 2),
                              ],
                            ),
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
                if (controller.state.inputAssetListFlag.value != '0')
                  ExpandableSection(
                    title: '資産情報',
                    hasPaddingTop: false,
                    child: InkWell(
                      onTap: controller.toAssetListPage,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  // 第一行
                                  Row(
                                    children: [
                                      SizedBox(
                                        width: Get.width / 3 - 20,
                                        child: Text(
                                          '資産数',
                                          style: TextStyle(color: Colors.black.withAlpha(140)),
                                          textAlign: TextAlign.left,
                                        ),
                                      ),
                                      Expanded(
                                        child: Obx(() {
                                          return Text(
                                            '${controller.state.dataCount.value}件',
                                            textAlign: TextAlign.left,
                                          );
                                        }),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  // 第二行
                                  Row(
                                    children: [
                                      SizedBox(
                                        width: Get.width / 3 - 20,
                                        child: Text(
                                          '資産種類',
                                          style: TextStyle(color: Colors.black.withAlpha(140)),
                                          textAlign: TextAlign.left,
                                        ),
                                      ),
                                      Expanded(
                                        child: Obx(() {
                                          return Text(controller.state.assetTypeName.value, textAlign: TextAlign.left);
                                        }),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            const Icon(Icons.chevron_right, color: Colors.black),
                          ],
                        ),
                      ),
                    ),
                  ),
                ExpandableSection(
                  title: '入力情報',
                  hasPaddingTop: false,
                  child: WorkflowPreviewPage(
                    itemData: controller.state.assetDict,
                    comments: controller.state.comments,
                    workflowActionService: controller.workflowActionService,
                  ),
                ),
                controller.state.isHasTantoushaF.value &&
                        controller.state.dynamicTaskInfo.length > 0 &&
                        controller.state.isSubmit.value
                    ? ExpandableSection(
                        title: '担当者',
                        hasPaddingTop: false,
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.only(right: 5, top: 5, bottom: 5),
                          child: Column(
                            children: controller.state.dynamicTaskInfo.asMap().entries.map((entries) {
                              final item = entries.value;
                              return Padding(
                                padding: const EdgeInsets.symmetric(vertical: 10.0),
                                child: GestureDetector(
                                  onTap: () {},
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: RichText(
                                          text: TextSpan(
                                            children: [
                                              TextSpan(
                                                text: item.taskDefName ?? '',
                                                style: const TextStyle(
                                                  fontFamily: 'NotoSansJP',
                                                  fontSize: 16,
                                                  color: Colors.black,
                                                ),
                                              ),
                                              const TextSpan(
                                                text: '※',
                                                style: TextStyle(
                                                  fontFamily: 'NotoSansJP',
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.red,
                                                ),
                                              ),
                                            ],
                                          ),
                                          softWrap: true,
                                          overflow: TextOverflow.visible,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      // Expanded(
                                      //     child: Text(
                                      //       // controller.getTantoushaInfoWithIndex(i),
                                      //       overflow: TextOverflow.visible,
                                      //     )),
                                      const SizedBox(width: 4),
                                      const Icon(Icons.arrow_forward_ios, size: 14, color: Colors.black),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      )
                    : const SizedBox(),
              ],
            );
          }),
        ),
      ),
      bottomNavigationBar: Obx(() {
        return WorkflowBottomWidget(
          process: WorkflowProcess.newApplication,
          workflowRaiseButtonNameList: controller.state.buttonNameList,
          buttonNum: controller.state.buttonNum.value,
          isNextEnable: controller.state.isNextEnable.value,
          apply: controller.applyForSubmit,
          isSubmit: true,
        );
      }),
    );
  }
}
