import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/workflow_new_application_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class WorkflowNewApplicationPage extends GetView<WorkflowNewApplicationController> {
  const WorkflowNewApplicationPage({super.key});

  @override
  Widget build(BuildContext context) {
    final newApplicationScrollController = ScrollController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Get.find<WorkflowNewApplicationController>().initializeListener(newApplicationScrollController);
    });
    return SafeArea(
      child: Stack(
        children: [
          RefreshIndicator(
            onRefresh: () => controller.refreshData(),
            child: CustomScrollView(
              controller: controller.scrollController,
              slivers: [
                // Search Bar
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Si<PERSON><PERSON><PERSON>(
                      height: 40,
                      child: TextField(
                        onChanged: controller.onSearchChanged,
                        decoration: InputDecoration(
                          hintText: 'WF種類名で検索',
                          prefixIcon: const Icon(Icons.search),
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // List Items
                Obx(() {
                  // 添加初始加载状态的判断
                  if (controller.state.isInitialLoading.value) {
                    return const SliverFillRemaining(
                      child: Center(
                        child: CircularProgressIndicator(
                          color: Colors.white, // 设置为白色以匹配当前主题
                        ),
                      ),
                    );
                  }
                  if (controller.state.isEmpty.value) {
                    return const SliverFillRemaining(
                      child: Center(
                        child: Text('新規申請はありません', style: TextStyle(fontSize: 16, color: Colors.white)),
                      ),
                    );
                  }

                  return SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      if (index > controller.state.filteredItems.length) return const SizedBox.shrink();
                      final item = controller.state.filteredItems[index];
                      return Container(
                        constraints: const BoxConstraints(minHeight: 70),
                        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: InkWell(
                          onTap: () => controller.onItemTap(item),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Title
                                      // Title Row
                                      if (item.assetTypeName?.isNotEmpty == true) ...[
                                        Row(
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            SizedBox(
                                              width: 14,
                                              height: 14,
                                              child: SvgPicture.asset(
                                                'assets/icons/icon-assets-v2.svg',
                                                colorFilter: const ColorFilter.mode(Color(0xFF0B3E86), BlendMode.srcIn),
                                              ),
                                            ),
                                            const SizedBox(width: 5),
                                            Expanded(
                                              child: Text(
                                                item.assetTypeName ?? '',
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.bold,
                                                  height: 1.5,
                                                  color: Color(0xFF0B3E86),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                      // Subtitle
                                      Padding(
                                        padding: const EdgeInsets.only(left: 0),
                                        child: Text(
                                          item.workflowName,
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            height: 1.5,
                                            color: Color(0xFF323232),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Arrow icon
                                const Icon(Icons.chevron_right, color: Color(0xFF323232)),
                              ],
                            ),
                          ),
                        ),
                      );
                    }, childCount: controller.state.filteredItems.length),
                  );
                }),
              ],
            ),
          ),
          // 添加回到顶部按钮
          Positioned(
            right: 16,
            bottom: 16,
            child: Obx(
              () => AnimatedOpacity(
                opacity: controller.state.showScrollToTop.value ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 200),
                child: Visibility(
                  visible: controller.state.showScrollToTop.value,
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: controller.scrollToTop,
                      borderRadius: BorderRadius.circular(5.0),
                      child: Container(
                        width: 32.0,
                        height: 32.0,
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(230),
                          borderRadius: BorderRadius.circular(5.0),
                        ),
                        alignment: Alignment.center,
                        child: const Icon(Icons.arrow_upward, color: Color(0xFF757575), size: 18.0),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
