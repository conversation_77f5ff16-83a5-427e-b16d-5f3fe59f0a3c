import 'dart:convert';

import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/data/models/file_model.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/workflow_preview_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 预览组件 bindings需要绑定workflowCommonController
class WorkflowPreviewPage extends StatelessWidget {
  final Map<String, List<AssetItemListModel>> itemData;
  final List<CommentsModel> comments;
  final WorkflowActionService workflowActionService;
  final Function? onLoadComplete;

  const WorkflowPreviewPage({
    super.key,
    required this.itemData,
    required this.comments,
    required this.workflowActionService,
    this.onLoadComplete,
  });
  @override
  Widget build(BuildContext context) {
    final _isExpanded = false.obs;
    return GetBuilder<WorkflowPreviewController>(
      init: WorkflowPreviewController(
        itemData: itemData,
        comments: comments,
        workflowActionService: workflowActionService,
        onLoadComplete: onLoadComplete,
      ),
      builder: (controller) {
        return Obx(() {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: controller.toEditPage,
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (controller.processedItemData.isNotEmpty)
                            for (var itemWidget in buildMainContextWidget(controller)) itemWidget,
                          if (_isExpanded.value) ...{
                            const Text('コメント', style: TextStyle(fontWeight: FontWeight.w700)),
                            for (var comment in controller.comments) ...{Text(comment.commentContent ?? '')},
                          },
                        ],
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0),
                      child: Icon(Icons.chevron_right, color: Colors.black),
                    ),
                  ],
                ),
              ),
              Obx(
                () => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: InkWell(
                    onTap: () {
                      _isExpanded.value = !_isExpanded.value;
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppTheme.darkBlueColor),
                      ),
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const SizedBox.shrink(),
                              Text(
                                _isExpanded.value ? '表示を減らす' : 'さらに表示',
                                style: const TextStyle(color: AppTheme.darkBlueColor),
                              ),
                              Icon(
                                _isExpanded.value ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                                color: AppTheme.darkBlueColor,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        });
      },
    );
  }

  List<Widget> buildMainContextWidget(WorkflowPreviewController controller) {
    final List<Widget> widgets = [];
    for (var sectionName in controller.sectionNameArray) {
      if (sectionName != '変更項目' && sectionName != '設定値')
        widgets.add(
          Text(
            '[$sectionName]',
            style: const TextStyle(color: Color(0xFF646464), fontWeight: FontWeight.w700),
            textAlign: TextAlign.left,
          ),
        );
      final sectionItems = controller.processedItemData[sectionName];
      if (sectionItems != null) {
        for (var entry in sectionItems) {
          if (entry.itemType == 'label') {
            widgets.add(buildLabelWidget(entry));
            continue;
          }
          if (entry.itemType == 'master' && controller.masterType(entry) == true) {
            widgets.add(_buildMasterWidget(entry, controller));
            continue;
          }
          if (entry.isShowMessageTS != null && entry.isShowMessageTS == true) {
            widgets.add(Text(entry.showMessage ?? ''));
            continue;
          }
          widgets.add(buildPreviewWidget(entry, controller));
        }
      }
    }
    return widgets;
  }

  Widget buildPreviewWidget(AssetItemListModel assetTypeItem, WorkflowPreviewController controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: Get.width / 3 - 20, child: _buildPreviewTitleWidget(assetTypeItem, controller)),
          if (assetTypeItem.itemType != 'label') const Text(':', style: TextStyle(color: Color(0xFF646464))),
          const SizedBox(width: 8),
          Flexible(child: _buildPreviewValueWidget(assetTypeItem, controller)),
        ],
      ),
    );
  }

  Widget _buildPreviewTitleWidget(AssetItemListModel assetTypeItem, WorkflowPreviewController controller) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: '${assetTypeItem.itemDisplayName ?? assetTypeItem.itemName}',
            style: const TextStyle(color: Color(0xFF646464)),
          ),
          controller.isNeedCheck(assetTypeItem)
              ? WidgetSpan(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                    decoration: BoxDecoration(color: AppTheme.delIconColor, borderRadius: BorderRadius.circular(20)),
                    child: const Text('必須', style: TextStyle(color: AppTheme.whiteColor, fontSize: 9)),
                  ),
                )
              : const WidgetSpan(child: const SizedBox()),
        ],
      ),
    );
  }

  Widget _buildPreviewValueWidget(AssetItemListModel assetTypeItem, WorkflowPreviewController controller) {
    final itemType = assetTypeItem.itemType;

    switch (itemType) {
      case 'input':
      case 'email':
      case 'number':
      case 'currency':
      case 'date':
      case 'list':
        return (assetTypeItem.isShowMessage ?? false)
            ? Text('${assetTypeItem.itemDisplayName ?? assetTypeItem.itemName}を設定してください')
            : Text(
                (assetTypeItem.method != '2')
                    ? controller.valueForShow(assetTypeItem)
                    : controller.valueForShow(assetTypeItem, valueType: 'show'),
              );
      case 'calculate':
        return (assetTypeItem.isShowMessage ?? false)
            ? Text('${assetTypeItem.itemDisplayName ?? assetTypeItem.itemName}を設定してください')
            : Text(controller.valueForShow(assetTypeItem));
      case 'textarea':
        return (assetTypeItem.isShowMessage ?? false)
            ? Text('${assetTypeItem.itemDisplayName ?? assetTypeItem.itemName}を設定してください')
            : Text(assetTypeItem.defaultData ?? '');
      case 'hyperlink':
        return (assetTypeItem.isShowMessage ?? false)
            ? Text('${assetTypeItem.itemDisplayName ?? assetTypeItem.itemName}を設定してください')
            : Text(
                assetTypeItem.defaultData != null ? assetTypeItem.defaultData.toString() : '',
                overflow: TextOverflow.ellipsis,
              );
      case 'checkbox':
        if (assetTypeItem.optionObject?.checkboxMultiFlg == '1' && assetTypeItem.optionObject?.checkboxOptions != null)
          return Column(
            children: [
              for (var checkItem in assetTypeItem.optionObject!.checkboxOptions!)
                Row(
                  children: [
                    SizedBox(
                      height: 12,
                      width: 12,
                      child: Checkbox(
                        value: controller.conversionOfValue(assetTypeItem.defaultData, checkItem),
                        onChanged: null,
                        fillColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
                          if (states.contains(WidgetState.disabled)) {
                            if (controller.conversionOfValue(assetTypeItem.defaultData, checkItem)) {
                              return AppTheme.darkBlueColor.withAlpha(120); // 禁用且选中时的颜色
                            }
                            return Colors.white; // 禁用且未选中时的颜色
                          }
                          return Colors.blue; // 默认颜色
                        }),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(checkItem),
                  ],
                ),
            ],
          );
        else
          return SizedBox(
            width: 12,
            height: 12,
            child: Checkbox(
              value: assetTypeItem.defaultData == '1',
              onChanged: null,
              fillColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
                if (states.contains(WidgetState.disabled)) {
                  if (assetTypeItem.defaultData == '1') {
                    return AppTheme.darkBlueColor.withAlpha(120); // 禁用且选中时的颜色
                  }
                  return Colors.white; // 禁用且未选中时的颜色
                }
                return Colors.blue; // 默认颜色
              }),
            ),
          );
      case 'file':
        if (assetTypeItem.defaultData is List<FileModel> && (assetTypeItem.defaultData as List<FileModel>).isNotEmpty)
          for (var data in assetTypeItem.defaultData as List<FileModel>) {
            return Row(children: [Text(data.fileName ?? ''), const SizedBox(width: 6), Text(data.uploadDate ?? '')]);
          }
        return const SizedBox();
      case 'map':
        if (assetTypeItem.defaultData == '') return const SizedBox();
        return Wrap(
          children: [
            (assetTypeItem.isShowMessage ?? false)
                ? Text('${assetTypeItem.itemDisplayName ?? assetTypeItem.itemName}を設定してください')
                : Text(assetTypeItem.defaultData != null ? assetTypeItem.defaultData.toString() : ''),
            // todo
            if (assetTypeItem.optionObject?.readonly != '1') const Icon(Icons.map),
          ],
        );
      case 'image':
        return Wrap(
          children: [
            if (assetTypeItem.defaultData is List)
              for (var data in assetTypeItem.defaultData) ...{
                if (data['turl'] != null)
                  CachedNetworkImage(
                    imageUrl: data['turl'],
                    errorWidget: (context, url, error) => const SizedBox(
                      height: 120,
                      width: double.infinity,
                      child: Center(child: Icon(Icons.error)),
                    ),
                  ),
              },
          ],
        );
      case 'digitalSign':
        if (assetTypeItem.defaultData == null || assetTypeItem.defaultData == '') return const Text('未設定');
        if (assetTypeItem.defaultData is List &&
            (assetTypeItem.defaultData as List).isNotEmpty &&
            (assetTypeItem.defaultData as List).first['turl'] != null)
          return CachedNetworkImage(
            imageUrl: (assetTypeItem.defaultData as List).first['turl'],
            errorWidget: (context, url, error) => const SizedBox(
              height: 120,
              width: double.infinity,
              child: Center(child: Icon(Icons.error)),
            ),
          );
        return const SizedBox.shrink();
      case 'master':
        if (controller.masterType(assetTypeItem) == false) {
          (assetTypeItem.isShowMessage ?? false)
              ? Text('${assetTypeItem.itemDisplayName ?? assetTypeItem.itemName}を設定してください')
              : Text(assetTypeItem.defaultData['display'] ?? '');
        }
    }
    return const SizedBox();
  }

  Widget _buildMasterWidget(AssetItemListModel assetTypeItem, WorkflowPreviewController controller) {
    final itemType = assetTypeItem.itemType;
    if (itemType == 'master' && controller.masterType(assetTypeItem) == true) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(assetTypeItem.itemDisplayName ?? ''),
          if (assetTypeItem.optionObject?.masterDisplayItems != null)
            for (var masterItem in assetTypeItem.optionObject!.masterDisplayItems!) ...{
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: Get.width / 3 - 20,
                      child: RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: masterItem.itemDisplayName ?? masterItem.itemName,
                              style: const TextStyle(color: Color(0xFF646464)),
                            ),
                            controller.isNeedCheck(assetTypeItem)
                                ? WidgetSpan(
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                                      decoration: BoxDecoration(
                                        color: AppTheme.delIconColor,
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: const Text(
                                        '必須',
                                        style: TextStyle(color: AppTheme.whiteColor, fontSize: 9),
                                      ),
                                    ),
                                  )
                                : const WidgetSpan(child: const SizedBox()),
                          ],
                        ),
                      ),
                    ),
                    if (assetTypeItem.itemType != 'label') const Text(':', style: TextStyle(color: Color(0xFF646464))),
                    const SizedBox(width: 8),
                    Flexible(child: Text(controller.showMasterValue(masterItem))),
                  ],
                ),
              ),
            },
        ],
      );
    }
    return const SizedBox();
  }

  Widget buildLabelWidget(AssetItemListModel assetTypeItem) {
    final option = assetTypeItem.option;
    final defaultData = assetTypeItem.defaultData;
    if (option == null || option.isEmpty || defaultData == null) {
      return const SizedBox();
    }
    final optionObject = OptionObjModel.fromJson(jsonDecode(option));
    final TextStyle textStyle = TextStyle(
      color: optionObject.color != null ? Color(int.parse(optionObject.color!.replaceAll('#', '0xFF'))) : Colors.black,
      fontSize: optionObject.fontSize != null ? double.parse(optionObject.fontSize!.replaceAll('px', '')) : 14,
      fontWeight: optionObject.fontWeight == 'bold' ? FontWeight.bold : FontWeight.normal,
      height: optionObject.lineHeight != null ? double.parse(optionObject.lineHeight!.replaceAll('%', '')) / 100 : 1.5,
    );
    return Text(defaultData, style: textStyle, overflow: TextOverflow.visible);
  }

  Widget buildTextWidget(AssetItemListModel assetTypeItem, {isShowMessage = false}) {
    return isShowMessage
        ? Text('${assetTypeItem.itemDisplayName ?? assetTypeItem.itemName}を設定してください')
        : Text(assetTypeItem.defaultData != null ? assetTypeItem.defaultData.toString() : '');
  }
}
