import 'package:json_annotation/json_annotation.dart';

part 'comments_model.g.dart';

@JsonSerializable()
class CommentsModel {
  final String? commentDate;
  final String? commentUser;
  final String? commentContent;
  final int? commentUserId;
  @JsonKey(includeIfNull: false)
  bool? newInput;

  CommentsModel({this.commentDate, this.commentUser, this.commentContent, this.commentUserId, this.newInput});

  /// 从 JSON 构造
  factory CommentsModel.fromJson(Map<String, dynamic> json) => _$CommentsModelFromJson(json);

  /// 转为 JSON
  Map<String, dynamic> toJson() => _$CommentsModelToJson(this);
}
