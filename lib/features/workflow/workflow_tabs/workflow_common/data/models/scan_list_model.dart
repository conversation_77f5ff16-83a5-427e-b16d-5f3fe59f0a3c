import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_asset_item.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_ai_ocr_type_enum.dart';
import 'package:get/get.dart';
import 'package:json_annotation/json_annotation.dart';

part 'scan_list_model.g.dart';

@JsonSerializable(explicitToJson: true)
class ScanListModel {
  final List<RegisteredAssetListModel>? registeredAssetListModel;
  final ScanType? scanType;
  final String? assetTypeId;

  ScanListModel({this.registeredAssetListModel, this.scanType, this.assetTypeId});

  factory ScanListModel.fromJson(Map<String, dynamic> json) => _$ScanListModelFromJson(json);
  Map<String, dynamic> toJson() => _$ScanListModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class HomeImageModel {
  final String? uploadDate;
  final String? uploadUserName;
  final String? fileName;
  final String? lastModifiedDate;
  final String? size;
  final bool? isHomeImage;
  final String? url;
  String? turl;

  HomeImageModel({
    this.uploadDate,
    this.uploadUserName,
    this.fileName,
    this.lastModifiedDate,
    this.size,
    this.isHomeImage,
    this.url,
    this.turl,
  });

  factory HomeImageModel.fromJson(Map<String, dynamic> json) => _$HomeImageModelFromJson(json);
  Map<String, dynamic> toJson() => _$HomeImageModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class RegisteredAssetListModel {
  final String? layoutNo;
  final String? modifiedById;
  @JsonKey(fromJson: _rxIntFromJson, toJson: _rxIntToJson)
  RxInt assetScanedCount;
  int? assetTotalCount;
  final String? rfid;
  final String? tenantId;
  final String? copyAppurtenancesInformationTypeIds;
  final int? assetId;
  final String? state;
  final String? createdById;
  final String? barcode;
  final String? jurisdiction;
  final List<dynamic>? assetReservationStatusList;
  @JsonKey(fromJson: _rxIntFromJson, toJson: _rxIntToJson)
  RxInt assetAmount;
  int? tempAssetCount;
  final List<dynamic>? insertAssetRelationColumnData;
  final int? workflowId;
  final int? assetTypeId;
  final List<dynamic>? relationAssetDataList;
  final String? modifiedDate;
  final String? groupIds;
  final String? assetLocation;
  final bool? relationNotUpdateFlag;
  final List<dynamic>? relationAssetIdList;
  final bool? relationFlg;
  final String? message;
  final String? createdDate;
  final List? dependentRelationAssetIdList;
  final String? externalCode;
  final int? willChangedAmount;
  final String? assetName;
  final List<SharedAssetItem>? assetItemList;
  final String? interactionOperation;
  final String? assetTypeName;
  dynamic assetText;
  HomeImageModel? homeImage;
  int? assetAmountBeforeScan;
  int? oldAssetScannedCount;
  bool? isApprovalSys;
  int? oldAssetAmount;
  int? savedAssetAmount;
  final String? mpFlag;
  @JsonKey(fromJson: _rxStringFromJson, toJson: _rxStringToJson)
  RxString? scanState;
  final String? processStepName;
  String? step;
  String? status;
  bool? isSelect;

  RegisteredAssetListModel({
    this.dependentRelationAssetIdList,
    this.externalCode,
    this.willChangedAmount,
    this.assetName,
    this.layoutNo,
    this.modifiedById,
    required this.assetScanedCount,
    this.assetTotalCount,
    this.rfid,
    this.tenantId,
    this.copyAppurtenancesInformationTypeIds,
    this.assetId,
    this.state,
    this.createdById,
    this.barcode,
    this.jurisdiction,
    this.assetReservationStatusList,
    required this.assetAmount,
    this.insertAssetRelationColumnData,
    this.workflowId,
    this.assetTypeId,
    this.relationAssetDataList,
    this.modifiedDate,
    this.groupIds,
    this.assetLocation,
    this.relationNotUpdateFlag,
    this.relationAssetIdList,
    this.relationFlg,
    this.message,
    this.createdDate,
    this.assetItemList,
    this.interactionOperation,
    this.assetTypeName,
    this.homeImage,
    this.assetText,
    this.savedAssetAmount,
    this.mpFlag,
    this.scanState,
    this.processStepName,
    this.step,
    this.status,
    this.isSelect,
    this.oldAssetScannedCount,
  });

  factory RegisteredAssetListModel.fromJson(Map<String, dynamic> json) => _$RegisteredAssetListModelFromJson(json);
  Map<String, dynamic> toJson() => _$RegisteredAssetListModelToJson(this);
}

RxInt _rxIntFromJson(dynamic value) {
  if (value == null) {
    value = 0;
  }
  if (value is num) {
    return RxInt(value.toInt());
  }
  if (value is String) {
    final parsed = int.tryParse(value);
    if (parsed == null) {
      throw BusinessException('Invalid assetScanedCount value: $value');
    }
    return RxInt(parsed);
  }
  throw BusinessException('Invalid assetScanedCount type: ${value.runtimeType}');
}

int _rxIntToJson(RxInt value) => value.value;

RxString _rxStringFromJson(dynamic value) {
  if (value == null) {
    value = '';
  }
  if (value is String) {
    return RxString(value);
  }
  throw BusinessException('Invalid scanState type: ${value.runtimeType}');
}

String _rxStringToJson(RxString? value) => value?.value ?? '';

@JsonSerializable(explicitToJson: true)
class ScanListItemModel {
  int? itemId;
  String? itemDisplayName;
  String? itemType;
  String? value;
  ScanListItemModel({this.value, this.itemDisplayName, this.itemType, this.itemId});
  factory ScanListItemModel.fromJson(Map<String, dynamic> json) => _$ScanListItemModelFromJson(json);
  Map<String, dynamic> toJson() => _$ScanListItemModelToJson(this);
}
