import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset.dart';

class UpdateByBarcodeResponseModel {
  bool isSuccessScan; // 成功にスキャンできましたかどうか（DBを更新しましたかどうか）
  int assetAmountBeforeScan; // 対象資産をスキャン前の入力数
  List<Asset> arrayAsset; // 対象資産
  UpdateByBarcodeResponseModel({
    required this.assetAmountBeforeScan,
    required this.arrayAsset,
    required this.isSuccessScan,
  });

  factory UpdateByBarcodeResponseModel.fromJson(Map<String, dynamic> json) {
    return UpdateByBarcodeResponseModel(
      isSuccessScan: json['isSuccessScan'] as bool,
      assetAmountBeforeScan: json['assetAmountBeforeScan'] as int,
      arrayAsset: [], // 不处理 arrayAsset
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isSuccessScan': isSuccessScan,
      'assetAmountBeforeScan': assetAmountBeforeScan,
      // 不包含 arrayAsset
    };
  }
}
