import 'package:json_annotation/json_annotation.dart';

part 'workflow_asset_list_data_model.g.dart';

/// 資産リストデータクラス
@JsonSerializable(explicitToJson: true)
class WorkflowAssetListDataModel {
  /// スキャン待ち
  static const String SCAN_STATE_NOT_SCAN = '0';

  /// スキャン完了
  static const String SCAN_STATE_SCANNED = '1';

  /// 資産リストID
  final int? assetListId;

  /// 資産ID
  final int? assetId;

  /// 資産種類ID
  final String? tenantId;

  /// プロセスID
  final String? processId;

  /// プロセス状態
  final String? processState;

  /// プロセス状態テキスト
  final String? processStateText;

  /// 资产テキスト
  final String? assetText;

  /// 资产個数
  final int? assetCount;

  /// 作成者ID
  final String? createdById;

  /// 作成日
  final String? createdDate;

  /// 更新者ID
  final String? modifiedById;

  /// 更新日
  final String? modifiedDate;

  /// スキャン状態(0:スキャン待ち、1:スキャン完了)
  String? scanState;

  /// スキャン順番
  final int? scanSort;

  /// スキャンタスクでスキャン対象になるかのフラグ
  final bool? scanTarget;

  /// バーコード
  final String? barcode;

  /// ステップ名（モバイル用
  final String? processStepName;

  /// 資産区分（レンタルケース）
  final String? mpFlag;

  /// Main資産インフォ
  final String? parentAssets;

  /// 増減数量
  final int? assetAmount;

  /// 一時保存数量
  final int savedAssetAmount;

  /// 資産リストASSET_TEXT
  final String? oldAssetText;

  /// 展示されている資産リスト
  final List<Map<String, dynamic>>? assetItemList;

  /// 場所
  final String? location;

  int? assetScannedCount;

  WorkflowAssetListDataModel({
    this.assetListId,
    this.assetId,
    this.tenantId,
    this.processId,
    this.processState,
    this.processStateText,
    this.assetText,
    this.assetCount,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.scanState,
    this.scanSort,
    this.scanTarget,
    this.barcode,
    this.processStepName,
    this.mpFlag,
    this.parentAssets,
    this.assetAmount,
    this.savedAssetAmount = 0,
    this.oldAssetText,
    this.assetItemList,
    this.location,
    this.assetScannedCount,
  });

  /// 从 JSON 创建实例
  factory WorkflowAssetListDataModel.fromJson(Map<String, dynamic> json) => _$WorkflowAssetListDataModelFromJson(json);

  /// 转换为 JSON
  Map<String, dynamic> toJson() => _$WorkflowAssetListDataModelToJson(this);
}
