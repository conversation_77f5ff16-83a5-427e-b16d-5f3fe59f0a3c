// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workflow_asset_list_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkflowAssetListDataModel _$WorkflowAssetListDataModelFromJson(
  Map<String, dynamic> json,
) => WorkflowAssetListDataModel(
  assetListId: (json['assetListId'] as num?)?.toInt(),
  assetId: (json['assetId'] as num?)?.toInt(),
  tenantId: json['tenantId'] as String?,
  processId: json['processId'] as String?,
  processState: json['processState'] as String?,
  processStateText: json['processStateText'] as String?,
  assetText: json['assetText'] as String?,
  assetCount: (json['assetCount'] as num?)?.toInt(),
  createdById: json['createdById'] as String?,
  createdDate: json['createdDate'] as String?,
  modifiedById: json['modifiedById'] as String?,
  modifiedDate: json['modifiedDate'] as String?,
  scanState: json['scanState'] as String?,
  scanSort: (json['scanSort'] as num?)?.toInt(),
  scanTarget: json['scanTarget'] as bool?,
  barcode: json['barcode'] as String?,
  processStepName: json['processStepName'] as String?,
  mpFlag: json['mpFlag'] as String?,
  parentAssets: json['parentAssets'] as String?,
  assetAmount: (json['assetAmount'] as num?)?.toInt(),
  savedAssetAmount: (json['savedAssetAmount'] as num?)?.toInt() ?? 0,
  oldAssetText: json['oldAssetText'] as String?,
  assetItemList:
      (json['assetItemList'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
  location: json['location'] as String?,
  assetScannedCount: (json['assetScannedCount'] as num?)?.toInt(),
);

Map<String, dynamic> _$WorkflowAssetListDataModelToJson(
  WorkflowAssetListDataModel instance,
) => <String, dynamic>{
  'assetListId': instance.assetListId,
  'assetId': instance.assetId,
  'tenantId': instance.tenantId,
  'processId': instance.processId,
  'processState': instance.processState,
  'processStateText': instance.processStateText,
  'assetText': instance.assetText,
  'assetCount': instance.assetCount,
  'createdById': instance.createdById,
  'createdDate': instance.createdDate,
  'modifiedById': instance.modifiedById,
  'modifiedDate': instance.modifiedDate,
  'scanState': instance.scanState,
  'scanSort': instance.scanSort,
  'scanTarget': instance.scanTarget,
  'barcode': instance.barcode,
  'processStepName': instance.processStepName,
  'mpFlag': instance.mpFlag,
  'parentAssets': instance.parentAssets,
  'assetAmount': instance.assetAmount,
  'savedAssetAmount': instance.savedAssetAmount,
  'oldAssetText': instance.oldAssetText,
  'assetItemList': instance.assetItemList,
  'location': instance.location,
  'assetScannedCount': instance.assetScannedCount,
};
