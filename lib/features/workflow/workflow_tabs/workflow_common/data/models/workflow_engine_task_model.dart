import 'package:json_annotation/json_annotation.dart';

part 'workflow_engine_task_model.g.dart';

@JsonSerializable()
class WorkflowEngineTaskModel {
  String? processInstanceId; // プロセスinstance
  final String? processDefinitionId; // プロセスdefinition
  final String? wfName; // WF名
  final int? workflowId; // ワークフローID
  final String? workflowName; // ワークフロー名
  String? id; // タスクID
  final String? name; // タスク名
  final String? state; // ステータス
  final String? createdDate; // プロセス提出時間
  final String? endDate; // プロセス終了時間
  final String? assignedBy; // 担当者
  final String? approvedBy; // 依頼者
  final String? approvedByUserId;
  final String? wfState; // WFステータス
  final String? assetListTitle; // 資産リストタイトル
  final String? deadlineDate; // 期限日付
  final String? wfExpireTime; // WF期限
  final String? workflowType; // 種類
  final String? workflowTypeCode; // 種類code
  final String? firstWorkflowAssetTypeName; // 資産種類
  final String? taskDefKey; // タスク名
  final int? assetTypeIdWithFirstWf; // 第一類WF関連の資産種類
  final bool? isMyselfInputTask;
  WorkflowEngineTaskModel({
    this.processInstanceId,
    this.processDefinitionId,
    this.wfName,
    this.workflowId,
    this.workflowName,
    this.id,
    this.name,
    this.state,
    this.createdDate,
    this.endDate,
    this.assignedBy,
    this.approvedBy,
    this.approvedByUserId,
    this.wfState,
    this.assetListTitle,
    this.deadlineDate,
    this.wfExpireTime,
    this.workflowType,
    this.workflowTypeCode,
    this.firstWorkflowAssetTypeName,
    this.taskDefKey,
    this.assetTypeIdWithFirstWf,
    this.isMyselfInputTask,
  });

  factory WorkflowEngineTaskModel.fromJson(Map<String, dynamic> json) => _$WorkflowEngineTaskModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkflowEngineTaskModelToJson(this);
}
