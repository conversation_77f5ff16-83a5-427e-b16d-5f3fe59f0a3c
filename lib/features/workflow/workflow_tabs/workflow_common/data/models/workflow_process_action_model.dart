import 'package:json_annotation/json_annotation.dart';

part 'workflow_process_action_model.g.dart';

// Wf否決、差し戻し情報、ボタン制御情報クラス (Wf rejection, remand information, button control information class)
@JsonSerializable(explicitToJson: true)
class WorkflowProcessActionModel {
  // テナントID
  String? tenantId;

  // ワークフローID
  int? workflowId;

  // タスクキー
  String? taskDefKey;

  // 否決表示可能
  String? rejectionStatus;

  // 差し戻し表示可能
  String? sendBackStatus;

  // 差し戻し先
  String? sendBackDestination;

  // 登録者
  String? createdById;

  // 登録日
  String? createdDate;

  // 更新者
  String? modifiedById;

  // 更新日
  String? modifiedDate;

  // 権限認証フラグ
  bool? isPermission;

  // 一時保存状態フラグ
  bool? isSuspended;

  // 取り消し
  bool? cancelStatus;

  // 提出
  bool? isMyselfInputTask;

  // 受取可能
  bool? possibleOfClaim;

  // スキャンタスクでコミットできるか
  bool? canBeCommitScanTask;

  // タスク名
  String? taskName;

  // ワークフロー名
  String? workflowName;

  // 複数人スキャンのスキャンタスクの判定フラグ
  bool? isMultiScanTask;

  // 「全資産をスキャン」フラグ
  bool? allMustScan;

  // スキャンタスクロックフラウ
  bool? scanTaskIsLocked;

  // スキャンタスクで「一時保存」ボタン表示かの判断フラグ(モバイル用
  bool? saveTemporaryButtonWithScanTask;

  // エンジン管理ID(ワークフローエンジンと連携用)
  String? engineId;

  // エンジン管理ID(ワークフローエンジンと連携用)
  String? newEngineId;

  WorkflowProcessActionModel({
    this.tenantId,
    this.workflowId,
    this.taskDefKey,
    this.rejectionStatus,
    this.sendBackStatus,
    this.sendBackDestination,
    this.createdById,
    this.createdDate,
    this.modifiedById,
    this.modifiedDate,
    this.isPermission,
    this.isSuspended,
    this.cancelStatus,
    this.isMyselfInputTask,
    this.possibleOfClaim,
    this.canBeCommitScanTask,
    this.taskName,
    this.workflowName,
    this.isMultiScanTask,
    this.allMustScan,
    this.scanTaskIsLocked,
    this.saveTemporaryButtonWithScanTask,
    this.engineId,
    this.newEngineId,
  });

  /// 从 JSON 创建实例
  factory WorkflowProcessActionModel.fromJson(Map<String, dynamic> json) => _$WorkflowProcessActionModelFromJson(json);

  /// 转换为 JSON
  Map<String, dynamic> toJson() => _$WorkflowProcessActionModelToJson(this);
}
