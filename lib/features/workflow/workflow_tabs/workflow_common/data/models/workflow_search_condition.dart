class WorkflowSearchCondition {
  String name;
  bool nameIsNull;
  String method;
  bool methodIsNull;
  dynamic value;
  bool valueIsNull;
  String itemType;
  String logicOperate;
  String valueForShow;
  bool isShow;

  WorkflowSearchCondition({
    this.name = '',
    this.nameIsNull = false,
    this.method = '',
    this.methodIsNull = false,
    this.value = '',
    this.valueIsNull = false,
    this.itemType = '',
    this.logicOperate = '',
    this.valueForShow = '',
    this.isShow = true,
  });

  Map<String, dynamic> toJson() => {
    'name': name,
    'nameIsNull': nameIsNull,
    'method': method,
    'methodIsNull': methodIsNull,
    'value': value,
    'valueIsNull': valueIsNull,
    'itemType': itemType,
    'logicOperate': logicOperate,
    'valueForShow': valueForShow,
    'isShow': isShow,
  };
}
