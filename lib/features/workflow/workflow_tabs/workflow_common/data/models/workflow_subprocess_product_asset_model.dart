import 'package:json_annotation/json_annotation.dart';

part 'workflow_subprocess_product_asset_model.g.dart';

/// 完成品の資産データ
@JsonSerializable(explicitToJson: true)
class WorkflowSubprocessProductAssetModel {
  /// タスクID
  final String? taskId;

  /// 資産ID
  final String? assetId;

  /// Step名
  final String? stepName;

  /// 資産テキスト
  final String? assetText;

  /// 識別コード
  final String? barcode;

  /// 担当者
  final String? assignedBy;

  /// 更新日
  final String? updateTime;

  WorkflowSubprocessProductAssetModel({
    this.taskId,
    this.assetId,
    this.stepName,
    this.assetText,
    this.barcode,
    this.assignedBy,
    this.updateTime,
  });

  /// 从 JSON 创建实例
  factory WorkflowSubprocessProductAssetModel.fromJson(Map<String, dynamic> json) =>
      _$WorkflowSubprocessProductAssetModelFromJson(json);

  /// 转换为 JSON
  Map<String, dynamic> toJson() => _$WorkflowSubprocessProductAssetModelToJson(this);
}
