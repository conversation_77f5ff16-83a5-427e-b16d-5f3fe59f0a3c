import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_asset_list_data_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_button_name_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_process_action_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_subprocess_product_asset_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'workflow_task_form_response_model.g.dart';

@JsonSerializable(explicitToJson: true)
class WorkflowTaskFormResponseModel {
  /// タスクフォームデータ
  final List<WorkflowEngineTaskFormItemModel>? form;

  /// ワークフローのボタン制御データ
  final WorkflowProcessActionModel? actions;

  /// ワークフローボタン表示名設定情報
  final WorkflowButtonNameModel? workflowButtonName;

  /// 指定件数の先頭資産リストデータ
  final List<WorkflowAssetListDataModel>? assetListDatas;

  /// 未スキャン資産の件数
  final int? noScanningAssetCount;

  /// すべての資産リストデータ件数
  final int? allAssetCount;

  /// タスク種類
  final String? taskType;

  /// スキャンタスクになる場合、関連の第３類WFのエンジンID
  final String? thirdWfProcessDefinitionId;

  /// 資産リストID
  final int? assetListId;

  /// 資産リストタイトル
  final String? assetListTitle;

  ///  モバイル用の判断フラグ（第１類WF＆資産種類と関連されるか）
  final bool? isFirstWfWithAssetList;

  /// 数量加減（舞浜リゾート）ケースの場合、必要な情報
  final WorkflowEngineAmountProcessModel? workflowEngineAmountProcess;

  /// レンタルのWFであるかどうか
  final bool? isRentalWorkflow;

  /// サブプロセスあるかどうか
  final bool? nonSubProcess;

  /// WF現在の状態
  final String? workflowState;

  /// （複数人スキャン用）未スキャンの資産リストデータ
  final List<WorkflowAssetListDataModel>? noScanningAssetList;

  /// （複数人スキャン用）資産種類名
  final String? assetTypeName;

  /// 帳票のワークフローフォームのリスト
  final List<WorkflowFormOfReport>? reportInfoList;

  /// 複数人スキャン用）資産種類ID
  final int? assetTypeIdWithMultiScan;

  /// フラグ「資産リストの入力」（WF資産リストを作成／編集できるかどうか）
  final String? inputAssetListFlag;

  /// ワークフローにスキャンタスクが存在するかどうか
  final bool? hasScanTask;

  /// 個数資産種類WFであるかどうか
  final bool? updateAmountShow;

  /// 当該タスクの「ワークフロー動的タスク承認者情報」
  final List<WorkflowAssignDynamicTaskModel>? wfAssignDynamicTasks;

  /// 動的承認者選択の指定タスクであるかどうかのフラグ
  final bool? assignDynamicFlag;

  /// サブプロセス（第３類WFではない）
  final bool? hasSubProcess;

  /// （メタルワン用）完成品の資産リストデータ
  final List<WorkflowSubprocessProductAssetModel>? subProcessProductAssetList;

  /// （サブプロセス用）完成品資産タイプ名
  final String? productAssetTypeName;

  /// （サブプロセス用）完成品資産タイプID
  final int? productAssetTypeId;

  /// 実行中のタスクはサブプロセスに存在するかどうか（第３類WFではない）
  final bool? currentTaskInSubProcess;

  /// サブプロセスの区分が「グループ」である、かつ、当該ユーザーのサブプロセスが完了である場合　→「True」
  final bool? currentUserSubProcessTaskIsCompleted;

  /// 当該プロセスと紐づくサブフォームのID
  final int? subformId;

  /// 当該プロセスと紐づくサブフォームのバージョン
  final int? subformVersion;

  /// 終了のサブプロセスがあるかどうか
  final bool? hasPassedSubProcess;

  /// 担当者区分（user：ユーザー；group：グループ）
  final String? assignDynamicType;

  /// サブプロセス内のWF用に帳票のワークフローフォームのリスト
  final List<WorkflowFormOfReport>? subProcessReportInfoList;

  /// 全ての資産のスキャン数量合計
  final int? totalSavedAssetAmountCount;

  /// 全ての資産の入力数（最大スキャン数量）合計
  final int? totalAssetAmountCount;

  /// 保存時ロジック
  final String? workflowScript;

  /// 自動読込用のSearchId
  final int? autoFetchSearchId;

  /// ワークフローのカスタマイズロジック
  final String? workflowLogicScript;

  /// 共通関数設定 - フォーム画面スクリプト
  final String? commonJS;
  WorkflowTaskFormResponseModel({
    this.form,
    this.actions,
    this.workflowButtonName,
    this.assetListDatas,
    this.noScanningAssetCount,
    this.allAssetCount,
    this.taskType,
    this.assetTypeName,
    this.assetListId,
    this.inputAssetListFlag,
    this.workflowScript,
    this.autoFetchSearchId,
    this.assignDynamicType,
    this.workflowEngineAmountProcess,
    this.workflowLogicScript,
    this.commonJS,
    this.assetListTitle,
    this.assetTypeIdWithMultiScan,
    this.assignDynamicFlag,
    this.currentTaskInSubProcess,
    this.currentUserSubProcessTaskIsCompleted,
    this.hasPassedSubProcess,
    this.hasScanTask,
    this.hasSubProcess,
    this.isFirstWfWithAssetList,
    this.isRentalWorkflow,
    this.nonSubProcess,
    this.noScanningAssetList,
    this.productAssetTypeId,
    this.productAssetTypeName,
    this.updateAmountShow,
    this.reportInfoList,
    this.subformId,
    this.subformVersion,
    this.subProcessProductAssetList,
    this.subProcessReportInfoList,
    this.thirdWfProcessDefinitionId,
    this.totalAssetAmountCount,
    this.totalSavedAssetAmountCount,
    this.wfAssignDynamicTasks,
    this.workflowState,
  });

  /// 从 JSON 创建实例
  factory WorkflowTaskFormResponseModel.fromJson(Map<String, dynamic> json) =>
      _$WorkflowTaskFormResponseModelFromJson(json);

  /// 转换为 JSON
  Map<String, dynamic> toJson() => _$WorkflowTaskFormResponseModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class WorkflowEngineTaskFormItemModel {
  dynamic formField;
  AssetItemListModel? workflowForm;
  dynamic variable;

  WorkflowEngineTaskFormItemModel({this.formField, this.workflowForm, this.variable});

  /// 从 JSON 创建实例
  factory WorkflowEngineTaskFormItemModel.fromJson(Map<String, dynamic> json) =>
      _$WorkflowEngineTaskFormItemModelFromJson(json);

  /// 转换为 JSON
  Map<String, dynamic> toJson() => _$WorkflowEngineTaskFormItemModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class WorkflowEngineAmountProcessModel {
  /// 個数管理の有無(0 or 1)
  final String? isUpdateAmount;

  /// 更新項目ID
  final String? amountActionTaskUpdateItemId;

  /// 更新項目名
  final String? amountActionTaskUpdateItemName;

  /// 資産リストJSON
  final String? amountActionAssetList;

  /// 該当プロセスの処理タスクが実装したかの判断フラグ
  final bool? amountActionTaskExecuted;

  WorkflowEngineAmountProcessModel({
    this.isUpdateAmount,
    this.amountActionTaskUpdateItemId,
    this.amountActionTaskUpdateItemName,
    this.amountActionAssetList,
    this.amountActionTaskExecuted,
  });

  /// 从 JSON 创建实例
  factory WorkflowEngineAmountProcessModel.fromJson(Map<String, dynamic> json) =>
      _$WorkflowEngineAmountProcessModelFromJson(json);

  /// 转换为 JSON
  Map<String, dynamic> toJson() => _$WorkflowEngineAmountProcessModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class WorkflowFormOfReport {
  /// 項目名称
  final String itemName;

  /// 項目の入力値
  final dynamic variable;
  WorkflowFormOfReport({required this.itemName, required this.variable});

  /// 从 JSON 创建实例
  factory WorkflowFormOfReport.fromJson(Map<String, dynamic> json) => _$WorkflowFormOfReportFromJson(json);

  /// 转换为 JSON
  Map<String, dynamic> toJson() => _$WorkflowFormOfReportToJson(this);
}
