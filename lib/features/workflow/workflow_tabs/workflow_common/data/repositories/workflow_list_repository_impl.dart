import 'package:asset_force_mobile_v2/core/network/global_variable.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/update_by_barcode_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_asset_column_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_scan_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_search_task_condition.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/core/exceptions/repository_error_handler.dart';

class WorkflowListRepositoryImpl with RepositoryErrorHandler implements WorkflowListRepository {
  final DioUtil dioUtil;

  WorkflowListRepositoryImpl({required this.dioUtil});

  @override
  Future<List<WorkflowEngineTaskModel>> getWorkflowList(params) async {
    return executeRepositoryTask<List<WorkflowEngineTaskModel>>(() async {
      var requestParams = params;
      if (params is WorkflowEngineSearchTaskCondition) {
        requestParams = params.toMap();
      }
      final response = await dioUtil.post(
        GlobalVariable.applicationWorkflowListUrl,
        data: requestParams,
        useFormUrlEncoded: true,
      );
      final data = response.data as Map<String, dynamic>?;
      if (data == null) {
        LogUtil.w('getWorkflowList response data is null');
        return [];
      }
      final tasksJson = data['tasks'] as List<dynamic>? ?? [];
      return tasksJson.map((json) => WorkflowEngineTaskModel.fromJson(json)).toList();
    }, 'Error fetching application workflow list.');
  }

  @override
  Future<List<WorkflowAssignDynamicTaskModel>> getWorkflowAssignDynamicTaskListByTaskId(
    String processDefinitionId, {
    String? taskDefKey = 'InputTask-0001',
  }) async {
    return executeRepositoryTask<List<WorkflowAssignDynamicTaskModel>>(() async {
      final result = await dioUtil.get(
        GlobalVariable.workflowAssignDynamicTaskListByTaskId,
        queryParams: {'processDefinitionId': processDefinitionId, 'taskDefKey': taskDefKey},
      );
      final data = result.data as Map<String, dynamic>?;
      if (data == null) {
        LogUtil.w('getWorkflowAssignDynamicTaskListByTaskId response data is null');
        return [];
      }
      final taskList = data['taskList'] as List<dynamic>? ?? [];
      final assignTask = taskList
          .map((e) => WorkflowAssignDynamicTaskModel.fromJson(e as Map<String, dynamic>))
          .toList();
      return assignTask;
    }, 'Error fetching workflow assign by task id.');
  }

  @override
  Future<String> saveTemporaryWorkFlow(Map<String, dynamic> formData) async {
    return executeRepositoryTask<String>(() async {
      final result = await dioUtil.post(GlobalVariable.workFlowSaveTemporary, data: formData, useFormUrlEncoded: true);
      final data = result.data as Map<String, dynamic>?;
      if (data == null) {
        LogUtil.w('saveTemporaryWorkFlow response data is null');
        return '';
      }
      final processInstanceId = data['processInstanceId'] as String? ?? '';
      return processInstanceId;
    }, 'Error save workflow temporary.');
  }

  @override
  Future<WorkflowAssignScanModel> getWorkflowTaskForm({
    required String processInstanceId,
    required String taskId,
    int? scanState,
    String? keyword,
    int skip = 0,
    int rows = 20,
    bool needLoading = true,
    bool delay = true,
  }) async {
    return executeRepositoryTask<WorkflowAssignScanModel>(() async {
      final queryParams = {'processInstanceId': processInstanceId, 'taskId': taskId, 'skip': skip, 'rows': rows};
      if (scanState != null) queryParams['scanState'] = scanState;
      if (keyword != null) queryParams['keyword'] = keyword;

      final result = await dioUtil.get(
        GlobalVariable.getAssetsByKeywordInWfAssetListForAssignScan,
        queryParams: queryParams,
      );
      final data = result.data as Map<String, dynamic>?;
      if (data == null) {
        return WorkflowAssignScanModel();
      }
      final workflowScanData = WorkflowAssignScanModel.fromJson(data);
      return workflowScanData;
    }, 'Error fetching workflow assign by task id.');
  }

  @override
  Future<String> startWorkflow(Map<String, dynamic> formData) async {
    return executeRepositoryTask<String>(() async {
      final result = await dioUtil.post(GlobalVariable.workflowStart, data: formData, useFormUrlEncoded: true);
      final data = result.data as Map<String, dynamic>?;
      if (data == null) {
        LogUtil.w('startWorkflow response data is null');
        return '';
      }
      final processInstanceId = data['processInstanceId'] as String? ?? '';
      return processInstanceId;
    }, 'Error start workflow.');
  }

  @override
  Future<String> startWithThirdFlow(Map<String, dynamic> formData) async {
    return executeRepositoryTask<String>(() async {
      final result = await dioUtil.post(GlobalVariable.startThirdWorkflow, data: formData, useFormUrlEncoded: true);
      final data = result.data as Map<String, dynamic>?;
      if (data == null) {
        LogUtil.w('startWithThirdFlow response data is null');
        return '';
      }
      final processInstanceId = data['processInstanceId'] as String? ?? '';
      return processInstanceId;
    }, 'Error start Third workflow.');
  }

  @override
  Future<String> startTransitFlow(Map<String, dynamic> formData) async {
    return executeRepositoryTask<String>(() async {
      final result = await dioUtil.post(GlobalVariable.startThirdWorkflow, data: formData, useFormUrlEncoded: true);
      final data = result.data as Map<String, dynamic>?;
      if (data == null) {
        LogUtil.w('startTransitFlow response data is null');
        return '';
      }
      final processInstanceId = data['processInstanceId'] as String? ?? '';
      return processInstanceId;
    }, 'Error start Transit workflow.');
  }

  @override
  Future<String> workflowStartSaveTemporary(Map<String, dynamic> formData) async {
    return executeRepositoryTask<String>(() async {
      final result = await dioUtil.post(
        GlobalVariable.workflowStartSaveTemporary,
        data: formData,
        useFormUrlEncoded: true,
      );
      final data = result.data as Map<String, dynamic>?;
      if (data == null) {
        LogUtil.w('workflowStartSaveTemporary response data is null');
        return '';
      }
      final processInstanceId = data['processInstanceId'] as String? ?? '';
      return processInstanceId;
    }, 'Error save workflow temporary on start.');
  }

  /// サブフォーム更新する
  @override
  Future<UpdateByBarcodeResponseModel> mobileUpdateByBarCode(Map<String, dynamic> formData) async {
    return executeRepositoryTask<UpdateByBarcodeResponseModel>(() async {
      final result = await dioUtil.post(
        GlobalVariable.workflowMobUpdateByBarCode,
        data: formData,
        useFormUrlEncoded: true,
      );
      final data = result.data;
      if (data == null) {
        LogUtil.w('mobileUpdateByBarCode response data is null');
        return UpdateByBarcodeResponseModel(assetAmountBeforeScan: 0, arrayAsset: [], isSuccessScan: false);
      }
      return UpdateByBarcodeResponseModel.fromJson(data);
    }, 'Error update by barCode for workflow new application.');
  }

  @override
  Future<Map> getMasterDefaultValue(int masterTypeId) async {
    return executeRepositoryTask<Map>(() async {
      final result = await dioUtil.get(
        GlobalVariable.actionGetMasterInfoById,
        queryParams: {'masterTypeId': masterTypeId},
      );
      final data = result.data;
      if (data == null) {
        LogUtil.w('Master Default response data is null');
        return {};
      }
      return data;
    }, 'Error get Master Default');
  }

  @override
  Future<int> httpRestoreAssetScanState(Map<String, dynamic> formData) async {
    return executeRepositoryTask<int>(() async {
      final result = await dioUtil.post(GlobalVariable.restoreAssetScanState, data: formData, useFormUrlEncoded: true);
      return result.statusCode ?? 0;
    }, 'Error save workflow temporary on start.');
  }

  @override
  Future<Map<String, dynamic>> httpGetWorkflowTaskForm(String processInstanceId, String taskId, String state) async {
    return executeRepositoryTask<Map<String, dynamic>>(() async {
      final result = await dioUtil.get(
        GlobalVariable.workflowTaskForm,
        queryParams: {'processInstanceId': processInstanceId, 'taskId': taskId, 'state': state},
      );
      final data = result.data;
      if (data == null) {
        LogUtil.w('get workflow task form data is null');
        return {};
      }
      return data;
    }, 'Error get workflow task form');
  }

  @override
  Future<dynamic> httpWorkFlowCancel(String processInstanceId) async {
    return executeRepositoryTask<dynamic>(() async {
      final result = await dioUtil.delete(
        GlobalVariable.workflowCancel,
        queryParameters: {'processInstanceId': processInstanceId},
      );
      final data = result.data;
      if (data == null) {
        LogUtil.w('get workflow cancel form data is null');
        return {};
      }
      return data;
    }, 'Error to cancel workflow');
  }

  @override
  Future<dynamic> httpWorkflowTransit(Map<String, dynamic> formData) async {
    return executeRepositoryTask<dynamic>(() async {
      final result = await dioUtil.post(GlobalVariable.transitFlowStart, data: formData, useFormUrlEncoded: true);
      return result.statusCode ?? 0;
    }, 'Error workflow transit on start.');
  }

  @override
  Future<dynamic> httpWorkflowsSendBack(Map<String, dynamic> formData) async {
    return executeRepositoryTask<dynamic>(() async {
      final result = await dioUtil.post(GlobalVariable.workflowsSendBack, data: formData, useFormUrlEncoded: true);
      return result.statusCode ?? 0;
    }, 'Error workflow on sendBack.');
  }

  @override
  Future<dynamic> httpWorkflowsSaveTemporaryFormData(Map<String, dynamic> formData) async {
    return executeRepositoryTask<dynamic>(() async {
      final result = await dioUtil.post(
        GlobalVariable.workflowsSaveTemporaryFromApprover,
        data: formData,
        useFormUrlEncoded: true,
      );
      return result.statusCode ?? 0;
    }, 'Error workflow on save form data wf.');
  }

  @override
  Future<dynamic> httpWorkflowsSaveTemporaryFromScanTask(Map<String, dynamic> formData) async {
    return executeRepositoryTask<dynamic>(() async {
      final result = await dioUtil.post(
        GlobalVariable.workflowsSaveTemporaryFromScanTask,
        data: formData,
        useFormUrlEncoded: true,
      );
      return result.statusCode ?? 0;
    }, 'Error workflow on save form temporary from scanTask.');
  }

  @override
  Future<dynamic> httpRestoreAssetScanStateForAssignScan(Map<String, dynamic> formData) async {
    return executeRepositoryTask<dynamic>(() async {
      final result = await dioUtil.post(
        GlobalVariable.restoreAssetScanStateForAssignScan,
        data: formData,
        useFormUrlEncoded: true,
      );
      return result.statusCode ?? 0;
    }, 'Error workflow on restore asset scanState for assignScan.');
  }

  @override
  Future<dynamic> httpUpdateScanTaskLock(Map<String, dynamic> formData) async {
    return executeRepositoryTask<dynamic>(() async {
      final result = await dioUtil.post(GlobalVariable.updateScanTaskLock, data: formData, useFormUrlEncoded: true);
      return result.statusCode ?? 0;
    }, 'Error workflow on update scan task.');
  }

  @override
  Future<dynamic> httpWorkflowUnClaim(Map<String, dynamic> formData) async {
    return executeRepositoryTask<dynamic>(() async {
      final result = await dioUtil.post(GlobalVariable.workflowUnClaim, data: formData, useFormUrlEncoded: true);
      return result.statusCode ?? 0;
    }, 'Error workflow on unClaim.');
  }

  @override
  Future<List<WorkflowAssetColumnModel>> httpGetWorkflowAssetColumn(int workflowId, String engineId) async {
    return executeRepositoryTask<List<WorkflowAssetColumnModel>>(() async {
      final result = await dioUtil.get(
        GlobalVariable.workflowAssetColumn,
        queryParams: {'workflowId': workflowId, 'engineId': engineId, 'columnType': 2},
      );
      final data = result.data ?? {};
      final list = data['workflowAssetColumns'] as List<dynamic>? ?? [];
      final assetActionColumnList = list
          .map((e) => WorkflowAssetColumnModel.fromJson(e as Map<String, dynamic>))
          .toList();
      return assetActionColumnList;
    }, 'Error get workflow asset column.');
  }

  @override
  Future<Map<String, dynamic>> httpCreateAssetList(Map<String, dynamic> formData) async {
    return executeRepositoryTask<Map<String, dynamic>>(() async {
      final result = await dioUtil.post(
        GlobalVariable.workflowCreateAssetList,
        data: formData,
        useFormUrlEncoded: true,
      );
      final data = result.data;
      if (data == null) {
        LogUtil.w('Error create asset list id, data is null');
        return {};
      }
      return data;
    }, 'Error create asset list id');
  }
}
