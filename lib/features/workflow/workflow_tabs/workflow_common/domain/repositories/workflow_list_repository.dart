import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/update_by_barcode_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_asset_column_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_scan_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_task_model.dart';

abstract class WorkflowListRepository {
  /// 获取应用工作流列表
  Future<List<WorkflowEngineTaskModel>> getWorkflowList(dynamic params);

  Future<List<WorkflowAssignDynamicTaskModel>> getWorkflowAssignDynamicTaskListByTaskId(
    String processDefinitionId, {
    String? taskDefKey,
  });

  Future<String> saveTemporaryWorkFlow(Map<String, dynamic> formData);

  Future<WorkflowAssignScanModel> getWorkflowTaskForm({
    required String processInstanceId,
    required String taskId,
    int? scanState,
    String? keyword,
    int skip,
    int rows,
    bool needLoading = true,
    bool delay = true,
  });

  Future<String> startWorkflow(Map<String, dynamic> formData);

  Future<String> startWithThirdFlow(Map<String, dynamic> formData);

  Future<String> startTransitFlow(Map<String, dynamic> formData);

  Future<String> workflowStartSaveTemporary(Map<String, dynamic> formData);

  Future<UpdateByBarcodeResponseModel> mobileUpdateByBarCode(Map<String, dynamic> formData);

  Future<Map> getMasterDefaultValue(int masterTypeId);

  Future<int> httpRestoreAssetScanState(Map<String, dynamic> formData);

  Future<dynamic> httpGetWorkflowTaskForm(String processInstanceId, String taskId, String state);

  Future<dynamic> httpWorkFlowCancel(String processInstanceId);

  Future<dynamic> httpWorkflowTransit(Map<String, dynamic> formData);

  /// remand Work Flow Start
  Future<dynamic> httpWorkflowsSendBack(Map<String, dynamic> formData);

  /// フォーム値の保存
  Future<dynamic> httpWorkflowsSaveTemporaryFormData(Map<String, dynamic> formData);

  /// remand Work Flow Start
  Future<dynamic> httpWorkflowsSaveTemporaryFromScanTask(Map<String, dynamic> formData);

  /// 【特定アサイン用】ページ割り付き、WF資産リストにて、資産ID、及び、資産テキストの項目値をキーワードとする検索
  Future<dynamic> httpRestoreAssetScanStateForAssignScan(Map<String, dynamic> formData);

  Future<dynamic> httpUpdateScanTaskLock(Map<String, dynamic> formData);

  Future<dynamic> httpWorkflowUnClaim(Map<String, dynamic> formData);

  /// ワークフローの資産リストの自由表示項目取得
  Future<List<WorkflowAssetColumnModel>> httpGetWorkflowAssetColumn(int workflowId, String engineId);

  /// 调用时如果不存在AssetList的话，会创建并返回AssetListId，存在的话会直接返回AssetListId(用于在新规wf和一时保存wf时，扫描qrcode之前调用，用于创建一个assetListId)
  Future<Map<String, dynamic>> httpCreateAssetList(Map<String, dynamic> formData);
}
