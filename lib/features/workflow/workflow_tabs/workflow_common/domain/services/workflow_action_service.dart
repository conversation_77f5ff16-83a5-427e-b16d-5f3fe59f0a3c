import 'dart:convert';

import 'package:asset_force_mobile_v2/core/exceptions/business_exception.dart';
import 'package:asset_force_mobile_v2/core/exceptions/system_exception.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_category/data/models/category_model.dart';
import 'package:asset_force_mobile_v2/features/home/<USER>/usecases/get_role_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/login/login_traditional/data/models/user_role_model.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/layout_setting_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/update_by_barcode_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_asset_column_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/master_detail_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_asset_list_data_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_scan_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_task_form_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:get/get.dart';

class WorkflowActionService {
  final WorkflowListRepository workflowListRepository;
  WorkflowActionService({required this.workflowListRepository});

  Future<String> saveTemporaryWorkFlow(Map<String, dynamic> params) {
    try {
      return workflowListRepository.saveTemporaryWorkFlow(params);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('UseCase Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('一時保存に失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<String> startWorkflow(Map<String, dynamic> params) {
    try {
      return workflowListRepository.startWorkflow(params);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('UseCase Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('新規WFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<String> startWithThirdFlow(Map<String, dynamic> params) {
    try {
      return workflowListRepository.startWithThirdFlow(params);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('UseCase Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('新規WFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<String> transitFlowStart(Map<String, dynamic> params) {
    try {
      return workflowListRepository.startTransitFlow(params);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('UseCase Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('新規WFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<String> workflowStartSaveTemporary(Map<String, dynamic> params) {
    try {
      return workflowListRepository.workflowStartSaveTemporary(params);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('UseCase Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('新規WFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> workflowCancel(String processInstanceId) async {
    try {
      await workflowListRepository.httpWorkFlowCancel(processInstanceId);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('UseCase Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('キャンセルWFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// サブフォーム更新する
  Future<UpdateByBarcodeResponseModel> workflowsMobileUpdateByBarCode(Map<String, dynamic> params) {
    try {
      return workflowListRepository.mobileUpdateByBarCode(params);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('UseCase Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('新規WFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<List<WorkflowAssignDynamicTaskModel>> getDynamicTaskInfo(String processDefinitionId) async {
    try {
      return await workflowListRepository.getWorkflowAssignDynamicTaskListByTaskId(processDefinitionId);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('UseCase Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('新規WFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<List> getGroupSelectInfo(String groupIdsStr) async {
    final roleListUseCase = Get.find<GetRoleListUseCase>();
    final List<UserRoleModel> roleList = []; // 初始化为空列表
    try {
      final roleObj = await roleListUseCase(const NoParams());
      if (roleObj != null && roleObj['roleList'] is List) {
        final dataList = roleObj['roleList'] as List;
        roleList.addAll(dataList.map((item) => UserRoleModel.fromJson(item as Map<String, dynamic>)).toList());
      }
    } catch (e) {
      return []; // 获取角色失败，直接返回空
    }
    final List arr = [];
    if (groupIdsStr.isNotEmpty && roleList.isNotEmpty) {
      List<dynamic> choseRoleIdObj;
      try {
        // 安全地解析 JSON
        final decoded = jsonDecode(groupIdsStr);
        if (decoded is List) {
          choseRoleIdObj = decoded;
        } else {
          return []; // 非列表格式，返回空
        }
      } catch (e) {
        return []; // JSON 解析失败，返回空
      }
      if (choseRoleIdObj.isNotEmpty) {
        for (final id in choseRoleIdObj) {
          final UserRoleModel? found = roleList.firstWhereOrNull((role) {
            return id is int && role.roleId == id;
          });
          if (found != null) {
            arr.add({'groupId': found.roleId, 'groupName': found.roleName});
          }
        }
      }
    }
    return arr;
  }

  Future<List<MasterDetailModel>> getMasterInfoById(int masterTypeId) async {
    try {
      final result = await workflowListRepository.getMasterDefaultValue(masterTypeId);
      if (result.isEmpty) {
        return [];
      }
      var masterInfo = <MasterDetailModel>[];
      if (result['masterDetail'] != null) {
        masterInfo = result['masterDetail'].map((item) => MasterDetailModel.fromJson(item)).toList();
      }
      return masterInfo;
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('UseCase Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('get Workflow master default value By masterTypeId');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> restoreAssetScanState(Map<String, dynamic> formData) async {
    final resultCode = await workflowListRepository.httpRestoreAssetScanState(formData);
    if (resultCode == 0) {
      clearLocalData();
    }
  }

  Future<Map> getCustomItem() async {
    final dynamicItemDic = {};
    if (Get.isRegistered<LayoutSettingRepository>()) {
      final layoutSettingRepository = Get.find<LayoutSettingRepository>();
      final layoutSetting = await layoutSettingRepository.getLayoutSetting();
      for (var item in layoutSetting) dynamicItemDic[item.itemId.toString()] = item.itemName;
    }
    return dynamicItemDic;
  }

  Future<Map<String, dynamic>> getWorkflowTaskForm(
    String processInstanceId,
    String taskId, {
    String state = '0',
  }) async {
    if (state != '1') state = '0';
    final result = await workflowListRepository.httpGetWorkflowTaskForm(processInstanceId, taskId, state);
    final WorkflowTaskFormResponseModel workflowTaskFormResponse = WorkflowTaskFormResponseModel.fromJson(result);

    final Map<String, List<AssetItemListModel>> sectionMap = {};
    final Map<String, bool> permissions = {};
    final List<String> sectionList = [];
    dynamic commentData;

    for (final workflow in workflowTaskFormResponse.form ?? <WorkflowEngineTaskFormItemModel>[]) {
      final form = workflow.workflowForm;
      if (form != null) {
        final enabled = workflow.formField != null;
        if (form.itemName != null && form.itemName!.isNotEmpty) {
          permissions[form.itemName!] = enabled;
        }
        if (form.option != null && form.option!.isNotEmpty)
          form.optionObject = OptionObjModel.fromJson(jsonDecode(form.option!));
        final variable = workflow.variable;
        if (variable == 'null') {
          form.defaultData = 'null';
        } else {
          switch (form.itemType) {
            case 'master':
            case 'digitalSign':
              if (variable != null && variable.isNotEmpty && variable != 'null') {
                form.defaultData = jsonDecode(variable);
              } else {
                form.defaultData = variable;
              }
              break;
            case 'file':
            case 'image':
              try {
                if (variable != null && variable.isNotEmpty && variable != 'null') {
                  form.defaultData = jsonDecode(variable);
                } else {
                  form.defaultData = variable;
                }
              } catch (e) {
                // 图片或文件获取失败
                LogUtil.e('Error parsing file/image variable: $e');
              }
              break;
            case 'checkbox':
              if (form.optionObject?.checkboxMultiFlg == '1') {
                try {
                  if (variable == null || variable.isEmpty) {
                    form.defaultData = <dynamic>[];
                  } else if (variable == 'null') {
                    form.defaultData = <dynamic>[];
                  } else {
                    // 尝试解析为 JSON，如果失败则作为单个值处理
                    try {
                      form.defaultData = jsonDecode(variable);
                    } catch (e) {
                      // 如果 JSON 解析失败，将其作为单个值处理
                      form.defaultData = [variable];
                    }
                  }
                } catch (e) {
                  LogUtil.e('解析复选框数据时出错: $e');
                  form.defaultData = <dynamic>[];
                }
              } else {
                form.defaultData = variable ?? '';
              }
              break;
            case 'userSelect':
              form.defaultData = (variable != null && variable.isNotEmpty) ? jsonDecode(variable) : '';
              break;
            default:
              if (form.itemType != 'label') {
                form.defaultData = variable ?? '';
              }
          }
        }
        // データ設定
        if (form.mobileFlg == '1') {
          // マスタ初期値のIDをmasterIdに保存する
          if (form.itemType == 'master' && form.defaultData != null && form.defaultData != '') {
            form.masterId = form.defaultData.toString();
          }
          // 按 sectionName 分组存储
          if (form.sectionName != null) {
            if (sectionMap.containsKey(form.sectionName!)) {
              sectionMap[form.sectionName!]?.add(form);
            } else {
              final itemList = <AssetItemListModel>[];
              itemList.add(form);
              sectionMap[form.sectionName!] = itemList;
            }
          }
        }
        if (form.itemType != 'commentItem' && form.optionObject != null) {
          // 編集権限チェック
          form.optionObject!.readonly = (workflow.formField != null) ? '0' : '1';
          form.isEditPermissions = (workflow.formField != null) ? '1' : '0';
        } else {
          commentData = variable;
        }
        //概覧権限チェック
        if (form.sectionName != null && form.sectionName!.isNotEmpty) {
          if (form.optionObject!.sectionPrivateGroups == null) {
            if (!sectionList.contains(form.sectionName!)) {
              sectionList.add(form.sectionName!);
            }
          } else {
            final controller = Get.find<WorkflowCommonController>();
            final bool isView = await controller.checkIsView(form.optionObject!.sectionPrivateGroups!);
            if (isView) {
              if (!sectionList.contains(form.sectionName!)) {
                sectionList.add(form.sectionName!);
              }
            }
          }
        }
      }
    }
    return {
      'sectionList': sectionList,
      'sectionDic': sortData(sectionMap),
      'resultData': result,
      'commentData': commentData,
      'permissions': permissions,
    };
  }

  Map<String, List<AssetItemListModel>> sortData(Map<String, List<AssetItemListModel>> dict) {
    dict.forEach((key, itemList) {
      itemList.sort((a, b) {
        final num? aX = a.positionX;
        final num? bX = b.positionX;

        if (aX != null && bX != null) {
          if (aX > bX) return 1;
          if (aX < bX) return -1;
          return 0;
        }
        return 0;
      });

      itemList.sort((a, b) {
        final num? aY = a.positionY;
        final num? bY = b.positionY;

        if (aY != null && bY != null) {
          if (aY > bY) return 1;
          if (aY < bY) return -1;
          return 0;
        }
        return 0;
      });
    });
    return dict;
  }

  /// approval Work Flow
  Future<void> approvalWorkFlow(Map<String, dynamic> formData) async {
    try {
      return await workflowListRepository.httpWorkflowTransit(formData);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('承认WFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> sendBackWorkFlow(Map<String, dynamic> formData) async {
    try {
      return await workflowListRepository.httpWorkflowsSendBack(formData);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('差し戻しWFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<WorkflowAssignScanModel> getAssetsByKeywordInWfAssetListForAssignScan(
    String processInstanceId,
    String taskId,
    String? keyWord,
    int skip,
    int row, {
    int? scanState,
  }) async {
    try {
      final result = await workflowListRepository.getWorkflowTaskForm(
        processInstanceId: processInstanceId,
        keyword: keyWord,
        taskId: taskId,
        skip: skip,
        rows: row,
        scanState: scanState,
      );
      return result;
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('スキャン結果の取得に失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// フォーム値の保存
  Future<dynamic> saveTemporaryFormData(Map<String, dynamic> formData) async {
    try {
      return await workflowListRepository.httpWorkflowsSaveTemporaryFormData(formData);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('保存WFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<dynamic> saveTemporaryFromScanTaskWorkFlow(Map<String, dynamic> formData) async {
    try {
      return await workflowListRepository.httpWorkflowsSaveTemporaryFromScanTask(formData);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('保存scanTask WFに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<dynamic> restoreAssetScanStateForAssignScan(Map<String, dynamic> formData) async {
    try {
      return await workflowListRepository.httpRestoreAssetScanStateForAssignScan(formData);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('restore asset scanStateに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<dynamic> updateScanTaskLock(Map<String, dynamic> formData) async {
    try {
      return await workflowListRepository.httpUpdateScanTaskLock(formData);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('更新スキャンタスクロックに失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<dynamic> workflowUnClaim(Map<String, dynamic> formData) async {
    try {
      return await workflowListRepository.httpWorkflowUnClaim(formData);
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('approval Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('WFプロセスを受取解除に失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<List<WorkflowAssetColumnModel>> getWorkflowAssetColumn(int? workflowId, String? engineId) async {
    try {
      if (workflowId == null || engineId == null) return [];
      final result = await workflowListRepository.httpGetWorkflowAssetColumn(workflowId, engineId);
      result.sort((a, b) => (a.itemSort ?? 0).compareTo(b.itemSort ?? 0));
      return result;
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('getWorkflowAssetColumn Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('ワークフローの資産リストの自由表示項目取得に失敗しました。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  void runJavaScripInWF(
    String javaScriptText,
    List<WorkflowAssetListDataModel>? assetList,
    String stepName,
    String actionName,
    Map<String, List<AssetItemListModel>> itemDataDict, {
    processInstanceId = '',
    taskId = '',
  }) async {}

  void clearLocalData() {
    StorageUtils.set(StorageUtils.keyIsDeleteAllAssetAtBefore, 0); // 重置资产状态后flag也要重置一下
    StorageUtils.set(StorageUtils.keyTempEditAssetList, '[]'); // 重置资产状态后备份用的资产list也要重置一下
  }

  // 增加数量
  Future<void> plusClick(RegisteredAssetListModel item, {isUpdateAsset = false, Map<String, dynamic>? param}) async {
    if (isUpdateAsset) {
      param?['location'] = item.assetText?['location'];
      final result = await workflowsMobileUpdateByBarCode(param ?? {});
      item.assetAmount.value = item.assetAmount.value + 1;
      if (!result.isSuccessScan) {
        item.assetAmount.value = item.assetAmount.value - 1;
      } else {
        item.assetAmountBeforeScan = result.assetAmountBeforeScan;
      }
    } else {
      item.assetScanedCount.value += 1;
      checkAssetCount(item);
    }
  }

  // 减少数量
  Future<void> minusClick(
    RegisteredAssetListModel item, {
    bool isUpdateAsset = false,
    Map<String, dynamic>? param,
    bool isFromCreateNewWF = false,
  }) async {
    if (isUpdateAsset) {
      param?['location'] = item.assetText?['location'];
      final result = await workflowsMobileUpdateByBarCode(param ?? {});
      if ((item.assetAmount.value) > 1) {
        item.assetAmount.value -= 1;
      }
      if (!result.isSuccessScan) {
        item.assetAmount.value += 1;
      } else {
        item.assetAmountBeforeScan = result.assetAmountBeforeScan;
      }
    } else if (isFromCreateNewWF) {
      if (item.assetScanedCount.value > 1) {
        item.assetScanedCount.value -= 1;
      }
      checkAssetCount(item);
    } else {
      if (item.assetScanedCount.value >= 1) {
        item.assetScanedCount.value -= 1;
      }
      checkAssetCount(item);
    }
  }

  Future<bool> updateCount(
    RegisteredAssetListModel item,
    String value, {
    bool isUpdateAsset = false,
    Map<String, dynamic>? param = null,
    bool needResetAmount = true,
  }) async {
    final isApprovalSys = item.isApprovalSys ?? false;
    final dialogService = Get.find<DialogService>();
    if (value.isEmpty) {
      await dialogService.show(
        content: '数量を入力してください。',
        onConfirm: () {
          if (needResetAmount) {
            if (isUpdateAsset) {
              item.assetAmount.value = item.oldAssetAmount ?? 0;
            } else {
              item.assetScanedCount.value = 1;
            }
          }
        },
        confirmText: 'はい',
      );
      return false;
    } else if (value == '0' && isUpdateAsset && !isApprovalSys) {
      param?['amount'] = item.assetAmount.value = 1;
      param?['location'] = item.assetText?['location'];
      final result = await workflowsMobileUpdateByBarCode({'location': item.assetText['location']});
      if (result.isSuccessScan) {
        item.assetAmountBeforeScan = result.assetAmountBeforeScan;
      }
      await dialogService.show(content: '1以上の数字を入力してください。');
      return false;
    } else if (value == '0' && !isUpdateAsset && !isApprovalSys) {
      item.assetScanedCount.value = 1;
      await dialogService.show(content: '1以上の数字を入力してください。');
      return false;
    }

    final hankakudata = value.replaceAll(RegExp(r'\b(0+)'), '');
    if (hankakudata.isNotEmpty) {
      if (RegExp(r'^\d+$').hasMatch(hankakudata)) {
        return await isWhenAllInteger(item, hankakudata, isUpdateAsset, param);
      } else {
        final val = hankakudata.replaceAll(RegExp(r'[^0-9]'), '');
        await isWhenNotInteger(item, val, isUpdateAsset, param);
        return false;
      }
    }
    return false;
  }

  /// 在庫数量はマイナスにならないように
  bool checkAssetCount(RegisteredAssetListModel item) {
    if (item.assetScanedCount < 0) {
      item.assetScanedCount.value = 0;
      return false;
    }
    if (item.assetTotalCount != null && item.assetScanedCount.value > item.assetTotalCount!) {
      item.assetScanedCount.value = item.assetTotalCount!;
      return false;
    }
    return true;
  }

  Future<void> isWhenNotInteger(
    RegisteredAssetListModel item,
    String val,
    bool isUpdateAsset,
    Map<String, dynamic>? param,
  ) async {
    if (isUpdateAsset) {
      item.assetAmount.value = int.parse(val);
    } else {
      item.assetScanedCount.value = int.parse(val);
    }
    await CommonDialog.show(content: '数字のみを入力して下さい', confirmText: 'はい');
  }

  Future<bool> isWhenAllInteger(
    RegisteredAssetListModel item,
    String amount,
    bool isUpdateAsset,
    Map<String, dynamic>? param,
  ) async {
    if (isUpdateAsset) {
      param?['amount'] = item.assetAmount.value = int.parse(amount);
      param?['location'] = item.assetText?['location'];
      final result = await workflowsMobileUpdateByBarCode(param!);
      if (result.isSuccessScan) {
        item.assetAmount.value = item.tempAssetCount ?? 0;
      } else {
        item.tempAssetCount = item.assetAmount.value;
        item.assetAmountBeforeScan = result.assetAmountBeforeScan;
      }
    } else {
      item.assetScanedCount.value = int.parse(amount);
    }
    return checkAssetCount(item);
  }

  Future<int?> workflowCreateAssetList(Map<String, dynamic> formData) async {
    try {
      final result = await workflowListRepository.httpCreateAssetList(formData);
      final assetListIdRaw = result['assetListId'];
      int? assetListId;
      if (assetListIdRaw is int) {
        assetListId = assetListIdRaw;
      } else if (assetListIdRaw is String) {
        assetListId = int.tryParse(assetListIdRaw);
      }
      if (assetListId == null) {
        throw BusinessException('assetListIdの型が正しくありません。');
      }
      return assetListId;
    } on BusinessException catch (e, stackTrace) {
      LogUtil.e('getWorkflowAssetColumn Error: BusinessException: $e', stackTrace: stackTrace);
      throw BusinessException('パラメータが正しくありません。もう一度お試しください。');
    } on SystemException catch (e, stackTrace) {
      LogUtil.e('SystemException: $e', stackTrace: stackTrace);
      rethrow;
    }
  }
}
