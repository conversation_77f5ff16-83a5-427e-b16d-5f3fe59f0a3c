import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_ai_ocr_type_enum.dart';
import 'package:asset_force_mobile_v2/features/skill_plugin/workflow_scan_plugin.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';

enum NewApplicationWorkflow {
  newW1ScanListPage,
  newW1FormPage,
  w1AssetListViewPage,
  w1NewScanListPage,
  newW3FormPage, //可能废弃
  quantityPage, //可能废弃
}

class WorkflowScanBarcodeData {
  String? assetTypeId;
  String? locationInfo;
  String? jobType;
  bool isFromNew;
  List<dynamic>? scannedAssetIdList;
  bool? isCountingType;
  int? searchId;
  int? workflowId;
  String? engineId;
  int? scannedAssetCount;
  List<String>? barcodeList;
  String? processInstanceId;
  String? progressDefinitionId;
  List<dynamic>? editAssetList;
  String? taskId;
  NewApplicationWorkflow? whatKindWF;
  String? state; // '0' | '1' | '2' | null
  ScanType? scanType;
  bool isFromLocationSettingPage;
  String assetTypeName;
  String taskName;
  String workflowName;
  int? timestamp; // 添加 timestamp 字段，用于原生通信
  String? assetListId;
  bool? isMultiScan;
  bool? showTrim;
  int? totalSavedAssetAmountCount;
  List<dynamic>? assetDataList;

  WorkflowScanBarcodeData({
    this.assetTypeId,
    this.locationInfo,
    this.jobType,
    required this.isFromNew,
    this.scannedAssetIdList,
    this.isCountingType,
    this.searchId,
    this.workflowId,
    this.engineId,
    this.scannedAssetCount,
    this.barcodeList,
    this.processInstanceId,
    this.progressDefinitionId,
    this.editAssetList,
    this.taskId,
    this.whatKindWF,
    this.state,
    this.scanType,
    required this.isFromLocationSettingPage,
    this.workflowName = '',
    this.assetTypeName = '',
    this.taskName = '',
    this.timestamp,
    this.assetListId,
    this.isMultiScan,
    this.showTrim,
    this.totalSavedAssetAmountCount,
    this.assetDataList,
  });
  Map<String, dynamic> toMap() {
    return {
      'assetTypeId': assetTypeId,
      'locationInfo': locationInfo,
      'jobType': jobType,
      'isFromNew': isFromNew,
      'scanedAssetidList': scannedAssetIdList,
      'scanedAssetCount': scannedAssetCount,
      'isCountingType': isCountingType,
      'searchId': searchId,
      'workflowId': workflowId,
      'engineId': engineId,
      'barcodeList': barcodeList,
      'processInstanceId': processInstanceId,
      'editAssetList': editAssetList,
      'taskId': taskId,
      'whatKindWF': whatKindWF?.toString(), // 枚举转换为字符串
      'state': state,
      'scanType': scanType?.name,
      'isFromLocationSettingPage': isFromLocationSettingPage,
      'assetTypeName': assetTypeName,
      'taskName': taskName,
      'workflowName': workflowName,
      'timestamp': timestamp ?? DateTime.now().millisecondsSinceEpoch, // 添加时间戳，如果没有提供则使用当前时间
      'progressDefinitionId': progressDefinitionId,
      'assetListId': assetListId,
      'isMultiScan': isMultiScan,
      'showTrim': showTrim,
      'totalSavedAssetAmountCount': totalSavedAssetAmountCount,
      'assetDataList': assetDataList,
      // 'realAssetidList': scannedAssetIdList
    };
  }
}

class WorkflowCommonScanService {
  WorkflowListRepository repository;
  WorkflowCommonScanService({required this.repository});

  Future<void> startWorkflowScanBarcode(
    WorkflowScanBarcodeData data,
    Function(Map<String, dynamic>) returnValueAfterScan,
  ) async {
    if (!data.isFromNew) {
      final getAssetData = await repository.getWorkflowTaskForm(
        processInstanceId: data.processInstanceId ?? '',
        taskId: data.taskId ?? '',
      );
      data.scannedAssetCount = getAssetData.allAssetCount;
    }
    // if (!data.isFromLocationSettingPage) {
    //   // final userName = await StorageUtils.get(StorageUtils.keyUserName);
    //   final locationInfo = await StorageUtils.getAssetScanLocation();
    //   data.locationInfo = locationInfo;
    // }
    LogUtil.d('startWorkflowScanBarcode ===> ${data.isFromNew ? "WF新规" : "非新规WF"} ===> 传入参数: $data');
    // final scanData = data.toMap();
    final getScanData = await WorkflowScanPlugin().workflowScanBarcode(data);
    if (getScanData.isEmpty) {
      return;
    }
    // if (getScanData['type'] == '5') {
    // todo
    // 场所已弹窗方式出现
    // const modal = await modalController.create({
    //   component: LocationSelectPage,
    //   componentProps: {
    //     isPlusBtn: 'true',
    //   },
    // });
    // 弹出
    // await modal.present();

    //   startWorkflowScanBarcode(data, returnValueAfterScan);
    // }
    // 用户选择关闭getScanData = {_Map} size = 3
    if (getScanData['type'] == '2' && getScanData['isFromCloseBtn'] == '1') {
      if (!data.isFromNew && data.whatKindWF == 'workflow/new/w1-asset-list/scan-list/scan-list.page.ts') {
        returnValueAfterScan({});
      }
      return;
    }
    final returnValue = {
      'registeredAssetList': getScanData['registeredAssetList'] ?? [],
      'editAssetList': getScanData['editAssetList'] ?? [],
      'showARInfo': getScanData['showARInfo'] ?? [],
      'scanType': ScanTypeExtension.fromString(getScanData['type']) ?? ScanType.barCode,
    };
    returnValueAfterScan(returnValue);
  }
}
