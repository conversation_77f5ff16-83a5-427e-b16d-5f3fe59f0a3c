import 'package:asset_force_mobile_v2/core/extensions/getx_extension.dart';
import 'package:asset_force_mobile_v2/core/js_engine/js_engine.dart';
import 'package:asset_force_mobile_v2/core/services/get_navigation_service.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/calc_asset_dict_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/check_validate_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/prepare_asset_data_usecase.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/controllers/af_customize_view_controller.dart';

import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/edit_form_controller.dart';
import 'package:get/get.dart';

class EditFormBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPutFenix(() => PrepareAssetDataUseCase());
    Get.lazyPut(() => GetNavigationService());
    Get.lazyPutFenix(() => CheckValidateUseCase());

    // 注册 JsExecutor 相关依赖（使用统一的服务）
    final jsExecutorContext = JsExecutorBindingService.registerWithoutTag();

    Get.lazyPutFenix(() => CalcAssetDictUseCase(Get.find<JsExecutor>()));

    Get.lazyPutFenix(
      () => AfCustomizeViewController(
        prepareAssetDataUseCase: Get.find<PrepareAssetDataUseCase>(),
        calcAssetDictUseCase: Get.find<CalcAssetDictUseCase>(),
        checkValidateUseCase: Get.find<CheckValidateUseCase>(),
        navigationService: Get.find<GetNavigationService>(),
        jsExecutor: Get.find<JsExecutor>(),
        jsExecutorContext: jsExecutorContext,
      ),
    );
    Get.lazyPut(() => EditFormController(navigationService: Get.find<GetNavigationService>()));
  }
}
