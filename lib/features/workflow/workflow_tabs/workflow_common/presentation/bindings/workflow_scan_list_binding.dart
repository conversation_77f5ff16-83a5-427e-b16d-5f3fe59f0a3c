import 'package:asset_force_mobile_v2/core/extensions/getx_extension.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/dio_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/data/repositories/scan_barcode_list_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/domain/repositories/scan_barcode_list_repository.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/data/repositories/asset_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_list/domain/repositories/asset_repository.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/common_search_bar_net_widget.dart';
import 'package:asset_force_mobile_v2/features/search/presentation/widgets/common_search_bar_widget.dart';
import 'package:asset_force_mobile_v2/features/shared/data/repositories/user_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/user_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/repositories/workflow_list_repository_impl.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/repositories/workflow_list_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_scan_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/usecases/workflow_get_layout_settings_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/usecases/workflow_scan_barcode_list_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_scan_list_controller.dart';
import 'package:get/get.dart';

class WorkflowScanListBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPutFenix(() => CommonSearchBarNetController());
    Get.lazyPutFenix(() => CommonSearchBarController());

    Get.lazyPut<UserRepository>(() => UserRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPut<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find<DioUtil>()));

    Get.lazyPut<ScanBarcodeListRepository>(() => ScanBarcodeListRepositoryImpl(dioUtil: Get.find()));

    Get.lazyPut<GetUserRoleUseCaseUseCase>(() => GetUserRoleUseCaseUseCase(userRepository: Get.find<UserRepository>()));
    Get.lazyPut<WorkflowGetLayoutSettingsUseCase>(
      () => WorkflowGetLayoutSettingsUseCase(assetRepository: Get.find<AssetRepository>()),
    );
    Get.lazyPut(() => GetTurlUseCase(s3Repository: Get.find<S3Repository>()));
    Get.lazyPut<WorkflowListRepository>(() => WorkflowListRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPut<AssetRepository>(() => AssetRepositoryImpl(dioUtil: Get.find<DioUtil>()));
    Get.lazyPut(() => WorkflowActionService(workflowListRepository: Get.find<WorkflowListRepository>()));
    Get.lazyPutFenix(() => WorkflowCommonScanService(repository: Get.find<WorkflowListRepository>()));

    Get.lazyPut(() => WorkflowScanBarcodeListUseCase(scanBarcodeListRepository: Get.find<ScanBarcodeListRepository>()));

    Get.lazyPutFenix(
      () => WorkflowScanListController(
        dialogService: Get.find<DialogService>(),
        getUserRoleUseCaseUseCase: Get.find<GetUserRoleUseCaseUseCase>(),
        workflowGetLayoutSettingsUseCase: Get.find<WorkflowGetLayoutSettingsUseCase>(),
        getTurlUseCase: Get.find<GetTurlUseCase>(),
        workflowActionService: Get.find<WorkflowActionService>(),
        workflowCommonScanService: Get.find<WorkflowCommonScanService>(),
        navigationService: Get.find<NavigationService>(),
      ),
    );
  }
}
