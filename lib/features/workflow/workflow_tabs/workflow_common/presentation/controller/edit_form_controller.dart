import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/services/get_navigation_service.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/overlay/overlay_mixin.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/state/edit_form_ui_state.dart';
import 'package:get/get.dart';

class EditFormController extends BaseController with OverlayMixin {
  final GetNavigationService navigationService;
  EditFormController({required this.navigationService});
  final EditFormUiState state = EditFormUiState();

  @override
  void onInit() {
    super.onInit();
    final params = Get.arguments;
    if (params != null) {
      if (params['assetDict'] != null) {
        state.assetDict = params['assetDict'];
      }
      if (params['comments'] != null) {
        state.comments = params['comments'];
      }
      state.workflowName = params['workflowName'] ?? '';
    }
  }

  @override
  void onReady() {
    super.onReady();
    try {
      final tabsController = Get.find<TabsController>();
      if (Get.isRegistered<TabsController>()) {
        tabsController.showBottomNavBar.value = false;
      }
    } catch (e) {
      // 处理找不到 Controller 的情况，可能 TabsPage 还没加载
      LogUtil.d('EditFormController error find tabController: $e');
    }
  }

  void back() {
    final editResult = {'assetDict': state.assetDict, 'comments': state.comments};
    navigationService.goBack(result: editResult);
  }
}
