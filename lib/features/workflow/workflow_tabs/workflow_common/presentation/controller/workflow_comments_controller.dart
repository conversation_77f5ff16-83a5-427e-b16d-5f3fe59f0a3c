import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:asset_force_mobile_v2/core/presentation/app_keyboard_manager.dart';

class WorkflowCommentsController extends BaseController {
  final RxList<CommentsModel> comments;
  final bool showButton;
  WorkflowCommentsController({required this.comments, this.showButton = true});
  final focusNode = FocusNode();
  final textEditingController = TextEditingController();
  final keyboardToolbarVisible = false.obs;
  final showToolBar = false.obs;

  @override
  void onInit() {
    super.onInit();
    // 设置当前页面的键盘操作
    AppKeyboardManager().setCurrentPageActions(onDone: handleKeyboardDone, onCancel: handleKeyboardCancel);

    focusNode.addListener(() {
      keyboardToolbarVisible.value = focusNode.hasFocus;
      if (!focusNode.hasFocus) {
        showToolBar.value = false;
        // 当失去焦点时，清除键盘配置，避免影响其他页面
        AppKeyboardManager().clearCurrentPageActions();
      } else {
        // 当获得焦点时，重新设置键盘配置
        AppKeyboardManager().setCurrentPageActions(onDone: handleKeyboardDone, onCancel: handleKeyboardCancel);
      }
    });
  }

  @override
  void onClose() {
    // 清除当前页面的键盘操作
    AppKeyboardManager().clearCurrentPageActions();
    super.onClose();
  }

  void addComment(BuildContext context) {
    showToolBar.value = true;
    final context = Get.context;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context!).requestFocus(focusNode);
      Scrollable.ensureVisible(
        context,
        alignment: 0.3, // 显示在屏幕下方30%位置
        duration: const Duration(milliseconds: 300),
      );
    });
  }

  void deleteComment(int index) {
    // final comments = state.comments;
    CommonDialog.show(
      content: 'コメントを削除しますか？',
      cancelText: 'いいえ',
      confirmText: 'はい',
      onConfirm: () {
        comments.removeAt(index);
      },
    );
  }

  void postComment() async {
    if (textEditingController.text.trim().isNotEmpty) {
      final userID = await StorageUtils.get<int>(StorageUtils.keyUserId);
      final firstName = await StorageUtils.get<String>(StorageUtils.keyFirstName) ?? '';
      final lastName = await StorageUtils.get<String>(StorageUtils.keyLastName) ?? '';
      final userName = lastName + ' ' + firstName;
      comments.add(
        CommentsModel(
          commentDate: DateTime.now().toString(),
          commentUser: userName,
          commentContent: textEditingController.text.trim(),
          commentUserId: userID,
          newInput: true,
        ),
      );
    }
    textEditingController.clear();
    focusNode.unfocus();
    showToolBar.value = false;
  }

  void cancelComment() {
    textEditingController.clear();
    focusNode.unfocus();
    showToolBar.value = false;
  }

  void handleKeyboardDone() {
    if (focusNode.hasFocus) {
      postComment();
    } else {
      SystemChannels.textInput.invokeMethod('TextInput.hide');
    }
  }

  void handleKeyboardCancel() {
    cancelComment();
  }
}
