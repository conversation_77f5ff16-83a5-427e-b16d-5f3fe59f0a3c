import 'dart:convert';

import 'package:asset_force_mobile_v2/core/presentation/base_controller.dart';
import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/dialog_service.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/usecase/usecase.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/number_utils.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/domain/usecase/get_user_role_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/user/user_role_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/widgets/common_dialog.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_asset_list_data_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_button_name_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_engine_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_search_condition.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_task_form_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_action_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

enum Order { descend, ascend }

class WorkflowCommonController extends BaseController {
  final DialogService dialogService;
  final GetUserRoleUseCaseUseCase getUserRoleUseCaseUseCase;
  final WorkflowActionService workflowActionService;
  WorkflowCommonController({
    required this.dialogService,
    required this.getUserRoleUseCaseUseCase,
    required this.workflowActionService,
  });
  final raiseButtonNameList = <Widget>[].obs;
  final navigationService = Get.find<NavigationService>();
  void scrollToTop(ScrollController scrollController) {
    if (scrollController.hasClients)
      scrollController.animateTo(0, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
  }

  List<WorkflowEngineTaskModel> sortByDeadlineDate(List<WorkflowEngineTaskModel> item, Order order) {
    final sortList = item.toList();
    sortList.sort((a, b) {
      final DateTime dateA = changeStringToDate(a.createdDate);
      final DateTime dateB = changeStringToDate(b.createdDate);
      return order == Order.descend ? dateA.compareTo(dateB) : dateB.compareTo(dateA);
    });
    return sortList;
  }

  /// 検索用にフォーマット
  String formatSearch(String str) {
    return hiraToKana(zenToHan(str));
  }

  /// 平假名转换为片假名
  String hiraToKana(String str) {
    return str.replaceAllMapped(RegExp(r'[\u3041-\u3096]'), (match) {
      final chr = match.group(0)!.codeUnitAt(0) + 0x60;
      return String.fromCharCode(chr);
    });
  }

  /// 全角英数字转换为半角英数字
  String zenToHan(String str) {
    return str.replaceAllMapped(RegExp(r'[Ａ-Ｚａ-ｚ０-９]'), (match) {
      return String.fromCharCode(match.group(0)!.codeUnitAt(0) - 0xFEE0);
    });
  }

  /// テキストと日付に変換
  DateTime changeStringToDate(dynamic str) {
    if (str is String) {
      return DateFormat('yyyy/MM/dd HH:mm:ss').parse(str);
    } else if (str is int) {
      // 假设数字表示时间戳（毫秒）
      return DateTime.fromMillisecondsSinceEpoch(str);
    } else if (str is DateTime) {
      return str;
    } else {
      throw ArgumentError('Invalid date format');
    }
  }

  String getDay(int day) {
    final DateTime now = DateTime.now();
    final DateTime futureDate = now.add(Duration(days: day));

    final String year = futureDate.year.toString();
    final String month = futureDate.month < 10 ? '0${futureDate.month}' : futureDate.month.toString();
    final String dayStr = futureDate.day < 10 ? '0${futureDate.day}' : futureDate.day.toString();

    return '$year-$month-$dayStr';
  }

  Future<void> defaultForDoneSearchCondition(String fromPage) async {
    dynamic searchConditionDataList;
    if (fromPage == 'application') {
      searchConditionDataList = StorageUtils.get<String>('${StorageUtils.keySearchConditionList}application');
    } else {
      searchConditionDataList = StorageUtils.get<String>('${StorageUtils.keySearchConditionList}approval');
    }

    if (searchConditionDataList == null || searchConditionDataList.isEmpty || searchConditionDataList == '[]') {
      final List<WorkflowSearchCondition> defaultDoneSearchConditionDataList = [];
      final defaultForDoneSearchCondition = WorkflowSearchCondition(
        name: 'state',
        nameIsNull: false,
        method: 'listInclude',
        methodIsNull: false,
        value: ['ACTIVE'],
        valueIsNull: false,
        itemType: 'multiSelect',
        isShow: true,
        valueForShow: 'ACTIVE',
      );
      defaultDoneSearchConditionDataList.add(defaultForDoneSearchCondition);
      final searchConditionList = jsonEncode(defaultDoneSearchConditionDataList.map((e) => e.toJson()).toList());
      if (fromPage == 'application') {
        await StorageUtils.set<String>('${StorageUtils.keySearchConditionList}application', searchConditionList);
      } else {
        await StorageUtils.set<String>('${StorageUtils.keySearchConditionList}approval', searchConditionList);
      }
    }
  }

  Future<void> defaultForAllSearchCondition(String fromPage) async {
    dynamic searchConditionDataList;
    if (fromPage == 'application') {
      searchConditionDataList = StorageUtils.get<String>('${StorageUtils.keySearchConditionList}application-all');
    } else {
      searchConditionDataList = StorageUtils.get<String>('${StorageUtils.keySearchConditionList}approval-all');
    }
    if (searchConditionDataList == null || searchConditionDataList.isEmpty || searchConditionDataList == '[]') {
      final List<WorkflowSearchCondition> defaultDoneSearchConditionDataList = [];
      final defaultForDoneSearchCondition = WorkflowSearchCondition(
        name: 'appliedDate',
        nameIsNull: false,
        method: 'gtOrEq',
        methodIsNull: false,
        value: getDay(-7),
        valueIsNull: false,
        itemType: 'date',
        isShow: true,
      );
      defaultDoneSearchConditionDataList.add(defaultForDoneSearchCondition);
      final searchConditionList = jsonEncode(defaultDoneSearchConditionDataList.map((e) => e.toJson()).toList());
      if (fromPage == 'application') {
        StorageUtils.set<String>('${StorageUtils.keySearchConditionList}application-all', searchConditionList);
      } else {
        StorageUtils.set<String>('${StorageUtils.keySearchConditionList}approval-all', searchConditionList);
      }
    }
  }

  Future<void> presentCancelConfirm({
    String processInstanceId = '',
    String taskId = '',
    List? editAssetList,
    Map? params,
  }) async {
    Future<void> backOkayFunAtUserTask() async {
      await showLoading();
      try {
        final formData = <String, dynamic>{};
        formData['processInstanceId'] = processInstanceId;
        formData['taskId'] = taskId;
        formData['assetListDataJson'] = jsonEncode(editAssetList ?? []);
        final assetList = await StorageUtils.get<String>(StorageUtils.keyTempEditAssetList);
        final isRestoreAllAssets = assetList != null && assetList != '[]' ? true : false;
        if (isRestoreAllAssets) {
          formData['clearedAssetListDataJson'] = assetList;
        } else {
          formData['clearedAssetListDataJson'] = jsonEncode([]);
        }
        await workflowActionService.restoreAssetScanState(formData);
        Get.back(id: SharedNavBarEnum.workflow.navigatorId, result: params);
      } finally {
        hideLoading();
      }
    }

    await dialogService.show(
      content: '保存せずに終了しますか？入力したデータ・及びスキャンデータは破棄されます。',
      cancelText: 'キャンセル',
      onConfirm: () async {
        if (Get.isRegistered<TabsController>()) {
          Get.find<TabsController>().showBar();
        }
        if (processInstanceId.isNotEmpty && taskId.isNotEmpty) {
          await backOkayFunAtUserTask();
        } else {
          Get.back(id: SharedNavBarEnum.workflow.navigatorId, result: params);
        }
      },
    );
  }

  // 申請
  void apply(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processDefinitionId,
    String? workflowScript,
    List? assignDynamicData,
    WorkflowRaiseButtonModel? raiseDic,
  ) async {
    final nameRaise = raiseDic?.nameRaise != null && raiseDic!.nameRaise.isNotEmpty ? raiseDic.nameRaise : '提出';
    baseAction(nameRaise, false, assetDict, () async {
      try {
        // todoカスタマイズロジック実行、（資産の新規作成、アップデート、取得）

        final formData = getFormData(assetDict, comments);
        formData.addAll({'processDefinitionId': processDefinitionId});
        if (assignDynamicData != null && assignDynamicData.isNotEmpty) {
          formData.addAll({'assignDynamicData': jsonEncode(assignDynamicData)});
        }
        // 后台新增字段，主要为了区分开点击的提出名字传给后台，即使是默认的提出两个字也要传给后台
        formData.addAll({'buttonName': nameRaise});
        final result = await workflowActionService.startWorkflow(formData);
        //todo 点击保存按钮后删除S3文件 deleteFileFromS3(this.http);
        actionWhenFinish('提出', '');
        if (result.isNotEmpty) {
          LogUtil.d('新规申请WF:$result');
        }
      } catch (e) {}
    }, () async {});
  }

  Future<void> applyWithList(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processDefinitionId,
    String processInstanceId,
    List<RegisteredAssetListModel> registeredAssetList,
    String workflowName,
    String assetTypeId,
    bool isNew,
    String taskId,
    String assetListId,
    bool updateAmountShow,
    List? assignDynamicData,
    bool isFromNew,
    bool isMyselfInputTask,
    WorkflowRaiseButtonModel? raiseDic,
    String? actionType,
    String? stepName,
    String workflowScript,
  ) async {
    final nameRaise = raiseDic?.nameRaise != null && raiseDic!.nameRaise.isNotEmpty ? raiseDic.nameRaise : '提出';
    await baseAction(nameRaise, false, assetDict, () async {
      try {
        // todo runJavaScripInWF
        final formData = getFormData(assetDict, comments);
        final assetIdList = <String>[];
        final eachAssetList = [];
        if (isFromNew) {
          if (registeredAssetList.isNotEmpty) {
            registeredAssetList.forEach((item) {
              if (updateAmountShow) {
                // 个数管理的情况
                eachAssetList.add({
                  'assetId': item.assetId.toString(),
                  'willChangeAmount': item.assetScanedCount.value,
                });
              } else {
                // 个体管理的情况
                assetIdList.add(item.assetId.toString());
              }
            });
          }
          final map = {};
          if (updateAmountShow) {
            // 個数管理の場合
            map['eachAssetList'] = eachAssetList;
          } else {
            // 個体管理の場合
            map['assetIds'] = assetIdList;
          }
          map['assetListTitle'] = workflowName;
          map['assetTypeId'] = assetTypeId;
          map['assetListId'] = assetListId;
          formData.addAll({'assetList': jsonEncode(map)});
        }
        if (assignDynamicData != null && assignDynamicData.isNotEmpty) {
          formData.addAll({'assignDynamicData': jsonEncode(assignDynamicData)});
        }
        if (isNew) {
          // 正常提出
          formData.addAll({'processDefinitionId': processDefinitionId});
          // 后台新增字段，主要为了区分开点击的提出名字传给后台，即使是默认的提出两个字也要传给后台
          formData.addAll({'buttonName': nameRaise});
          await workflowActionService.startWithThirdFlow(formData);
          // 点击保存按钮后删除S3文件 deleteFileFromS3(this.http);
          actionWhenFinish('提出', '');
          LogUtil.d('message');
        } else {
          if (isMyselfInputTask) {
            // 差し戻し
            formData.addAll({'taskId': taskId});
            formData.addAll({'mode': '2'});
            formData.addAll({'processInstanceId': processInstanceId});
            formData.addAll({'buttonName': raiseDic?.nameRaise ?? ''});
            final result = await workflowActionService.transitFlowStart(formData);
            //todo 点击保存按钮后删除S3文件 deleteFileFromS3(this.http)
            LogUtil.d(result);
          } else {
            // 一时保存
            formData.addAll({'status': 'active'});
            formData.addAll({'processInstanceId': processInstanceId});
            formData.addAll({'buttonName': raiseDic?.nameRaise ?? ''});
            final result = await workflowActionService.workflowStartSaveTemporary(formData);
            // todo 点击保存按钮后删除S3文件  deleteFileFromS3(this.http);
            LogUtil.d(result);
          }
          //重置flag和备份用的资产list
          StorageUtils.set(StorageUtils.keyIsDeleteAllAssetAtBefore, 0); // 重置资产状态后flag也要重置一下
          StorageUtils.set(StorageUtils.keyTempEditAssetList, '[]'); // 重置资产状态后备份用的资产list也要重置一下
          // 跳转至application
          Get.back(id: SharedNavBarEnum.workflow.navigatorId, result: {'initialIndex': 1});
        }
      } finally {}
    }, () async {});
  }

  /// 能否跟上面方法统一 applyWithList
  void activeApplyWithout(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processInstanceId,
    List? assignDynamicData,
    bool isMyselfInputTask,
    WorkflowRaiseButtonModel? raiseDic,
    String? stepName,
    String workflowScript,
  ) async {
    final nameRaise = raiseDic?.nameRaise != null && raiseDic!.nameRaise.isNotEmpty ? raiseDic.nameRaise : '提出';
    await baseAction(nameRaise, false, assetDict, () async {
      // final String WFActionType = isMyselfInputTask ? 'restart' : 'startNew';
      // run javaScript todo
      final formData = getFormData(assetDict, comments);
      formData['processInstanceId'] = processInstanceId;
      formData['status'] = processInstanceId;
      if (assignDynamicData != null && assignDynamicData.length > 0)
        formData['assignDynamicData'] = jsonEncode(assignDynamicData);
      formData['buttonName'] = nameRaise;
      await workflowActionService.workflowStartSaveTemporary(formData);
      if (Get.isRegistered<TabsController>()) {
        Get.find<TabsController>().showBar();
      }
      Get.back(id: SharedNavBarEnum.workflow.navigatorId);
    }, () async {});
  }

  void approval(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processInstanceId,
    List? assignDynamicData,
    String taskId,
    String workflowScript,
    WorkflowRaiseButtonModel? raiseDic,
    Function(bool result)? callBack,
  ) async {
    final nameRaise = raiseDic?.nameRaise != null && raiseDic!.nameRaise.isNotEmpty ? raiseDic.nameRaise : '承認';
    await baseAction(nameRaise, false, assetDict, () async {
      final formData = getFormData(assetDict, comments);
      formData['processInstanceId'] = processInstanceId;
      formData['taskId'] = taskId;
      formData['mode'] = '0';
      if (assignDynamicData != null && assignDynamicData.length > 0)
        formData['assignDynamicData'] = jsonEncode(assignDynamicData);
      formData['buttonName'] = nameRaise;
      if (callBack != null) {
        callBack(true);
      }
      try {
        await workflowActionService.approvalWorkFlow(formData);
      } catch (e) {
        if (callBack != null) {
          callBack(false);
        }
      }
      setNavigateBack('承認', id: SharedNavBarEnum.workflow.navigatorId);
    }, () async {});
  }

  void approvalWithList(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processInstanceId,
    String dataCount,
    String taskId,
    List? assignDynamicData,
    String stepName,
    String workflowScript,
    bool isAssetListEditable,
    WorkflowRaiseButtonModel? raiseDic,
  ) async {
    final nameRaise = raiseDic?.nameRaise != null && raiseDic!.nameRaise.isNotEmpty ? raiseDic.nameRaise : '承認';
    await baseAction(nameRaise, false, assetDict, () async {
      // todo runJavaScripInWF

      Future<void> confirm() async {
        final formData = getFormData(assetDict, comments);
        formData['processInstanceId'] = processInstanceId;
        formData['taskId'] = taskId;
        formData['mode'] = '0';
        // 后台新增字段，主要为了区分开点击的承認名字传给后台，即使是默认的承認两个字也要传给后台
        formData['buttonName'] = nameRaise;
        if (assignDynamicData != null && assignDynamicData.length > 0) {
          formData['assignDynamicData'] = jsonEncode(assignDynamicData);
        }
        await workflowActionService.approvalWorkFlow(formData);
        //重置flag和备份用的资产list
        workflowActionService.clearLocalData();
        // 跳转至application
        Get.back(id: SharedNavBarEnum.workflow.navigatorId, result: {'initialIndex': 1});
      }

      // 承認時資産が選択されない場合
      if (dataCount == '0' && isAssetListEditable) {
        await dialogService.show(
          content: '資産が選択されていませんが、承認しますか？',
          cancelText: 'キャンセル',
          onConfirm: () async {
            await confirm();
          },
        );
      } else {
        await confirm();
      }
    }, () async {});
  }

  Future<void> confirmForApproval(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    bool isFromAssetListPage,
    String processInstanceId,
    String taskId,
    bool isApproval,
    List assignDynamicData,
  ) async {
    final actionName = isApproval ? '承認' : '否決';
    if (!isFromAssetListPage) {
    } else {
      // 运行工作流JavaScript
      // final resultCus = await workflowActionService.runJavaScripInWF(
      //   workflowScript,
      //   confirmData.assetListDatas,
      //   stepName,
      //   actionNameCus,
      //   assetDict,
      //   processInstanceId: processInstanceId,
      //   taskId: taskId,
      // );
      //
      // if (resultCus == false) {
      //   await showValidationCheckAlert();
      //   return;
      // }
    }
    // 准备表单数据
    final formData = getFormData(assetDict, comments);
    formData['processInstanceId'] = processInstanceId;
    formData['taskId'] = taskId;
    formData['mode'] = isApproval ? '0' : '1';

    if (assignDynamicData.isNotEmpty) {
      formData['assignDynamicData'] = jsonEncode(assignDynamicData);
    }

    // 提交工作流
    await workflowActionService.approvalWorkFlow(formData);
    // 清理数据
    workflowActionService.clearLocalData();

    // 延迟返回
    await Future.delayed(const Duration(milliseconds: 500));
    setNavigateBack(actionName, id: SharedNavBarEnum.workflow.navigatorId);
  }

  /// approval 承认
  Future<bool> confirmWithScanCompleteNoSubprocessWithAllMust({
    required Map<String, List<AssetItemListModel>> assetDict,
    required List<CommentsModel> comments,
    required WorkflowTaskFormResponseModel? rawFormData,
    required String processInstanceId,
    required String taskId,
    required bool isApproval,
    required List assignDynamicData,
    required String buttonName,
    required String workflowScript,
    required bool isFromAssetListPage,
    int numberOfUnscanned = 0,
    int totalAssets = 0,
  }) async {
    final actionName = isApproval ? '承認' : '否決';
    final isBaseAction = await baseAction(
      buttonName.isEmpty ? actionName : buttonName,
      false,
      assetDict,
      () async {},
      () async {},
    );
    if (!isBaseAction) return false;
    // String? actionNameCus;
    // if (actionName == '承認') {
    //   actionNameCus = 'admit';
    // } else if (actionName == '否決') {
    //   actionNameCus = 'deny';
    // }

    bool isExecutionMarker = false;
    final isAllMustScan = rawFormData?.actions?.allMustScan ?? false;
    final isWFStatus = numberOfUnscanned > 0 && totalAssets >= numberOfUnscanned;
    if (isAllMustScan && numberOfUnscanned != 0) {
      isExecutionMarker = false;
    }
    if (isWFStatus) {
      await dialogService.show(
        content: 'スキャンされてない資産が存在しています。未スキャン資産はリストから削除されますがこのまま進めますか？',
        cancelText: 'キャンセル',
        onConfirm: () async {
          await confirmForApproval(
            assetDict,
            comments,
            isFromAssetListPage,
            processInstanceId,
            taskId,
            isApproval,
            assignDynamicData,
          );
          isExecutionMarker = true;
        },
        onCancel: () {
          isExecutionMarker = false;
        },
      );
    } else {
      isExecutionMarker = true;
      confirmForApproval(
        assetDict,
        comments,
        isFromAssetListPage,
        processInstanceId,
        taskId,
        isApproval,
        assignDynamicData,
      );
    }
    return isExecutionMarker;
  }

  /// 复数人专用
  void confirmWithMultiScanCompleteNoSubprocess({
    required Map<String, List<AssetItemListModel>> assetDict,
    required List<CommentsModel> comments,
    required WorkflowTaskFormResponseModel? rawFormData,
    required String processInstanceId,
    required String taskId,
    required bool isApproval,
    required List assignDynamicData,
    required String buttonName,
    required String workflowScript,
    required bool isFromAssetListPage,
    Function(bool result)? callBack,
  }) async {
    final actionName = isApproval ? '承認' : '否決';
    await baseAction(actionName, false, assetDict, () async {
      final isAllMustScan = rawFormData?.actions?.allMustScan ?? false;
      final totalAssetAmountCount = rawFormData?.totalAssetAmountCount ?? 0;
      final totalSavedAssetAmountCount = rawFormData?.totalSavedAssetAmountCount ?? 0;
      final isWFStatus = (totalAssetAmountCount != totalSavedAssetAmountCount);
      final isScanTask = (rawFormData?.actions?.taskDefKey ?? '').startsWith('ScanTask');
      if (!isAllMustScan && isWFStatus && isScanTask) {
        await dialogService.show(
          content: 'スキャンされてない資産が存在しています。未スキャン資産はリストから削除されますがこのまま進めますか？',
          cancelText: 'キャンセル',
          onConfirm: () async {
            try {
              await showLoading();
              if (callBack != null) callBack(true);
              await confirmForApproval(
                assetDict,
                comments,
                isFromAssetListPage,
                processInstanceId,
                taskId,
                isApproval,
                assignDynamicData,
              );
            } catch (e) {
              if (callBack != null) callBack(false);
            } finally {
              hideLoading();
            }
          },
          onCancel: () {
            if (callBack != null) callBack(false);
          },
        );
      } else {
        try {
          await showLoading();
          if (callBack != null) callBack(true);
          await confirmForApproval(
            assetDict,
            comments,
            isFromAssetListPage,
            processInstanceId,
            taskId,
            isApproval,
            assignDynamicData,
          );
        } catch (e) {
          if (callBack != null) callBack(false);
        } finally {
          hideLoading();
        }
      }
    }, () async {});
  }

  /// approval 承认
  void confirmWithScanComplete({
    required bool isAssetListEditable,
    required String assetCount,
    required Map<String, List<AssetItemListModel>> assetDict,
    required List<CommentsModel> comments,
    required WorkflowTaskFormResponseModel? rawFormData,
    required String processInstanceId,
    required String taskId,
    required bool isApproval,
    required List assignDynamicData,
    required String buttonName,
    required String workflowScript,
    required bool isFromAssetListPage,
    Function(bool result)? callBack,
  }) async {
    final actionName = isApproval ? '承認' : '否決';
    await baseAction(buttonName.isEmpty ? actionName : buttonName, false, assetDict, () async {
      // run javaScript
      // 承認時資産が選択されない場合
      if (assetCount == '0' && isAssetListEditable) {
        await dialogService.show(
          content: '資産が選択されていませんが、承認しますか？',
          cancelText: 'キャンセル',
          onConfirm: () async {
            try {
              await showLoading();
              if (callBack != null) callBack(true);
              await confirmForApproval(
                assetDict,
                comments,
                isFromAssetListPage,
                processInstanceId,
                taskId,
                isApproval,
                assignDynamicData,
              );
            } catch (e) {
              if (callBack != null) callBack(false);
            } finally {
              hideLoading();
            }
          },
          onCancel: () {
            if (callBack != null) callBack(false);
          },
        );
      } else {
        try {
          await showLoading();
          if (callBack != null) callBack(true);
          await confirmForApproval(
            assetDict,
            comments,
            isFromAssetListPage,
            processInstanceId,
            taskId,
            isApproval,
            assignDynamicData,
          );
        } catch (e) {
          if (callBack != null) callBack(false);
        } finally {
          hideLoading();
        }
      }
    }, () async {});
  }

  void rejection(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processInstanceId,
    String taskId,
    String workflowScript,
    String stepName,
    Function(bool result)? callBack,
    String buttonName,
  ) async {
    baseAction(buttonName.isEmpty ? '否决' : buttonName, false, assetDict, () async {
      // todo run javascript

      final formData = getFormData(assetDict, comments);
      formData['processInstanceId'] = processInstanceId;
      formData['taskId'] = taskId;
      formData['mode'] = 1;
      formData['buttonName'] = buttonName.isEmpty ? '否决' : buttonName;
      if (callBack != null) {
        callBack(true);
      }
      try {
        await workflowActionService.approvalWorkFlow(formData);
      } catch (e) {
        if (callBack != null) {
          callBack(false);
        }
      }
      setNavigateBack('否決', id: SharedNavBarEnum.workflow.navigatorId);
      if (callBack != null) {
        callBack(false);
      }
    }, () async {});
  }

  /// sendBack 解除
  void unClaimFormApproval(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processInstanceId,
    String taskId,
    Function(bool result)? callBack,
  ) async {
    final isValid = await validateAssetItems(assetDict);
    if (!isValid) return;

    await baseAction('ご自身に割り当てられたタスクを解除', false, assetDict, () async {
      try {
        await showLoading();

        // 准备表单数据
        final formData = getFormData(assetDict, comments);
        formData['processInstanceId'] = processInstanceId;
        formData['taskId'] = taskId;

        if (callBack != null) {
          callBack(true);
        }

        try {
          await workflowActionService.workflowUnClaim(formData);
          workflowActionService.clearLocalData();
        } catch (e) {
          if (callBack != null) {
            callBack(false);
          }
        }
        await Future.delayed(const Duration(milliseconds: 500));
        setNavigateBack('ご自身に割り当てられたタスクを解除', id: SharedNavBarEnum.workflow.navigatorId);
        if (callBack != null) {
          callBack(false);
        }
      } finally {
        hideLoading();
      }
    }, () async {});
  }

  void rejectionWithList(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processInstanceId,
    List assetListData,
    String assetListTitle,
    int? assetTypeIdWithFirstWf,
    String taskId,
    String workflowScript,
    String stepName,
    Function(bool result)? callBack,
    String buttonName,
  ) async {
    await baseAction(buttonName.isNotEmpty ? buttonName : '否決', false, assetDict, () async {
      // todo runJavaScripInWF

      final formData = getFormData(assetDict, comments);
      formData['processInstanceId'] = processInstanceId;
      formData['taskId'] = taskId;

      final assetList = {};
      assetList['assetListTitle'] = assetListTitle;
      assetList['assetTypeId'] = assetTypeIdWithFirstWf;
      final assetIdList = <String>[];
      for (var item in assetListData) {
        assetIdList.add(item);
      }
      assetList['assetIds'] = assetIdList;
      formData['assetList'] = jsonEncode(assetList);

      formData['mode'] = '1';
      formData['buttonName'] = buttonName.isNotEmpty ? buttonName : '否決';
      if (callBack != null) {
        callBack(false);
      }
      try {
        await workflowActionService.approvalWorkFlow(formData);
      } catch (e) {
        if (callBack != null) {
          callBack(false);
        }
      }
      //重置flag和备份用的资产list
      workflowActionService.clearLocalData();
      // 跳转至application
      Get.back(id: SharedNavBarEnum.workflow.navigatorId, result: {'initialIndex': 1});
    }, () async {});
  }

  Future<void> showValidationCheckAlert() async {
    await dialogService.show(
      content: '入力内容に誤りがあります、ご確認ください',
      onConfirm: () async {
        Get.back(result: {'initialIndex': 2}, id: SharedNavBarEnum.workflow.navigatorId);
      },
    );
  }

  /// rejection调用
  void confirmWithAssets(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processInstanceId,
    String taskId,
    List<WorkflowAssetListDataModel>? assetListDatas,
    String assetListTitle,
    int assetTypeIdWithFirstWf,
    String workflowScript,
    String stepName,
    Function(bool result)? callBack,
    bool isFromAssetListPage,
    bool isApproval,
    String buttonName,
  ) async {
    final actionName = isApproval ? '承認' : '否決';
    baseAction(buttonName.isEmpty ? actionName : buttonName, false, assetDict, () async {
      var actionNameCus;
      if (actionName == '承認') {
        actionNameCus = 'admit';
      } else if (actionName == '否決') {
        actionNameCus = 'deny';
      }
      if (!isFromAssetListPage) {
        // todo run javaScript
        workflowActionService.runJavaScripInWF(
          workflowScript,
          assetListDatas,
          stepName,
          actionNameCus,
          assetDict,
          processInstanceId: processInstanceId,
          taskId: taskId,
        );
      } else {
        // run javaScript
      }
      final formData = getFormData(assetDict, comments);
      formData['processInstanceId'] = processInstanceId;
      formData['taskId'] = taskId;
      final resAssetList = {};
      resAssetList['assetListTitle'] = assetListTitle;
      resAssetList['assetTypeId'] = assetTypeIdWithFirstWf;

      formData['assetList'] = jsonEncode(resAssetList);
      formData['mode'] = isApproval ? '0' : '1';
      formData['buttonName'] = buttonName.isEmpty ? actionName : buttonName;
      if (callBack != null) {
        callBack(true);
      }
      try {
        await workflowActionService.approvalWorkFlow(formData);
      } catch (e) {
        if (callBack != null) {
          callBack(false);
        }
      }
      workflowActionService.clearLocalData();
      setNavigateBack(actionName, id: SharedNavBarEnum.workflow.navigatorId, backPage: AutoRoutes.appTabWorkflowTabs);
      if (callBack != null) {
        callBack(false);
      }
    }, () async {});
  }

  void workflowSendBack(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String sendBackDestination,
    String taskId,
    String workflowScript,
    String stepName,
    Function(bool result)? callBack,
    String buttonName, {
    int backTabIndex = 0,
  }) async {
    await baseAction(buttonName.isNotEmpty ? buttonName : '差戻し', false, assetDict, () async {
      // todo runJavaScripInWF

      final formData = getFormData(assetDict, comments);
      formData['sendBackDestination'] = sendBackDestination;
      formData['taskId'] = taskId;
      formData['buttonName'] = buttonName.isEmpty ? '差戻し' : buttonName;

      if (callBack != null) {
        callBack(true);
      }
      try {
        await workflowActionService.sendBackWorkFlow(formData);
      } catch (e) {
        if (callBack != null) {
          callBack(false);
        }
      }
      //重置flag和备份用的资产list
      workflowActionService.clearLocalData();
      // 跳转至application
      Get.offNamedUntil(
        AutoRoutes.appTabWorkflowTabs,
        (route) => route.settings.name == AutoRoutes.workflow,
        id: 3,
        arguments: {'initialIndex': backTabIndex},
      );
      // Get.back(id: SharedNavBarEnum.workflow.navigatorId, result: {'initialIndex': backTabIndex});
    }, () async {});
  }

  /// 一時保存
  void saveWorkflowData(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processDefinitionId,
    bool isSaveAction,
    String backUrl,
  ) async {
    final Map<String, List<AssetItemListModel>> tempAssetDict = Map.from(assetDict);
    // 遍历 assetDict，确保 "WF名" 的 defaultData 不为空
    tempAssetDict.forEach((sectionName, items) {
      for (var item in items) {
        if (item.itemName == 'WF名' && (item.defaultData == null || item.defaultData.isEmpty)) {
          item.defaultData = '';
        }
      }
    });
    baseAction('一時保存', isSaveAction, assetDict, () async {
      final formData = await getFormData(assetDict, comments);
      formData['processDefinitionId'] = processDefinitionId;
      String result = '';
      result = await workflowActionService.saveTemporaryWorkFlow(formData);
      // todo 点击保存按钮后删除S3文件 deleteFileFromS3(this.http);
      actionWhenFinish('一時保存', backUrl);
      LogUtil.d(result);
    }, () async {});
  }

  /// 入力項目（フォーム値）を保存 (w1+assets)
  void saveTemporaryFormData(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processInstanceId,
    dynamic taskId,
    List? assignDynamicData,
  ) async {
    baseAction('保存して終了', false, assetDict, () async {
      final formData = getFormData(assetDict, comments);
      formData['processInstanceId'] = processInstanceId;
      formData['status'] = processInstanceId;
      if (assignDynamicData != null && assignDynamicData.length > 0)
        formData['assignDynamicData'] = jsonEncode(assignDynamicData);
      await workflowActionService.saveTemporaryFormData(formData);
      setNavigateBack('保存', id: SharedNavBarEnum.workflow.navigatorId);
    }, () async {});
  }

  Future<void> setNavigateBack(String actionName, {int? id, String? backPage, dynamic params}) async {
    dialogService.showCustomToast('$actionName完了しました');
    if (id != null && Get.isRegistered<TabsController>()) {
      Get.find<TabsController>().showBar();
    }
    if (backPage != null) {
      navigationService.navigateUntil(backPage, id: id, arguments: params);
    } else {
      navigationService.goBack(id: id, result: params);
    }
  }

  Future<void> actionWhenFinish(String actionName, String url) async {
    final successText = actionName + '完了しました';
    CommonDialog.showCustomToast(successText, duration: const Duration(seconds: 1));
    try {
      Get.offNamedUntil(
        AutoRoutes.appTabWorkflowTabs,
        id: SharedNavBarEnum.workflow.navigatorId,
        (route) => route.settings.name == AutoRoutes.appTabWorkflowTabs,
      );
      if (Get.isRegistered<TabsController>()) {
        Get.find<TabsController>().showBar();
      }
      // await Get.offNamedUntil(AutoRoutes.appTabWorkflowTabs, (route) => route.settings.name == AutoRoutes.workflow, id: NavBarEnum.workflow.navigatorId);
    } catch (e) {
      // await Get.offAllNamed(url, id: NavBarEnum.workflow.navigatorId);
    }
  }

  /// 基本メソット
  Future<bool> baseAction(
    String alertText,
    bool isSaveAction,
    Map<String, List<AssetItemListModel>> itemDataDict,
    Future<void> Function()? callback,
    Future<void> Function()? noAction, {
    bool isCustomizeViewComponent = false,
  }) async {
    if (isCustomizeViewComponent && !isSaveAction) {
      final map = await checkAndGetData(itemDataDict);
      if (!map) {
        return false;
      }
    }
    bool isExecutionMarker = false;
    await dialogService.show(
      content: '選択した処理を実行しますか？',
      title: alertText,
      cancelText: 'キャンセル',
      onCancel: () async {
        if (noAction != null) {
          await noAction();
        }
        isExecutionMarker = false;
      },
      onConfirm: () async {
        isExecutionMarker = true;
        if (callback != null) {
          try {
            await showLoading(); // 显示加载状态
            await callback();
          } catch (error) {
            LogUtil.e('Error in callback: $error');
            isExecutionMarker = false;
          } finally {
            hideLoading(); // 隐藏加载状态
          }
        }
      },
    );
    return isExecutionMarker;
  }

  /// 共通のフォームデータを作成する
  Map<String, dynamic> getFormData(
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments, {
    bool fromOriginCompare = false,
  }) {
    // 深拷贝 assetDict
    final Map<String, List<AssetItemListModel>> assetDictCopy = Map.from(assetDict);

    final List<AssetItemListModel> assetTypeList = [];
    final Map<String, dynamic> formData = {};

    // 将 assetDict 中的所有 AssetItemListModel 添加到 assetTypeList
    assetDictCopy.forEach((key, value) {
      if (key != 'null') {
        assetTypeList.addAll(value);
      }
    });

    try {
      for (var assetType in assetTypeList) {
        if (assetType.defaultData is List) {
          final List<dynamic> defaultDataList = List<dynamic>.from(assetType.defaultData);
          for (var i = 0; i < defaultDataList.length; i++) {
            if (defaultDataList[i] is Map) {
              final Map<String, dynamic> item = Map<String, dynamic>.from(defaultDataList[i]);
              if (item.containsKey('turl') && item['turl'] != null && item['turl'] != '') {
                item.remove('turl');
              }
              if (item.containsKey('loaded')) {
                item.remove('loaded');
              }
              defaultDataList[i] = item;
            }
          }
          assetType.defaultData = defaultDataList;
        }

        switch (assetType.itemName) {
          case 'WF期限':
            formData[assetType.itemIdStr.toString()] = assetType.defaultData ?? '';
            break;
          case 'file':
            formData[assetType.itemIdStr.toString()] = assetType.defaultData != null
                ? json.encode(assetType.defaultData)
                : json.encode([]);
            break;
          case 'master':
          case 'digitalSign':
          case 'image':
            formData[assetType.itemIdStr.toString()] = assetType.defaultData != null
                ? json.encode(assetType.defaultData)
                : json.encode([]);
            break;
          case 'checkbox':
            if (assetType.optionObject?.checkboxMultiFlg == '1') {
              // 多选
              formData[assetType.itemIdStr.toString()] = assetType.defaultData is String
                  ? assetType.defaultData
                  : json.encode(assetType.defaultData);
            } else {
              formData[assetType.itemIdStr.toString()] = assetType.defaultData;
            }
            break;
          case 'number':
          case 'currency':
            formData[assetType.itemIdStr.toString()] = assetType.defaultData?.toString().replaceAll(',', '') ?? '';
            break;
          case 'userSelect':
            formData[assetType.itemIdStr.toString()] = json.encode(assetType.defaultData);
            break;
          case 'groupSelect':
            var data = '[]';
            if (!fromOriginCompare) {
              final defaultData = assetType.defaultData;
              if (defaultData == null || defaultData == '') {
                data = '[]';
              } else if (defaultData is String) {
                data = defaultData;
              } else {
                data = jsonEncode(defaultData);
              }
            } else {
              assetType.valueForShow = '[]';
            }
            formData[assetType.itemIdStr.toString()] = data;
            break;
          default:
            formData[assetType.itemIdStr.toString()] = assetType.defaultData;
        }
      }
    } catch (e) {
      LogUtil.d('getFormData 遍历发生error: $e');
    }
    // 添加评论
    formData['commentItem'] = jsonEncode(
      comments.map((comment) {
        // newInput 为true才会增加，其他的null或false不能传递否则删除comments
        if (comment.newInput == false) comment.newInput = null;
        return comment.toJson();
      }).toList(),
    );
    return formData;
  }

  void updateScanTaskLock(
    String taskId,
    String lockType,
    String alertMessage,
    String progress,
    Map<String, List<AssetItemListModel>> assetDict,
    List<CommentsModel> comments,
    String processInstanceId, {
    Function(bool result)? callBack = null,
  }) async {
    final isValid = await validateAssetItems(assetDict);
    if (!isValid) return;
    await baseAction(alertMessage, false, assetDict, () async {
      try {
        await showLoading();
        // 获取表单数据
        final formData = getFormData(assetDict, comments);
        formData['processInstanceId'] = processInstanceId;
        formData['taskId'] = taskId;
        formData['lockType'] = lockType;

        if (callBack != null) {
          callBack(true);
        }
        try {
          // 调用更新扫描任务锁定的服务
          await workflowActionService.updateScanTaskLock(formData);
        } catch (e) {
          if (callBack != null) {
            callBack(false);
          }
        }
        // 重置flag和备份用的资产list
        workflowActionService.clearLocalData();
        // 延迟返回
        await Future.delayed(const Duration(milliseconds: 500));
        setNavigateBack(
          alertMessage,
          backPage: AutoRoutes.appTabWorkflowTabs,
          id: SharedNavBarEnum.workflow.navigatorId,
        );
        if (callBack != null) {
          callBack(false);
        }
      } finally {
        hideLoading();
      }
    }, () async {});
  }

  Future<bool> validateAssetItems(
    Map<String, List<AssetItemListModel>> assetDict, {
    bool checkPrivateSection = true,
    bool checkReadonly = true,
  }) async {
    // 获取所有资产项
    final List<AssetItemListModel> assetTypeList = [];
    final Map<String, List<AssetItemListModel>> assetDictCopy = Map.from(assetDict);

    // 将资产字典中的所有项添加到列表中
    assetDictCopy.forEach((sectionName, items) {
      assetTypeList.addAll(items);
    });

    // 验证每个资产项
    for (var assetType in assetTypeList) {
      // 检查是否有权限查看私有部分
      if (checkPrivateSection) {
        bool isView = true;
        if (assetType.optionObject?.sectionPrivateGroups != null) {
          isView = await checkIsView(assetType.optionObject!.sectionPrivateGroups!);
        }
        if (!isView) {
          // 没有权限预览，跳过
          continue;
        }
      }

      // 检查是否只读
      if (checkReadonly) {
        final isReadonly = assetType.optionObject?.readonly == '1';
        if (isReadonly) {
          continue;
        }
      }

      // 必填检查
      if (assetType.inputFlg == '1') {
        if (_isEmpty(assetType.defaultData)) {
          await dialogService.show(content: '${assetType.itemName}を設定してください。');
          return false;
        }
      }

      // 复选框验证
      if (assetType.inputFlg == '1' && assetType.itemType == 'checkbox') {
        if (_isInvalidCheckbox(assetType.defaultData)) {
          final String? displayName = assetType.itemDisplayName ?? assetType.itemName;
          await dialogService.show(content: '$displayNameを設定してください。');
          return false;
        }
      }

      // 错误消息检查
      if (assetType.isShowMessageTS != null) {
        await dialogService.show(content: '入力エラーがあるのでご確認ください。');
        return false;
      }

      if (assetType.defaultData != null) {
        // 桁数チェック
        if (assetType.defaultData is String &&
            assetType.optionObject?.maxlength != null &&
            assetType.defaultData.length > int.parse(assetType.optionObject!.maxlength!)) {
          await dialogService.show(content: '${assetType.itemName}を${assetType.optionObject?.maxlength}文字以内に入力してください。');
          return false;
        }

        // 校验数字和通货类型
        if (assetType.itemType == 'number' || assetType.itemType == 'currency') {
          if (!NumberUtils.validateNumericAndCurrencyField(assetType)) {
            final String? displayName = assetType.itemDisplayName ?? assetType.itemName;
            await dialogService.show(content: '「$displayName」の値を、正しく入力してください。');
            return false;
          }
        }

        // メールアドレスの有効性チェック
        if (assetType.itemType == 'email' && !_isValidEmail(assetType.defaultData)) {
          await dialogService.show(content: '${assetType.itemName}のフォーマットが間違っています。');
          return false;
        }
      }
    }

    return true;
  }

  /// 入力したデータのObjectを返す
  Future<bool> checkAndGetData(Map<String, List<AssetItemListModel>> itemDataDict) async {
    final List<AssetItemListModel> assetTypeList = itemDataDict.values.expand((items) => items).toList();
    await getUserRoleUseCaseUseCase(const NoParams());

    for (var assetType in assetTypeList) {
      // 检查是否有权限查看私有部分
      if (assetType.optionObject?.sectionPrivateGroups != null) {
        // 有私有权限设置的情况
        final isView = await checkIsView(assetType.optionObject!.sectionPrivateGroups!);
        if (!isView) continue; // 没有权限查看，跳过此项
      }

      // 检查是否只读
      if (assetType.optionObject?.readonly == '1') {
        // 只读项只需要验证数字和货币类型
        if (!NumberUtils.validateNumericAndCurrencyField(assetType)) {
          return false;
        }
        continue;
      }

      // 必填检查
      if (assetType.inputFlg == '1') {
        if (_isEmpty(assetType.defaultData)) {
          final displayName = assetType.itemDisplayName ?? assetType.itemName;
          await dialogService.show(content: '$displayNameを設定してください。');
          return false;
        }

        // 复选框特殊处理
        if (assetType.itemType == 'checkbox') {
          if (_isInvalidCheckbox(assetType.defaultData)) {
            final displayName = assetType.itemDisplayName ?? assetType.itemName;
            await dialogService.show(content: '$displayNameを設定してください。');
            return false;
          }
        }
      }

      // 错误消息检查
      if (assetType.isShowMessageTS != null) {
        await dialogService.show(content: '入力エラーがあるのでご確認ください。');
        return false;
      }

      if (assetType.defaultData != null) {
        // 长度检查
        if (assetType.defaultData is String &&
            assetType.optionObject?.maxlength != null &&
            assetType.defaultData.length > int.parse(assetType.optionObject!.maxlength!)) {
          await dialogService.show(content: '${assetType.itemName}を${assetType.optionObject?.maxlength}文字以内に入力してください。');
          return false;
        }

        // 数字和货币类型验证
        if (assetType.itemType == 'number' || assetType.itemType == 'currency') {
          if (!NumberUtils.validateNumericAndCurrencyField(assetType)) {
            final displayName = assetType.itemDisplayName ?? assetType.itemName;
            await dialogService.show(content: '「$displayName」の値を、正しく入力してください。');
            return false;
          }
        }

        // 邮箱格式验证
        if (assetType.itemType == 'email' && !_isValidEmail(assetType.defaultData)) {
          await dialogService.show(content: '${assetType.itemName}のフォーマットが間違っています。');
          return false;
        }
      }
    }
    return true;
  }

  Future<bool> checkValidate(List<AssetItemListModel> assetTypeList, bool isFromDetailOrEditPage) async {
    for (var assetType in assetTypeList) {
      if (assetType.isValid == false) {
        await dialogService.show(content: '入力エラーがあるのでご確認ください。');
        return false;
      }

      if (assetType.optionObject?.readonly == '1') {
        if (assetType.inputFlg == '1' && isFromDetailOrEditPage && _isEmpty(assetType.defaultData)) {
          _showValidationMessage(assetType);
          return false;
        }
        if (!NumberUtils.validateNumericAndCurrencyField(assetType)) {
          return false;
        }
        continue;
      }

      if (assetType.inputFlg == '1' && _isEmpty(assetType.defaultData)) {
        _showValidationMessage(assetType);
        return false;
      }

      if (assetType.inputFlg == '1' && assetType.itemType == 'checkbox' && _isInvalidCheckbox(assetType.defaultData)) {
        _showValidationMessage(assetType);
        return false;
      }

      if (assetType.defaultData != null) {
        // 桁数チェック
        if (assetType.defaultData is String &&
            assetType.optionObject?.maxlength != null &&
            assetType.defaultData.length > int.parse(assetType.optionObject!.maxlength!)) {
          await dialogService.show(content: '${assetType.itemName}を${assetType.optionObject?.maxlength}文字以内に入力してください。');
          return false;
        }

        // 数字或货币类型输入位数检查
        if (!NumberUtils.validateNumericAndCurrencyField(assetType)) {
          return false;
        }

        // メールアドレスの有効性チェック
        if (assetType.itemType == 'email' && !_isValidEmail(assetType.defaultData)) {
          await dialogService.show(content: '${assetType.itemName}のフォーマットが間違っています。');
          return false;
        }
      }
    }
    return true;
  }

  bool _isEmpty(dynamic data) {
    return data == null ||
        (data is String && data.trim().isEmpty) ||
        (data is Map && data.isEmpty) ||
        data == '' ||
        data == '[]';
  }

  bool _isInvalidCheckbox(dynamic data) {
    return data == null || data == '' || data == '0' || data == '[]';
  }

  bool _isValidEmail(String? email) {
    if (email == null) return false;
    final RegExp reg = RegExp(r'^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$');
    return reg.hasMatch(email);
  }

  // 错误信息
  Future<void> _showValidationMessage(AssetItemListModel assetType) async {
    final String message = (assetType.itemDisplayName?.isNotEmpty == true)
        ? '${assetType.itemDisplayName}を設定してください。'
        : '${assetType.itemName}を設定してください。';
    await dialogService.show(content: message);
  }

  // パラメータをブール値に変換します。
  // パラメータがブール型の場合、そのブール値をそのまま返します。
  // パラメータが文字列型で空でない場合、文字列をブール値に変換して返します。
  // それ以外の場合、false を返します。
  // value 変換する値
  // 変換後のブール値
  bool customBooleanCheck(dynamic value) {
    if (value is bool) {
      return value;
    } else if (value is String) {
      final trimmedString = value.trim();
      if (trimmedString.isNotEmpty) {
        return trimmedString.toLowerCase() == 'true';
      }
    }
    return false; // 默认返回 false
  }

  /// todo 后续等共同抽出后删除
  bool checkIsView(String sectionPrivateGroups) {
    final userRole = StorageUtils.get<String>(StorageUtils.keyUserRoleList);
    if (userRole == null) return false;
    final roleIdList = _genUserRoleList(userRole)?.map((e) => e.roleId?.toString()).whereType<String>().toList();
    if (roleIdList == null) return false;

    if (sectionPrivateGroups.contains(',')) {
      final splitGroups = sectionPrivateGroups.split(',');
      return splitGroups.any((group) => roleIdList.contains(group));
    }

    return sectionPrivateGroups.isEmpty || roleIdList.contains(sectionPrivateGroups);
  }

  /// 生成用户角色列表
  List<UserRoleResponseData>? _genUserRoleList(String userRole) {
    if (userRole.isEmpty) {
      return null;
    }

    final List<Map<String, dynamic>>? parsedUserRole = _parseUserRole(userRole);
    if (parsedUserRole == null) {
      return null;
    }

    return parsedUserRole.map((e) => UserRoleResponseData.fromJson(e)).toList();
  }

  /// 解析用户角色列表
  List<Map<String, dynamic>>? _parseUserRole(String userRole) {
    if (userRole.isEmpty) {
      return null;
    }

    try {
      final parsed = jsonDecode(userRole);
      if (parsed is List) {
        return parsed.cast<Map<String, dynamic>>();
      }
    } catch (e) {
      // 处理 JSON 解析异常
      LogUtil.e('Failed to parse user role: $e');
      return null;
    }

    return null;
  }

  Future<String?> getDynamicSettingName(String nameKey) async {
    final dynamicSettingNamedic = {
      'today': '今日',
      'now': '現在',
      'valueBlank': '<空白>',
      'currentUserName': 'ログインユーザーのユーザー名',
      'currentUserEmail': 'ログインユーザーのEmail',
      'currentUserInput': '',
    };
    final key = nameKey.split('_');
    var name = dynamicSettingNamedic[key];
    if (name == null) {
      final data = await workflowActionService.getCustomItem();
      name = data[key];
    }
    return name;
  }

  //  ボタンのステータス
  bool getButtonStatus(WorkflowTaskFormResponseModel resultData) {
    final taskType = resultData.taskType;
    final actions = resultData.actions;
    final isPermission = actions?.isPermission ?? false;
    final canBeCommitScanTask = actions?.canBeCommitScanTask ?? false;

    bool tempisPermission = false;
    if ('SCAN' == taskType) {
      tempisPermission = isPermission && canBeCommitScanTask; // スキャンタスクでコミットできるか
    } else {
      tempisPermission = isPermission;
    }

    final saveTemporaryFlg = actions?.isSuspended ?? false;

    final isMyselfInputTask = actions?.isMyselfInputTask ?? false;

    final claimButtonShow = actions?.possibleOfClaim ?? false;

    final unclaimButtonShow = actions?.isPermission ?? false;

    if (tempisPermission || saveTemporaryFlg || isMyselfInputTask || claimButtonShow || unclaimButtonShow) {
      return true; // show button
    }
    return false; // hidden
  }

  ExtractedButtonResult extractWFApprovalOrApplicationRaiseButtons(
    WorkflowButtonNameModel? jsonData,
    bool isApprovalPage,
  ) {
    final String raiseDefaultName = isApprovalPage ? '承認' : '提出';

    if (jsonData == null) {
      return ExtractedButtonResult(
        raiseButtonList: [WorkflowRaiseButtonModel(isCustomizedButtonName: false, nameRaise: raiseDefaultName)],
        turnBackButtonDic: TurnBackButtonInfo(isCustomizedButtonName: false, nameTurnBack: '差戻し'),
        vetoButtonDic: VetoButtonInfo(isCustomizedButtonName: false, nameVeto: '否決'),
      );
    }

    final String? transitButtonName = jsonData.transitButtonName;
    final String? addedButtonFirst = jsonData.addedButtonFirst;
    final String? addedButtonSecond = jsonData.addedButtonSecond;
    final String? rejectButtonName = jsonData.rejectButtonName;
    final String? sendBackButtonName = jsonData.sendBackButtonName;

    final List<WorkflowRaiseButtonModel> raiseButtonsNameArray = [];

    TurnBackButtonInfo turnBackButtonsNameDic = TurnBackButtonInfo(isCustomizedButtonName: false, nameTurnBack: '差戻し');

    VetoButtonInfo vetoButtonsNameDic = VetoButtonInfo(isCustomizedButtonName: false, nameVeto: '否決');

    void pushRaiseButton(String? buttonName) {
      if (buttonName != null && buttonName.isNotEmpty) {
        raiseButtonsNameArray.add(WorkflowRaiseButtonModel(isCustomizedButtonName: true, nameRaise: buttonName));
      }
    }

    void handleSendBackButton(String? buttonName) {
      if (raiseButtonsNameArray.isEmpty) {
        raiseButtonsNameArray.add(WorkflowRaiseButtonModel(isCustomizedButtonName: false, nameRaise: raiseDefaultName));
      }
      if (buttonName != null && buttonName.isNotEmpty) {
        turnBackButtonsNameDic = TurnBackButtonInfo(isCustomizedButtonName: true, nameTurnBack: buttonName);
      }
    }

    void handleRejectButton(String? buttonName) {
      if (raiseButtonsNameArray.isEmpty) {
        raiseButtonsNameArray.add(WorkflowRaiseButtonModel(isCustomizedButtonName: false, nameRaise: raiseDefaultName));
      }
      if (buttonName != null && buttonName.isNotEmpty) {
        vetoButtonsNameDic = VetoButtonInfo(isCustomizedButtonName: true, nameVeto: buttonName);
      }
    }

    pushRaiseButton(addedButtonSecond);
    pushRaiseButton(addedButtonFirst);
    pushRaiseButton(transitButtonName);

    handleSendBackButton(sendBackButtonName);
    handleRejectButton(rejectButtonName);

    return ExtractedButtonResult(
      raiseButtonList: raiseButtonsNameArray,
      turnBackButtonDic: turnBackButtonsNameDic,
      vetoButtonDic: vetoButtonsNameDic,
    );
  }

  /// 用于不确定类型使用jsonDecode避免报错
  Map<String, dynamic> safeDecodeAssetText(dynamic assetText) {
    if (assetText is String && assetText.isNotEmpty) {
      try {
        final decoded = jsonDecode(assetText);
        if (decoded is Map<String, dynamic>) {
          return decoded;
        }
        if (decoded is Map) {
          return Map<String, dynamic>.from(decoded);
        }
      } catch (e) {
        LogUtil.e('workflow jsonDecode error:$e');
      }
    }
    return {};
  }

  Future<void> saveTemporaryFromScanTaskCommon({
    required bool isCountingType,
    required Map<String, List<AssetItemListModel>> assetDict,
    required List<CommentsModel> comments,
    required String processInstanceId,
    required String assetListId,
    required String taskId,
    required List<dynamic> assignDynamicData,
    required bool isUserTask,
  }) async {
    final isBaseAction = await baseAction('一時保存', false, assetDict, () async {}, () async {});
    if (!isBaseAction) {
      return;
    }
    try {
      await showLoading();
      final formData = getFormData(assetDict, comments);
      formData['processInstanceId'] = processInstanceId;
      formData['taskId'] = taskId;
      if (assignDynamicData.length > 0) formData['assignDynamicData'] = jsonEncode(assignDynamicData);
      formData['assetListId'] = assetListId;
      if (isUserTask) {
        await workflowActionService.saveTemporaryFormData(formData);
      } else {
        if (isCountingType) {
          formData['eachAssetList'] = jsonEncode([]);
        } else {
          formData['assetIds'] = jsonEncode([]);
        }
        await workflowActionService.saveTemporaryFromScanTaskWorkFlow(formData);
      }
    } finally {
      hideLoading();
    }
    setNavigateBack('保存', backPage: AutoRoutes.appTabWorkflowTabs, id: SharedNavBarEnum.workflow.navigatorId);
  }

  /// フォーム情報
  String getVariables(Map<String, List<AssetItemListModel>> assetDict, dynamic comments) {
    final List<AssetItemListModel> assetTypeList = [];
    final Map<String, dynamic> formDic = {};
    assetDict.forEach((key, value) {
      assetTypeList.addAll(value);
    });

    for (var assetType in assetTypeList) {
      // 删除 turl 字段
      if (assetType.defaultData is List) {
        for (var item in assetType.defaultData) {
          if (item is Map && item.containsKey('turl') && item['turl'] != null && item['turl'] != '') {
            item.remove('turl');
          }
        }
      }
      if (assetType.itemIdStr != null) {
        if (assetType.itemName == 'WF期限') {
          if (assetType.defaultData == 'null' || assetType.defaultData == null) {
            formDic[assetType.itemIdStr!] = '';
          } else {
            formDic[assetType.itemIdStr!] = assetType.itemValue;
          }
        } else if (['master', 'file', 'digitalSign', 'image'].contains(assetType.itemType)) {
          if (isValEmpty(assetType.defaultData, assetType.itemType ?? '')) {
            formDic[assetType.itemIdStr!] = jsonEncode([]);
          } else {
            formDic[assetType.itemIdStr!] = jsonEncode(assetType.defaultData);
          }
        } else if (assetType.itemType == 'checkbox') {
          if (assetType.optionObject != null && assetType.optionObject?.checkboxMultiFlg == '1') {
            formDic[assetType.itemIdStr!] = jsonEncode(assetType.defaultData);
          } else {
            formDic[assetType.itemIdStr!] = assetType.defaultData;
          }
        } else if (assetType.itemType == 'userSelect') {
          formDic[assetType.itemIdStr!] = jsonEncode(assetType.defaultData);
        } else if (assetType.itemType == 'number' || assetType.itemType == 'currency') {
          formDic[assetType.itemIdStr!] = assetType.defaultData.toString().replaceAll(',', '');
        } else {
          formDic[assetType.itemIdStr!] = assetType.defaultData;
        }
      }
    }

    // コメント
    formDic['commentItem'] = jsonEncode(comments);
    return jsonEncode(formDic);
  }

  /// 判断是否符合提交条件
  /// 返回true时满足条件可以提交
  bool checkDynamicTaskCanSubmit(List<dynamic> dynamicTaskInfo, List<dynamic> dynamicTantousha) {
    if (dynamicTaskInfo.length > 0) {
      if (dynamicTantousha.length > 0) {
        return dynamicTaskInfo.length == dynamicTantousha.length;
      } else {
        return false;
      }
    }
    return true;
  }

  /// 特殊 判断 空 只判断类型为 image | digitalSign | file
  bool isValEmpty(dynamic value, String itemType) {
    const typeList = ['image', 'digitalSign', 'file'];
    if (!typeList.contains(itemType)) {
      return false;
    }
    if (value == null) return true;
    if (value == '') return true;
    if (value == 'null') return true;
    if (value == '{}') return true;
    if (value == '[]') return true;
    if (value is List && value.isEmpty) return true;
    return false;
  }
}
