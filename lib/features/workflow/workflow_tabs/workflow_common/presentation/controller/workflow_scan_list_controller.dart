import 'dart:convert';

import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/core/services/navigation_service.dart';
import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/core/utils/log_utils.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/domain/usecases/get_turl_usecase.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_ai_ocr_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/services/workflow_scan_service.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/domain/usecases/workflow_get_layout_settings_usecase.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/state/workflow_scan_list_state.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WorkflowScanListController extends WorkflowCommonController {
  final WorkflowGetLayoutSettingsUseCase workflowGetLayoutSettingsUseCase;
  final WorkflowCommonScanService workflowCommonScanService;
  final GetTurlUseCase getTurlUseCase;
  final NavigationService navigationService;
  WorkflowScanListController({
    required super.dialogService,
    required super.getUserRoleUseCaseUseCase,
    required this.workflowGetLayoutSettingsUseCase,
    required this.getTurlUseCase,
    required super.workflowActionService,
    required this.workflowCommonScanService,
    required this.navigationService,
  });

  final WorkflowScanListState state = WorkflowScanListState();
  final scrollController = ScrollController();

  /// (isFromNew = false)
  var loadStatus = false.obs;

  /// 页码(isFromNew = false)
  int nextPageNum = 1;

  /// 每次后台返回多少条数据(isFromNew = false)
  final getAmountDataNumber = 20;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    final params = Get.arguments;
    state.isFromNew = params['isFromNew'] ?? true;
  }

  @override
  void onReady() async {
    super.onReady();
    final params = Get.arguments;
    await showLoading();
    state.isEdit.value = params['isEdit'] ?? true;
    state.isUserTask = params['isUserTask'] ?? false;
    state.isCountingType.value = params['isCountingType'] ?? false;
    state.isUpdateAmount = params['isUpdateAmount'] ?? '';
    state.processDefinitionId = params['processDefinitionId'] ?? '';
    state.workflowName = params['workflowName'] ?? '';
    state.stepName = params['stepName'] ?? '';
    state.assetTypeName = params['assetTypeName'] ?? '';
    state.workflowId = params['workflowId'];
    state.scanType = params['scanType'] ?? ScanType.barCode;
    state.backTargetPageURL = params['backPage'] ?? '';
    state.fromPage = params['fromPage'] ?? '';
    state.isPreview = params['isPreview'] ?? false;
    state.inputAssetListFlag = params['inputAssetListFlag'] ?? '';
    state.assetDict = params['assetDict'] ?? <String, List<AssetItemListModel>>{};
    state.comments = params['comments'] ?? <CommentsModel>[];
    state.buttonNameList.value = params['workflowRaiseButtonNameList'] ?? <WorkflowRaiseButtonModel>[];

    state.isMyselfInputTask = params['isMyselfInputTask'] ?? false;
    state.assetTypeId = params['assetTypeId'] ?? '';
    state.taskId = params['taskId'] ?? '';
    state.assetListId = params['assetListId'] ?? '';
    state.workflowScript = params['workflowScript'] ?? '';
    state.processInstanceId = params['processInstanceId'] ?? '';
    state.dynamicTantousha.value = params['dynamicTantousha'] ?? [];
    state.dynamicTaskInfo.value = params['dynamicTaskInfo'] ?? <WorkflowAssignDynamicTaskModel>[];
    var registeredAssetList = <RegisteredAssetListModel>[];
    if (params['registeredAssetList'] is List) {
      registeredAssetList = (params['registeredAssetList'] as List)
          .map((item) => RegisteredAssetListModel.fromJson(item as Map<String, dynamic>))
          .toList();
    }
    try {
      state.mobileLayoutSettings = await workflowGetLayoutSettingsUseCase(state.assetTypeId);
      state.locationInfo = await StorageUtils.getAssetScanLocation();
      if (state.mobileLayoutSettings?.code == 0) {
        final imageTypeArray = _getImageTypeArray();
        if (imageTypeArray != null && registeredAssetList.isNotEmpty) {
          for (var iterator in registeredAssetList) {
            for (var it2 in imageTypeArray) {
              // 如果 assetText 为字符串且不为空，则转换为 Map
              if (iterator.assetText is String && iterator.assetText!.isNotEmpty) {
                final assetText = jsonDecode(iterator.assetText!);
                iterator.assetText = assetText;
              }
              var assetTextElement = iterator.assetText?[it2?.itemName];
              // 修改处理assetText为字符串的情况
              if (assetTextElement is String && assetTextElement.isNotEmpty) {
                assetTextElement = jsonDecode(assetTextElement);
              }
              if (assetTextElement is List) {
                final homeImage = assetTextElement.firstWhere(orElse: () => null, (item) {
                  if (item is Map<String, dynamic>) {
                    return item['isHomeImage'] == true;
                  }
                  return false;
                });
                if (homeImage is Map<String, dynamic>) {
                  // 过滤掉没有权限预览的home画像
                  var isView = true;
                  final optionObj = jsonDecode(it2?.option ?? '');
                  final sectionPrivateGroups = optionObj['sectionPrivateGroups'];
                  if (sectionPrivateGroups is String && sectionPrivateGroups.isNotEmpty) {
                    isView = checkIsView(sectionPrivateGroups);
                  }
                  if (!isView) {
                    //没有权限预览
                    continue;
                  }
                  homeImage['turl'] = await getTurlUseCase(homeImage['url'] ?? '');
                  iterator.homeImage = HomeImageModel.fromJson(homeImage);
                  break;
                }
              }
            }
          }
        }
      }
      if (!state.isFromNew) {
        assetListPagePaginationTarget();
      } else {
        state.filterListTemp.value = registeredAssetList;
        state.itemsListTemp = registeredAssetList;
        state.totalCount.value = registeredAssetList.length;
      }
      dealData();
      if (state.backTargetPageURL != '/workflow/new/w1-asset-list/submit') {
        final resultForgetTaskInfo = await workflowActionService.getDynamicTaskInfo(state.processDefinitionId);
        state.isHasTantoushaF = resultForgetTaskInfo.length > 0;
      } else {
        state.isHasTantoushaF = false;
      }
    } catch (e) {
      LogUtil.d('scanList onInit error');
    } finally {
      hideLoading();
    }
  }

  void isFormNewPage() {
    final args = Get.arguments;
    if (args?['fromSubmit'] ?? false) {
      state.isFromSubmit = true;
    } else {
      state.isFromSubmit = false;
    }
  }

  /// 分页资产列表数据组装
  Future<void> assetListPagePaginationTarget() async {
    loadStatus.value = true;
    try {
      final nextPage = nextPageNum - 1;
      // skip 从（开始条数）0或者16是从  例：0～15 16～32 公式：nextPage x getAmountDataNumber
      final skip = nextPage * getAmountDataNumber;
      final getAssetListData = await workflowActionService.getAssetsByKeywordInWfAssetListForAssignScan(
        state.processInstanceId,
        state.taskId,
        state.searchText,
        skip,
        getAmountDataNumber,
      );
      state.totalCount.value = getAssetListData.allAssetCount ?? 0;
      state.assetsCount.value = getAssetListData.allAssetCount ?? 0;

      final wfAssetList = getAssetListData.assetListDatas ?? [];
      if (nextPage == 0) {
        state.filterListTemp.value = wfAssetList;
      } else {
        state.filterListTemp.addAll(wfAssetList);
      }
      dealData();
      await setHomeImageForRegisterList();
      state.moreThenLimit.value = !(wfAssetList.length != 0 && wfAssetList.length % getAmountDataNumber == 0);
    } finally {
      loadStatus.value = false;
    }
  }

  void onLoadMoreData() async {
    nextPageNum += 1;
    await assetListPagePaginationTarget();
  }

  Future<void> setHomeImageForRegisterList() async {
    if (state.mobileLayoutSettings?.code == 0) {
      final imageTypeArray = _getImageTypeArray();
      if (imageTypeArray != null && state.filterListTemp.isNotEmpty) {
        for (var iterator in state.filterListTemp) {
          // 循环保留assetAmount的原始数字
          iterator.tempAssetCount = iterator.assetAmount.value;
          for (var it2 in imageTypeArray) {
            // 如果 assetText 为字符串且不为空，则转换为 Map
            if (iterator.assetText is String && iterator.assetText!.isNotEmpty) {
              final assetText = jsonDecode(iterator.assetText!);
              iterator.assetText = assetText;
            }
            var assetTextElement = iterator.assetText?[it2?.itemName];
            // 修改处理assetText为字符串的情况
            if (assetTextElement is String && assetTextElement.isNotEmpty) {
              assetTextElement = jsonDecode(assetTextElement);
            }
            if (assetTextElement is List) {
              final homeImage = assetTextElement.firstWhere(orElse: () => null, (item) {
                if (item is Map<String, dynamic>) {
                  return item['isHomeImage'] == true;
                }
                return false;
              });
              if (homeImage is Map<String, dynamic>) {
                // 过滤掉没有权限预览的home画像
                var isView = true;
                final optionObj = jsonDecode(it2?.option ?? '');
                final sectionPrivateGroups = optionObj['sectionPrivateGroups'];
                if (sectionPrivateGroups is String && sectionPrivateGroups.isNotEmpty) {
                  isView = checkIsView(sectionPrivateGroups);
                }
                if (!isView) {
                  //没有权限预览
                  continue;
                }
                homeImage['turl'] = await getTurlUseCase(homeImage['url'] ?? '');
                iterator.homeImage = HomeImageModel.fromJson(homeImage);
                break;
              }
            }
          }
        }
      }
    }
  }

  // データを組み込む
  void dealData() {
    if (state.filterListTemp.isEmpty) {
      return;
    }
    // 处理每个项目
    for (var item in state.filterListTemp) {
      var assetTextObj = item.assetText;
      if (assetTextObj is String) {
        assetTextObj = jsonDecode(assetTextObj);
      }
      if (assetTextObj?['location'] != null && assetTextObj['location'] == '拠点指定なし') {
        assetTextObj['location'] = '';
      }
      item.assetText = assetTextObj;
    }
    if (!state.isCountingType.value) {
      return;
    }
    //是否为个数
    final List<RegisteredAssetListModel> tempRegisteredAssetList = []; // 创建缓存
    // 遍历扫描返回的数据
    for (var element in state.filterListTemp) {
      if (tempRegisteredAssetList.isEmpty) {
        // 缓存数据长度为0，直接添加数据到缓存中
        _initScannedCountWhenErr(element);
        tempRegisteredAssetList.add(element);
        continue;
      }
      final loc = element.assetText['location'] ?? '';
      // 缓存里有数据时才能筛选比较
      final assetItem = tempRegisteredAssetList.firstWhere(
        (item) => item.barcode == element.barcode && (item.assetText?['location'] ?? '') == loc,
        orElse: () => RegisteredAssetListModel(assetScanedCount: 0.obs, assetAmount: 0.obs),
      );
      if (assetItem.assetId != null) {
        // 如果找到相同数据
        if (element.assetScanedCount > assetItem.assetScanedCount.value) {
          // 比较扫描数，选取扫描数大的
          // 删除扫描数小的项
          tempRegisteredAssetList.remove(assetItem);
          tempRegisteredAssetList.add(element); // 添加扫描数大的
        }
      } else {
        // 如果找不到相同数据那么直接加入数据到缓存中
        tempRegisteredAssetList.add(element);
      }
    }

    state.filterListTemp.value = tempRegisteredAssetList; // 将缓存数据赋给数据源
    state.itemsListTemp = tempRegisteredAssetList;
  }

  List<AssetItemListModel?>? _getImageTypeArray() {
    final imageTypeArray = state.mobileLayoutSettings?.assetItemList;
    return imageTypeArray?.where((item) => item?.itemType == 'image' && item?.mobileFlg == '1').toList();
  }

  void _initScannedCountWhenErr(RegisteredAssetListModel element) {
    if (element.assetScanedCount == '') {
      element.assetScanedCount.value = 1;
    }
  }

  /// 全削除のダイアログ
  Future<void> presentClearConfirm() async {
    await dialogService.show(
      content: 'すべて削除しますか？',
      cancelText: 'キャンセル',
      confirmText: 'OK',
      onConfirm: () {
        state.filterListTemp.clear();
        state.itemsListTemp.clear();
      },
    );
  }

  // 個別削除の確認ダイアログ
  Future<void> presentDeleteConfirm(RegisteredAssetListModel asset) async {
    final name = asset.assetText['assetName'] ?? '-';
    await dialogService.show(
      content: '${name}を削除しますか？',
      cancelText: 'キャンセル',
      onConfirm: () async {
        try {
          await showLoading();
          if (!state.isFromNew) {
            final formData = <String, dynamic>{
              'processInstanceId': state.processInstanceId,
              'taskId': state.taskId,
              'barCode': asset.barcode,
              'operation': '3',
              'location': asset.assetText?['location'],
            };
            final result = await workflowActionService.workflowsMobileUpdateByBarCode(formData);
            await createEditAssetList(asset.assetId, result.assetAmountBeforeScan);
          }
          final tempIndex = state.itemsListTemp.indexOf(asset);
          if (tempIndex != -1) {
            state.itemsListTemp.removeAt(tempIndex);
            state.filterListTemp.value = List.from(state.itemsListTemp);
            state.totalCount.value = state.filterListTemp.length;
          }
          if (state.searchText.isNotEmpty) {
            valueChange('');
          }
        } finally {
          hideLoading();
        }
      },
    );
  }

  Future<void> scan() async {
    void extracted() async {
      final dataScan = WorkflowScanBarcodeData(
        assetTypeId: state.assetTypeId,
        locationInfo: state.locationInfo,
        jobType: 'createNewWF',
        isFromNew: false,
        scannedAssetIdList: [], // assetID(同じbarcode、違う場所)
        isCountingType: state.isUpdateAmount == '1' ? false : state.isCountingType.value,
        searchId: state.autoFetchSearchId,
        workflowId: state.workflowId,
        engineId: state.processDefinitionId,
        scannedAssetCount: state.itemsListTemp.length,
        processInstanceId: state.processInstanceId,
        editAssetList: state.editAssetList,
        taskId: state.taskId,
        whatKindWF: NewApplicationWorkflow.newW1FormPage,
        scanType: state.scanType,
        isFromLocationSettingPage: false,
        assetTypeName: state.assetTypeName,
        taskName: state.stepName,
        workflowName: state.workflowName,
      );
      await workflowCommonScanService.startWorkflowScanBarcode(dataScan, (returnValueAfterScan) async {
        final editAssetObj = returnValueAfterScan['editAssetList'];
        await checkEditAssetList(editAssetObj);
        await assetListPagePaginationTarget();
      });
    }

    if (state.assetListId.isEmpty) {
      final formData = {
        'engineId': state.processDefinitionId,
        'processInstanceId': state.processInstanceId,
        'assetTypeId': state.assetTypeId,
      };
      final createAssetList = await workflowActionService.workflowCreateAssetList(formData);
      if (createAssetList != null) {
        state.assetListId = createAssetList.toString();
      }
      extracted();
    } else {
      extracted();
    }
  }

  Future<void> toScan() async {
    void goToNextPage(List<dynamic> registeredAssetList, ScanType scanType) async {
      if (state.mobileLayoutSettings?.code == 0 && registeredAssetList.isNotEmpty) {
        await showLoading();
        try {
          final assetList = registeredAssetList.map((item) => RegisteredAssetListModel.fromJson(item)).toList();
          final imageTypeArray = _getImageTypeArray();
          if (imageTypeArray != null && registeredAssetList.isNotEmpty) {
            for (var iterator in assetList) {
              for (var it2 in imageTypeArray) {
                // 如果 assetText 为字符串且不为空，则转换为 Map
                if (iterator.assetText is String && iterator.assetText!.isNotEmpty) {
                  final assetText = jsonDecode(iterator.assetText!);
                  iterator.assetText = assetText;
                }
                var assetTextElement = iterator.assetText?[it2?.itemName];
                // 修改处理assetText为字符串的情况
                if (assetTextElement is String && assetTextElement.isNotEmpty) {
                  assetTextElement = jsonDecode(assetTextElement);
                }
                if (assetTextElement is List) {
                  final homeImage = assetTextElement.firstWhere(orElse: () => null, (item) {
                    if (item is Map<String, dynamic>) {
                      return item['isHomeImage'] == true;
                    }
                    return false;
                  });
                  if (homeImage is Map<String, dynamic>) {
                    // 过滤掉没有权限预览的home画像
                    var isView = true;
                    final optionObj = jsonDecode(it2?.option ?? '');
                    final sectionPrivateGroups = optionObj['sectionPrivateGroups'];
                    if (sectionPrivateGroups is String && sectionPrivateGroups.isNotEmpty) {
                      isView = checkIsView(sectionPrivateGroups);
                    }
                    if (!isView) {
                      //没有权限预览
                      continue;
                    }
                    homeImage['turl'] = await getTurlUseCase(homeImage['url'] ?? '');
                    iterator.homeImage = HomeImageModel.fromJson(homeImage);
                    break;
                  }
                }
              }
            }
          }
          if (registeredAssetList.isNotEmpty) {
            state.itemsListTemp = assetList;
            state.totalCount.value = assetList.length;
          }
          valueChange('');
        } finally {
          hideLoading();
        }
      }
    }

    final dataScan = WorkflowScanBarcodeData(
      assetTypeId: state.assetTypeId,
      locationInfo: state.locationInfo,
      jobType: 'createNewWF',
      isFromNew: true,
      scannedAssetIdList: state.itemsListTemp.map((item) => item.toJson()).toList(), // assetID(同じbarcode、違う場所)
      isCountingType: state.isUpdateAmount == '1' ? false : state.isCountingType.value,
      searchId: state.autoFetchSearchId,
      workflowId: state.workflowId,
      engineId: state.processDefinitionId,
      scannedAssetCount: state.itemsListTemp.length,
      processInstanceId: state.processDefinitionId,
      editAssetList: [],
      taskId: '',
      whatKindWF: NewApplicationWorkflow.newW1FormPage,
      state: null, // どちらの一覧画面から： 0:申請； 1:承認； 2:全て；
      scanType: state.scanType,
      isFromLocationSettingPage: false,
      assetTypeName: state.assetTypeName,
      taskName: state.stepName,
      workflowName: state.workflowName,
    );
    workflowCommonScanService.startWorkflowScanBarcode(dataScan, (returnValueAfterScan) async {
      goToNextPage(returnValueAfterScan['registeredAssetList'], returnValueAfterScan['scanType']);
    });
  }

  void back() async {
    if (state.backTargetPageURL != '') {
      await next();
    } else if (state.fromPage != '') {
      if (state.isUserTask) {
        backIfAssetListIsEmpty();
      } else {
        navigationService.navigateUntil(
          state.fromPage,
          id: SharedNavBarEnum.workflow.navigatorId,
          arguments: {
            'assetCount': state.totalCount.value,
            'isFromNew': state.isFromNew,
            'editAssetList': state.editAssetList,
          },
        );
      }
    } else {
      navigationService.goBack(
        id: SharedNavBarEnum.workflow.navigatorId,
        result: {'assetCount': state.totalCount.value, 'editAssetList': state.editAssetList},
      );
    }
  }

  Future<void> next() async {
    if (state.isFromNew) {
      if (state.itemsListTemp.isEmpty) {
        await dialogService.show(content: '資産をスキャンしてください', confirmText: '確定');
        return;
      } else {
        if (state.isUserTask) {
          navigationService.goBack(id: SharedNavBarEnum.workflow.navigatorId);
          return;
        }
      }
    } else {
      if (state.isUserTask) {
        backIfAssetListIsEmpty();
        return;
      }
    }
    final queryParams = {
      'registeredAssetList': state.itemsListTemp,
      'assetDict': state.assetDict,
      'assetTypeName': state.assetTypeName,
      'workflowName': state.workflowName,
      'stepName': state.stepName,
      'comments': state.comments,
      'inputAssetListFlag': state.inputAssetListFlag,
      'buttonNameList': state.buttonNameList,
      'isCountingType': state.isCountingType.value,
      'processDefinitionId': state.processDefinitionId,
      'isMyselfInputTask': state.isMyselfInputTask,
      'assetTypeId': state.assetTypeId,
      'taskId': state.taskId,
      'assetListId': state.assetListId,
      'workflowScript': state.workflowScript,
      'processInstanceId': state.processInstanceId,
      'dynamicTantousha': state.dynamicTantousha,
      'dynamicTaskInfo': state.dynamicTaskInfo,
      'isFromNew': state.isFromNew,
      'assetCount': state.totalCount.value,
    };
    if (state.isPreview) {
      // submit
      if (state.isFromSubmit) {
        navigationService.goBack(id: SharedNavBarEnum.workflow.navigatorId, result: queryParams);
      } else {
        await navigationService.navigateTo(
          AutoRoutes.newFormSubmit,
          id: SharedNavBarEnum.workflow.navigatorId,
          arguments: queryParams,
        );
      }
    } else if (state.isHasTantoushaF) {
      if (state.isFromSubmit) {
        navigationService.goBack(id: SharedNavBarEnum.workflow.navigatorId, result: queryParams);
      } else {
        await navigationService.navigateTo(
          AutoRoutes.chooseTantousha,
          id: SharedNavBarEnum.workflow.navigatorId,
          arguments: queryParams,
        );
      }
    } else {
      //submit
      if (state.isFromSubmit) {
        navigationService.goBack(id: SharedNavBarEnum.workflow.navigatorId, result: queryParams);
      } else {
        await navigationService.navigateTo(
          AutoRoutes.newFormSubmit,
          id: SharedNavBarEnum.workflow.navigatorId,
          arguments: queryParams,
        );
      }
    }
  }

  /// userTack 阶段如果资产为空提示文言并返回上一个页面
  void backIfAssetListIsEmpty() async {
    if (state.totalCount.value == 0) {
      await dialogService.show(content: '資産を追加してください', confirmText: 'OK');
    }
    navigationService.goBack(
      id: SharedNavBarEnum.workflow.navigatorId,
      result: {'assetCount': state.totalCount.value, 'editAssetList': state.editAssetList},
    );
  }

  Future<void> doRefresh() async {
    state.filterListTemp.clear();
    state.itemsListTemp.clear();
    state.moreThenLimit.value = true;
    nextPageNum = 1;
    await assetListPagePaginationTarget();
  }

  void clearInputKey() async {
    state.filterListTemp.clear();
    state.itemsListTemp.clear();
    state.searchText = '';
    nextPageNum = 1;
    await assetListPagePaginationTarget();
  }

  Future<void> doSearch() async {
    state.filterListTemp.clear();
    state.itemsListTemp.clear();
    nextPageNum = 1;
    await assetListPagePaginationTarget();
  }

  /// 搜索
  void valueChange(String searchText) {
    // scrollToTop(scrollController);
    state.searchText = searchText;
    if (searchText.isNotEmpty) {
      // 使用原始数据进行过滤
      final List<RegisteredAssetListModel> result = state.itemsListTemp.where((item) {
        final assetName = item.assetText['assetName'] ?? '';
        final identityCode = item.assetText['identityCode'] ?? '';
        return assetName.contains(searchText) || identityCode.contains(searchText);
      }).toList();
      state.filterListTemp.value = result;
    } else {
      // 没有搜索条件时恢复全部数据
      state.filterListTemp.value = state.itemsListTemp;
    }
  }

  /// 手动输入数量
  Future<bool> updateInputCount(
    RegisteredAssetListModel item,
    String value, {
    bool isUpdateAsset = false,
    Map<String, dynamic>? param = null,
    bool needResetAmount = true,
  }) async {
    final result = await workflowActionService.updateCount(item, value);
    return result;
  }

  Future<void> onTapPlusClick(RegisteredAssetListModel item) async {
    if (state.isFromNew) {
      workflowActionService.plusClick(item);
    } else {
      await showLoading();
      try {
        await workflowActionService.plusClick(
          item,
          isUpdateAsset: true,
          param: {
            'processInstanceId': state.processInstanceId,
            'taskId': state.taskId,
            'barCode': item.barcode,
            'operation': '0',
          },
        );
        await createEditAssetList(item.assetId, item.assetAmountBeforeScan);
      } finally {
        hideLoading();
      }
    }
  }

  Future<void> onTapMinusClick(RegisteredAssetListModel item) async {
    if (state.isFromNew) {
      workflowActionService.minusClick(item, isFromCreateNewWF: true);
    } else {
      await showLoading();
      try {
        await workflowActionService.minusClick(
          item,
          isUpdateAsset: true,
          param: {
            'processInstanceId': state.processInstanceId,
            'taskId': state.taskId,
            'barCode': item.barcode,
            'operation': '1',
          },
        );
        await createEditAssetList(item.assetId, item.assetAmountBeforeScan);
      } finally {
        hideLoading();
      }
    }
  }

  Future<void> checkEditAssetList(dynamic editAssetObj) async {
    List<dynamic> editAssetList = [];

    if (editAssetObj != null) {
      if (editAssetObj is String) {
        try {
          editAssetList = jsonDecode(editAssetObj);
        } catch (e) {
          return;
        }
      } else if (editAssetObj is List) {
        editAssetList = editAssetObj;
      }

      if (editAssetList.isNotEmpty) {
        for (var element in editAssetList) {
          final assetId = element['assetId'];
          final initAssetAmount = element['initAssetAmount'];
          await createEditAssetList(assetId, initAssetAmount);
        }
      }
    }
  }

  Future<void> createEditAssetList(int? assetId, int? assetAmountBeforeScan) async {
    final result = await StorageUtils.get<int>(StorageUtils.keyIsDeleteAllAssetAtBefore);
    if (result == 1) return;

    var isInList = false;
    for (var editAsset in state.editAssetList) {
      if (editAsset.assetId == assetId) {
        isInList = true;
        break;
      }
    }

    if (!isInList && assetId != null && assetAmountBeforeScan != null) {
      final editAsset = EditAsset(assetId: assetId, initAssetAmount: assetAmountBeforeScan);
      state.editAssetList.add(editAsset);
    }
  }
}
