import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/bindings/af_customize_view_bindings.dart';
import 'package:asset_force_mobile_v2/features/af_customize_view/presentation/widgets/af_customize_view.dart';
import 'package:asset_force_mobile_v2/features/asset/asset_detail/presentation/widgets/expandable_section.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/bindings/edit_form_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/edit_form_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_comments_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_comments_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/editPage', isConst: false, bindings: [EditFormBinding, AfCustomizeViewBindings])
class EditFormPage extends GetView<EditFormController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          return Text(
            controller.state.workflowName.value,
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w700, fontSize: 20),
            // maxLines: 3,
            textAlign: TextAlign.center,
          );
        }),
        leading: IconButton(onPressed: controller.back, icon: const Icon(Icons.chevron_left)),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Obx(() {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 没放入下面的padding 防止刚进入时コメント突兀
                  const SizedBox(height: 16),
                  if (controller.state.assetDict.isNotEmpty)
                    AfCustomizeView(
                      needEnterValidate: false,
                      instance: '',
                      assetDict: controller.state.assetDict,
                      scene: AfCustomizeViewScene.assetDetail,
                    ),
                  controller.state.isFromAction.value
                      ? const SizedBox()
                      : ExpandableSection(
                          title: 'コメント',
                          hasPaddingTop: false,
                          child: GetBuilder<WorkflowCommentsController>(
                            init: WorkflowCommentsController(comments: controller.state.comments),
                            builder: (controller) {
                              return WorkflowCommentsWidget();
                            },
                          ),
                        ),
                  const SizedBox(height: 20),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }
}
