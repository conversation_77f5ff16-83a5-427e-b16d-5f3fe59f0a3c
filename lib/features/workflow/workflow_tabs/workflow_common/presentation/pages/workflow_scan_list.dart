import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/bindings/workflow_scan_list_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_scan_list_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/scan_list_view_widget.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:asset_force_mobile_v2/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

@GetRoutePage('/workflowScanList', isConst: false, binding: WorkflowScanListBinding)
class WorkflowScanList extends GetView<WorkflowScanListController> {
  @override
  Widget build(BuildContext context) {
    controller.isFormNewPage();
    final TextEditingController _searchController = TextEditingController();
    final _showClearButton = false.obs;
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text('資産リスト'),
        leading: IconButton(icon: const Icon(Icons.chevron_left), onPressed: controller.back),
        actions: [
          InkWell(
            onTap: controller.state.isFromNew ? controller.toScan : controller.scan,
            child: SvgPicture.asset(
              'assets/icons/icon-camera.svg',
              width: 24,
              height: 24,
              colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: InkWell(
              onTap: controller.presentClearConfirm,
              child: SvgPicture.asset(
                'assets/icons/icon-trash.svg',
                width: 24,
                height: 24,
                colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
              ),
            ),
          ),
        ],
        bottom: controller.state.isFromNew
            ? PreferredSize(
                preferredSize: const Size.fromHeight(56),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                  child: SizedBox(
                    height: 40,
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: '何をお探しですか？',
                        prefixIcon: Container(
                          padding: const EdgeInsets.all(8),
                          child: SvgPicture.asset(
                            'assets/icons/icon-search.svg',
                            colorFilter: const ColorFilter.mode(Color(0xFF0B3E86), BlendMode.srcIn),
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                      ),
                      style: const TextStyle(color: Colors.black, fontSize: 16),
                      onChanged: (value) {
                        controller.valueChange(value.trim());
                      },
                    ),
                  ),
                ),
              )
            : null,
      ),
      body: Obx(() {
        return RefreshIndicator(
          onRefresh: () async {
            if (controller.state.isFromNew) {
              await controller.doRefresh();
            }
            return;
          },
          child: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification scrollInfo) {
              if (!controller.state.isFromNew &&
                  !controller.state.moreThenLimit.value && // 还有更多
                  !controller.loadStatus.value && // 没有在加载
                  scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
                controller.onLoadMoreData();
              }
              return false;
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: CustomScrollView(
                controller: controller.scrollController,
                slivers: [
                  if (!controller.state.isFromNew)
                    SliverToBoxAdapter(
                      child: Container(
                        margin: const EdgeInsets.symmetric(vertical: 16),
                        height: 40,
                        width: double.infinity,
                        child: Obx(() {
                          return TextField(
                            controller: _searchController,
                            onSubmitted: (value) {
                              // controller.doSearch();
                            },
                            decoration: InputDecoration(
                              hintText: 'キーワードで検索',
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                              suffixIcon: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  if (_showClearButton.value)
                                    IconButton(
                                      icon: const Icon(Icons.clear, size: 20),
                                      splashRadius: 20,
                                      onPressed: () {
                                        _searchController.clear();
                                        _showClearButton.value = false;
                                        controller.clearInputKey();
                                      },
                                    ),
                                  Container(
                                    decoration: const BoxDecoration(
                                      borderRadius: BorderRadius.only(
                                        topRight: Radius.circular(8),
                                        bottomRight: Radius.circular(8),
                                      ),
                                      color: Color(0xFF0B3E86),
                                    ),
                                    padding: const EdgeInsets.all(8),
                                    child: GestureDetector(
                                      onTap: controller.doSearch,
                                      child: SvgPicture.asset(
                                        Assets.iconsIconSearch,
                                        colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            style: const TextStyle(color: Colors.black, fontSize: 16),
                            onChanged: (value) {
                              _showClearButton.value = value.isNotEmpty;
                              controller.state.searchText = value;
                            },
                          );
                        }),
                      ),
                    )
                  else
                    const SliverToBoxAdapter(child: SizedBox(height: 10)),
                  SliverList(
                    delegate: SliverChildBuilderDelegate(childCount: controller.state.filterListTemp.length + 1, (
                      context,
                      index,
                    ) {
                      if (index < controller.state.filterListTemp.length) {
                        return ScanListItemWidget(asset: controller.state.filterListTemp[index]);
                      } else {
                        if (!controller.state.isFromNew && !controller.state.moreThenLimit.value) {
                          return const Padding(
                            padding: EdgeInsets.symmetric(vertical: 16),
                            child: Center(child: CircularProgressIndicator()),
                          );
                        } else {
                          return const Padding(
                            padding: EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                              '全件表示されました',
                              style: TextStyle(color: Colors.white),
                              textAlign: TextAlign.center,
                            ),
                          );
                        }
                      }
                    }),
                  ),
                ],
              ),
            ),
          ),
        );
      }),
      bottomNavigationBar: Container(
        color: Colors.white,
        child: SafeArea(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(() {
                  return Text('全${controller.state.totalCount.value}件');
                }),
                WorkflowButton(
                  text: '确定',
                  textColor: Colors.white,
                  buttonColor: const Color(0xFF0B3E86),
                  onTapCallback: controller.next,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
