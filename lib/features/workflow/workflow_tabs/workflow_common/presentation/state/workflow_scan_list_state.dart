import 'package:asset_force_mobile_v2/features/shared/data/models/asset/asset_item_response.dart';
import 'package:asset_force_mobile_v2/features/shared/data/models/common/shared_arinfo.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_ai_ocr_type_enum.dart';
import 'package:asset_force_mobile_v2/features/shared/presentation/models/shared_action_asset_ui_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/comments_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_assign_dynamic_task_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_bottom_widget.dart';
import 'package:get/get.dart';

class WorkflowScanListState {
  RxList<RegisteredAssetListModel> filterListTemp = <RegisteredAssetListModel>[].obs;
  // 用于保存搜索前数据
  List<RegisteredAssetListModel> itemsListTemp = <RegisteredAssetListModel>[];
  List<EditAsset> editAssetList = <EditAsset>[]; //为了重置资产信息,把编辑过的资产放到一个list里
  // 個数管理場合
  RxBool isCountingType = false.obs;
  String isUpdateAmount = '';
  AssetItemResponse? mobileLayoutSettings;
  // 資産種類ID
  String assetTypeId = '';
  // タスクID
  String taskId = '';
  // 資産リストID
  String assetListId = '';
  String locationInfo = ''; // 場所情報
  //  フォームデータ
  Map<String, List<AssetItemListModel>> assetDict = {};
  RxList<SharedActionAssetUIModel> showAssetList = <SharedActionAssetUIModel>[].obs;
  List<CommentsModel> comments = [];
  RxList<WorkflowRaiseButtonModel> buttonNameList = <WorkflowRaiseButtonModel>[].obs;
  RxList<WorkflowScanListItem> displayList = <WorkflowScanListItem>[].obs;
  // 用于获取过滤资产抽出条件用的参数
  int? autoFetchSearchId = null;
  String processDefinitionId = '';
  int? workflowId = null;
  ScanType scanType = ScanType.barCode;
  // ワークフロー名前
  String workflowName = '';
  // ステップ名
  String stepName = '';
  // 資産種類名前
  String assetTypeName = '';
  bool isUserTask = false;
  bool isHasTantoushaF = false;
  String backTargetPageURL = '';
  String fromPage = '';
  bool isPreview = false;
  String searchText = '';
  //資産スキャンできるか
  String inputAssetListFlag = '';
  bool isMyselfInputTask = false;
  String workflowScript = '';
  String processInstanceId = '';
  RxList<WorkflowAssignDynamicTaskModel> dynamicTaskInfo = <WorkflowAssignDynamicTaskModel>[].obs;
  RxList dynamicTantousha = [].obs;

  bool isFromSubmit = false;

  RxBool hasScanTask = true.obs;

  RxBool isEdit = true.obs;

  RxInt totalCount = 0.obs;
  RxInt assetsCount = 0.obs;

  /// 是否还有下一页更多数据
  RxBool moreThenLimit = false.obs;

  bool isFromNew = true;

  List<SharedArInfo> sharedArInfo = <SharedArInfo>[]; // 扫描后iOS返回数据（NEW）

  List<int> assetIds = <int>[]; // 取扫描返回的id 再去请求后台资产
}

class WorkflowScanListItem {
  // 用于 表示を減らす' : 'さらに表示
  RxBool isExpanded;
  RxList<ScanListItemModel>? itemsList;
  WorkflowScanListItem({this.itemsList}) : isExpanded = false.obs;
}

class EditAsset {
  final int assetId;
  final int initAssetAmount;

  EditAsset({required this.assetId, required this.initAssetAmount});
}
