import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/repositories/s3_repository.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/workflow_build_row.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BaseScanListWidget extends StatelessWidget {
  final RegisteredAssetListModel asset;
  final RxBool isExpanded;
  final Widget leadingWidget;
  final Widget bottomWidget;
  final Color? scannedColor;
  final Color? scannedTextColor;
  final bool isCountingType;
  final bool hasScanTask;
  final bool isFromNew;
  final List<Widget>? endWidget;

  const BaseScanListWidget({
    required this.asset,
    required this.isExpanded,
    required this.leadingWidget,
    required this.bottomWidget,
    this.scannedColor,
    this.scannedTextColor,
    this.isCountingType = true,
    this.hasScanTask = false,
    this.isFromNew = true,
    this.endWidget,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final bool needsExpandButton = (asset.assetItemList ?? []).length > 10;
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Obx(() {
        final ScanListTheme theme = (asset.scanState ?? ''.obs).value == '1'
            ? ScanListTheme(
                backgroundColor: scannedColor,
                textColor: scannedTextColor,
                borderColor: scannedTextColor == Colors.white ? scannedTextColor : Colors.blue.shade800,
              )
            : ScanListTheme(
                backgroundColor: AppTheme.lightTheme.customTheme.cardBackgroundColor,
                textColor: const Color(0xFF646464),
                borderColor: Colors.blue.shade800,
              );
        return Container(
          decoration: BoxDecoration(
            color: theme.backgroundColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [BoxShadow(color: Colors.black.withAlpha(10), blurRadius: 8, offset: const Offset(0, 2))],
          ),
          child: IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                leadingWidget,
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Flexible(
                              child: Obx(() {
                                final bool isExpandedValue = isExpanded.value;
                                List<Widget> showItemList = [];
                                if ((asset.assetItemList ?? []).length <= 10 || isExpandedValue) {
                                  showItemList = (asset.assetItemList ?? []).map((item) {
                                    if (item.itemDisplayName != null || item.value != null)
                                      return WorkflowBuildRow(
                                        textColor: theme.textColor,
                                        title: item.itemDisplayName!,
                                        value: item.value!,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        maxLines: 3,
                                      );
                                    return const SizedBox.shrink();
                                  }).toList();
                                } else {
                                  showItemList = (asset.assetItemList ?? []).sublist(0, 10).map((item) {
                                    if (item.itemDisplayName != null || item.value != null)
                                      return WorkflowBuildRow(
                                        textColor: theme.textColor,
                                        title: item.itemDisplayName!,
                                        value: item.value!,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        maxLines: 3,
                                      );
                                    return const SizedBox.shrink();
                                  }).toList();
                                }
                                if (endWidget != null) {
                                  for (var widget in endWidget!) {
                                    showItemList.add(widget);
                                  }
                                } else {
                                  showItemList.add(
                                    WorkflowBuildRow(
                                      textColor: theme.textColor,
                                      title: '数量',
                                      value: isCountingType && !hasScanTask
                                          ? (isFromNew
                                                ? asset.assetScanedCount.value.toString()
                                                : asset.assetAmount.value.toString())
                                          : '${asset.savedAssetAmount ?? 0} /${asset.assetAmount.value}',
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                    ),
                                  );
                                }

                                return Column(children: showItemList);
                              }),
                            ),
                            if ((asset.homeImage?.turl ?? '').isNotEmpty) _buildImage(),
                          ],
                        ),
                        bottomWidget,
                        if (needsExpandButton) _buildExpandButton(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Image.network(
        asset.homeImage!.turl!,
        width: 50,
        height: 50,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return const Center(child: CircularProgressIndicator());
        },
        errorBuilder: (context, error, stackTrace) {
          Get.find<S3Repository>().getTurl(asset.homeImage!.turl!).then((value) {
            asset.homeImage!.turl = value;
          });
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildExpandButton() {
    return Obx(
      () => InkWell(
        onTap: () {
          isExpanded.value = !isExpanded.value;
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: (asset.scanState ?? ''.obs) == '1' ? Colors.white : AppTheme.homeImageBorderColor,
            ),
          ),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox.shrink(),
                  Text(
                    isExpanded.value ? '表示を減らす' : 'さらに表示',
                    style: TextStyle(color: (asset.scanState ?? ''.obs) == '1' ? Colors.white : Colors.blue.shade800),
                  ),
                  Icon(
                    isExpanded.value ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: (asset.scanState ?? ''.obs) == '1' ? Colors.white : Colors.blue.shade800,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class ScanListTheme {
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;

  ScanListTheme({
    this.backgroundColor = const Color(0xD9FFFFFF),
    this.textColor = const Color(0xFF646464),
    this.borderColor = Colors.blue,
  });
}
