import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/controllers/application_scan_list_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/base_scan_list_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ScanListPreviewWidget extends GetView<ApplicationScanListController> {
  final RegisteredAssetListModel asset;
  final bool isCountingType;
  final bool hasScanTask;
  final _isExpanded = false.obs;

  ScanListPreviewWidget({required this.asset, this.isCountingType = true, this.hasScanTask = false});

  @override
  Widget build(BuildContext context) {
    return BaseScanListWidget(
      asset: asset,
      isExpanded: _isExpanded,
      isCountingType: isCountingType,
      hasScanTask: hasScanTask,
      leadingWidget: const SizedBox.shrink(),
      bottomWidget: const SizedBox.shrink(),
      isFromNew: false,
    );
  }
}
