import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/scan_list_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_scan_list_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/widget/base_scan_list_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ScanListItemWidget extends GetView<WorkflowScanListController> {
  final RegisteredAssetListModel asset;
  final TextEditingController editingController = TextEditingController();
  final _isExpanded = false.obs;

  ScanListItemWidget({required this.asset});

  @override
  Widget build(BuildContext context) {
    editingController.text = controller.state.isFromNew
        ? asset.assetScanedCount.value.toString()
        : asset.assetAmount.value.toString();
    return BaseScanListWidget(
      asset: asset,
      isExpanded: _isExpanded,
      leadingWidget: _buildLeadingWidget(),
      bottomWidget: _bottomNumber(),
      isFromNew: controller.state.isFromNew,
    );
  }

  Widget _buildLeadingWidget() {
    return GestureDetector(
      onTap: () {
        controller.presentDeleteConfirm(asset);
      },
      child: Container(
        width: 40,
        decoration: const BoxDecoration(
          border: Border(right: BorderSide(color: Colors.grey, width: 0.5)),
        ),
        child: const Icon(Icons.delete, color: Colors.black),
      ),
    );
  }

  Widget _bottomNumber() {
    return Container(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Row(
            children: [
              InkWell(
                onTap: () async {
                  await controller.onTapMinusClick(asset);
                  editingController.text = controller.state.isFromNew
                      ? '${asset.assetScanedCount.value}'
                      : '${asset.assetAmount.value}';
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.blue.shade800),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(child: Icon(Icons.remove, color: Colors.blue.shade800)),
                ),
              ),
              Expanded(
                child: Container(
                  height: 50,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  child: Center(
                    child: TextField(
                      controller: editingController,
                      keyboardType: TextInputType.number,
                      textAlign: TextAlign.center,
                      textAlignVertical: TextAlignVertical.center,
                      style: const TextStyle(fontSize: 16),
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(vertical: 0),
                        filled: true,
                        fillColor: Colors.white,
                        border: _inputBorderStyle,
                        enabledBorder: _inputBorderStyle,
                        focusedBorder: _inputBorderStyle,
                      ),
                      onChanged: (value) {
                        controller.updateInputCount(asset, value);
                        editingController.text = '${asset.assetScanedCount.value}';
                      },
                    ),
                  ),
                ),
              ),
              InkWell(
                onTap: () async {
                  await controller.onTapPlusClick(asset);
                  editingController.text = controller.state.isFromNew
                      ? '${asset.assetScanedCount.value}'
                      : '${asset.assetAmount.value}';
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppTheme.lightTheme.primaryColor),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Center(child: Icon(Icons.add, color: Colors.blue.shade800)),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  InputBorder get _inputBorderStyle => OutlineInputBorder(
    borderRadius: BorderRadius.circular(8),
    borderSide: BorderSide(color: Colors.grey.shade400, width: 1),
  );
}
