import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WfComponentFormOutputModal extends StatelessWidget {
  final Map<String, dynamic>? queryPar;
  final String? backUrl;
  final Map<String, dynamic>? rawFormData;
  final String? workflowName;
  final String? processInstanceId;
  final String? assetListId;
  final List<dynamic>? assetList;
  final String? assetTypeId;

  const WfComponentFormOutputModal({
    super.key,
    this.queryPar,
    this.backUrl,
    this.rawFormData,
    this.workflowName,
    this.processInstanceId,
    this.assetListId,
    this.assetList,
    this.assetTypeId,
  });

  bool get isReportInfo {
    if (rawFormData == null || rawFormData!.isEmpty) {
      return false;
    }

    // 只有 user task 才显示
    final taskType = rawFormData!['taskType'];
    if (!['USER', 'END'].contains(taskType)) {
      return false;
    }

    final reportInfoList = rawFormData!['reportInfoList'];
    if (reportInfoList == null || reportInfoList.isEmpty) {
      return false;
    }

    return true;
  }

  List<dynamic>? get reportInfoList {
    return rawFormData?['reportInfoList'];
  }

  void _onClickFormOutput() {
    if (!isReportInfo) return;

    final navigationExtras = {
      'backUrl': {'backUrl': backUrl, 'obj': queryPar},
      'reportInfoList': reportInfoList,
      'workflowName': workflowName,
      'processInstanceId': processInstanceId,
      'assetListId': assetListId,
      'assetTypeId': assetTypeId,
    };

    Get.toNamed('/workflow/form-report-display', arguments: navigationExtras);
  }

  @override
  Widget build(BuildContext context) {
    if (!isReportInfo) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      height: 54,
      margin: const EdgeInsets.only(left: -1),
      child: TextButton(
        onPressed: _onClickFormOutput,
        style: TextButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: const BorderSide(color: Colors.white),
          ),
          padding: EdgeInsets.zero,
        ),
        child: const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '帳票を作成する',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700, height: 24 / 16),
              ),
              Icon(Icons.chevron_right, color: Colors.white),
            ],
          ),
        ),
      ),
    );
  }
}
