import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WorkflowAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget leading;
  final String title;

  WorkflowAppBar({super.key, required this.leading, required this.title});
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).primaryColor,
      child: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 1.0, top: 2),
              child: Container(height: 37, width: 37, child: leading, alignment: Alignment.topRight),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Center(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                      overflow: TextOverflow.visible,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: null,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 37),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize {
    double textHeit = kToolbarHeight;
    // 计算文本所需的高度
    final textPainter = TextPainter(
      text: TextSpan(
        text: title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w700,
          color: Colors.white,
          overflow: TextOverflow.visible,
        ),
      ),
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
      maxLines: null,
    )..layout(maxWidth: Get.width - 78);
    if (textPainter.height + 32 > 56) {
      textHeit = textPainter.height + 32;
    }

    // 返回计算出的高度，加上padding
    return Size.fromHeight(textHeit);
  }
}
