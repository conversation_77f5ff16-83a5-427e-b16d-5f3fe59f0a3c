import 'package:asset_force_mobile_v2/core/storage/storage_utils.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/data/models/workflow_task_form_response_model.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_common_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum WorkflowProcess { newApplication, application, approval }

class WorkflowBottomWidget extends GetView<WorkflowCommonController> {
  final int buttonNum;
  final WorkflowProcess process;
  final bool isNextEnable;
  // 没有资产名多的判断
  final bool? isSubmit;
  final List<WorkflowRaiseButtonModel>? workflowRaiseButtonNameList;
  final Function? save;
  final Function(WorkflowRaiseButtonModel raiseDic)? apply;
  final Function? next;

  /// 申请用
  final WorkflowTaskFormResponseModel? taskFormModel;

  final String? taskType;
  final bool? isPermission;
  // 否決
  final Function(WorkflowRaiseButtonModel raiseDic)? rejection;
  // 差し戻し
  final Function(WorkflowRaiseButtonModel raiseDic)? sendBack;

  WorkflowBottomWidget({
    required this.buttonNum,
    required this.isNextEnable,
    this.workflowRaiseButtonNameList,
    required this.process,
    this.isSubmit,
    this.save,
    this.apply,
    this.next,
    this.isPermission,
    this.taskType,
    this.rejection,
    this.sendBack,
    this.taskFormModel,
  });
  @override
  Widget build(BuildContext context) {
    final type = StorageUtils.get<String>(StorageUtils.keyFontSize);
    final isBigFont = type == null ? false : type == 'big';
    final raiseButtonNameList = <Widget>[
      WorkflowButton(
        text: 'キャンセル',
        moreButtons: buttonNum >= 5 || isBigFont,
        onTapCallback: controller.presentCancelConfirm,
      ),
      if (!(isSubmit ?? false))
        WorkflowButton(text: '一時保存', moreButtons: buttonNum >= 5 || isBigFont, onTapCallback: save),
    ];
    switch (process) {
      case WorkflowProcess.newApplication:
        final secondRowButton = <Widget>[];
        if (isNextEnable)
          secondRowButton.add(
            WorkflowButton(
              text: '次へ',
              moreButtons: isBigFont,
              textColor: Colors.white,
              buttonColor: const Color(0xFF0B3E86),
              onTapCallback: next,
            ),
          );
        if (!isNextEnable && workflowRaiseButtonNameList != null && workflowRaiseButtonNameList!.length > 0)
          for (var raiseButtonDic in workflowRaiseButtonNameList!)
            secondRowButton.add(
              WorkflowButton(
                text: raiseButtonDic.nameRaise,
                moreButtons: isBigFont,
                textColor: Colors.white,
                onTapCallback: () {
                  if (apply != null) apply!(raiseButtonDic);
                },
                buttonColor: const Color(0xFF0B3E86),
              ),
            );
        Widget buttonBar;
        if (buttonNum == 4 && isBigFont || buttonNum > 4) {
          buttonBar = Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: raiseButtonNameList),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: secondRowButton),
            ],
          );
        } else {
          raiseButtonNameList.addAll(secondRowButton);
          buttonBar = Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 10,
            children: raiseButtonNameList,
          );
        }
        return Container(
          color: Colors.white,
          child: SafeArea(
            child: Container(padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5), child: buttonBar),
          ),
        );
      case WorkflowProcess.application:
        // TODO: Handle this case.
        throw UnimplementedError();
      case WorkflowProcess.approval:
        // TODO: Handle this case.
        throw UnimplementedError();
    }
  }
}

class WorkflowButton extends StatefulWidget {
  final String text;
  // 按钮不可点击态
  final bool enable;
  final Color buttonColor;
  final Color tapColor;
  final Color textColor;
  final bool moreButtons;
  final Function? onTapCallback;
  const WorkflowButton({
    super.key,
    required this.text,
    this.enable = false,
    this.buttonColor = Colors.white,
    this.tapColor = const Color(0xFF0B3E86),
    this.textColor = const Color(0xFF0B3E86),
    this.moreButtons = false,
    this.onTapCallback,
  });

  /// 按钮布局
  static List<List<Widget>> distributeButtons(List<Widget> allButtons, bool isBigFont) {
    final buttonCount = allButtons.length;

    // 当按钮数量 <= 4 且不是大字体时，所有按钮放在一行
    if (buttonCount <= 4 && !isBigFont) {
      return [allButtons, []];
    }

    // 当按钮数量 = 5 时，第一行放3个按钮，第二行放2个按钮
    if (buttonCount == 5) {
      return [allButtons.sublist(0, 3), allButtons.sublist(3)];
    }

    // 其他情况（按钮数量 > 4 或字体为大字体），按钮平均分配到两行
    final firstRowCount = (buttonCount + 1) ~/ 2; // 向上取整，确保第一行按钮数量 >= 第二行
    return [allButtons.sublist(0, firstRowCount), allButtons.sublist(firstRowCount)];
  }

  @override
  _WorkflowButtonState createState() => _WorkflowButtonState();
}

class _WorkflowButtonState extends State<WorkflowButton> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: ElevatedButton(
        style: ButtonStyle(
          padding: const WidgetStatePropertyAll(EdgeInsets.zero),
          side: WidgetStateProperty.all(
            BorderSide(
              color: widget.enable
                  ? Colors.grey
                  : widget.textColor == Colors.white
                  ? const Color(0xFF0B3E86)
                  : widget.textColor,
            ),
          ),
          backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (widget.enable) {
              return Colors.grey.shade200; // 禁用时的背景色
            }
            if (states.contains(WidgetState.pressed)) {
              return widget.tapColor; // 按下时的背景色
            }
            return widget.buttonColor; // 默认背景色
          }),
          foregroundColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.pressed)) {
              return Colors.white; // 按下时的文字颜色
            }
            return widget.textColor; // 默认文字颜色
          }),
          minimumSize: WidgetStateProperty.all(
            Size(widget.moreButtons ? (Get.width - 30) / 2 : (Get.width - 30) / 3, 36),
          ),
        ),
        onPressed: widget.enable
            ? null
            : () {
                if (widget.onTapCallback != null) widget.onTapCallback!();
              }, //
        child: Text(widget.text, softWrap: false),
      ),
    );
  }
}

class WorkflowRaiseButtonModel {
  bool isCustomizedButtonName;
  String nameRaise;

  WorkflowRaiseButtonModel({required this.isCustomizedButtonName, required this.nameRaise});
}

class TurnBackButtonInfo {
  bool isCustomizedButtonName;
  String nameTurnBack;

  TurnBackButtonInfo({required this.isCustomizedButtonName, required this.nameTurnBack});
}

class VetoButtonInfo {
  bool isCustomizedButtonName;
  String nameVeto;

  VetoButtonInfo({required this.isCustomizedButtonName, required this.nameVeto});
}

class ExtractedButtonResult {
  List<WorkflowRaiseButtonModel> raiseButtonList;
  TurnBackButtonInfo turnBackButtonDic;
  VetoButtonInfo vetoButtonDic;

  ExtractedButtonResult({required this.raiseButtonList, required this.turnBackButtonDic, required this.vetoButtonDic});
}
