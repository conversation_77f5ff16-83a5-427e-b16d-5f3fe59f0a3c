import 'package:flutter/material.dart';

class WorkflowBuildRow extends StatelessWidget {
  final String title;
  final String value;
  final CrossAxisAlignment crossAxisAlignment;
  final int? maxLines;
  final Color? textColor;
  const WorkflowBuildRow({
    super.key,
    required this.title,
    required this.value,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.maxLines,
    this.textColor,
  });

  @override
  Widget build(context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: crossAxisAlignment,
        children: [
          // 固定宽度容器用于显示键文本，左对齐
          SizedBox(
            width: 90,
            child: Text(
              title,
              style: TextStyle(color: textColor != null ? textColor : const Color(0xFF646464)),
              textAlign: TextAlign.left,
            ),
          ),
          // 单独的冒号，颜色与键相同
          Text(':', style: TextStyle(color: textColor)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              maxLines: maxLines,
              style: TextStyle(color: textColor != null ? textColor : null),
            ),
          ),
        ],
      ),
    );
  }
}
