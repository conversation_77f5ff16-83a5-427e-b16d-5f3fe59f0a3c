import 'package:flutter/material.dart';

class WorkflowBuildSegmentButton extends StatelessWidget {
  final String text;
  final bool isActive;
  final String? unApprovalCount;
  final bool? isApproval;
  const WorkflowBuildSegmentButton({
    super.key,
    required this.text,
    required this.isActive,
    this.unApprovalCount,
    this.isApproval,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      // color: isActive ? Colors.white : Colors.transparent,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              text,
              style: TextStyle(
                color: isActive ? Colors.blue : Colors.white, // 选中时字体蓝色，否则白色
                fontWeight: FontWeight.bold, // 粗体
              ),
            ),
            unApprovalCount != '0' && unApprovalCount != null && (isApproval ?? false)
                ? Padding(
                    padding: const EdgeInsets.only(left: 4),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: const [BoxShadow(color: Colors.black12, offset: Offset(0, 2), blurRadius: 4)],
                      ),
                      child: Text(
                        unApprovalCount ?? '',
                        style: const TextStyle(fontSize: 13, color: Colors.white, fontWeight: FontWeight.w700),
                      ),
                    ),
                  )
                : const SizedBox(),
          ],
        ),
      ),
    );
  }
}
