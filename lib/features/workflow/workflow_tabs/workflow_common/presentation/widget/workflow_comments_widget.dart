import 'package:asset_force_mobile_v2/core/theme/app_theme.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_common/presentation/controller/workflow_comments_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class WorkflowCommentsWidget extends GetView<WorkflowCommentsController> {
  WorkflowCommentsWidget({super.key});

  @override
  Widget build(context) {
    return Obx(() {
      return Column(
        children: [
          if (controller.showButton)
            buildArrowButton(
              text: 'コメント追加',
              trailingIcon: false,
              onPressed: () {
                controller.addComment(context);
              },
            ),
          if (controller.showToolBar.value)
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: TextField(
                scrollPadding: const EdgeInsets.only(bottom: 48),
                onTap: () => FocusScope.of(context).requestFocus(controller.focusNode),
                focusNode: controller.focusNode,
                controller: controller.textEditingController,
                decoration: InputDecoration(
                  hintText: 'コメントを入力...',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                ),
              ),
            )
          else
            const SizedBox.shrink(),
          for (var index = 0; index < controller.comments.length; index++)
            Container(
              padding: const EdgeInsets.only(bottom: 8.0),
              constraints: const BoxConstraints(minHeight: 52),
              child: Column(
                children: [
                  Container(color: Colors.black, height: 1),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(
                          controller.comments[index].commentContent ?? '',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 4),
                      if (controller.showButton)
                        GestureDetector(
                          onTap: () {
                            controller.deleteComment(index);
                          },
                          child: SvgPicture.asset(
                            width: 20,
                            height: 20,
                            'assets/icons/icon-trash.svg',
                            colorFilter: ColorFilter.mode(Colors.black.withAlpha(180), BlendMode.srcIn),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
        ],
      );
    });
  }

  // af相同组件 todo
  Widget buildArrowButton({
    required String text,
    required VoidCallback? onPressed,
    Color buttonColor = AppTheme.darkBlueColor,
    double arrowSize = 10.0,
    double radius = 8.0,
    bool trailingIcon = true,
    FontWeight? fontWeight,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(right: 5, top: 5, bottom: 5),
      child: TextButton(
        onPressed: onPressed,
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(AppTheme.transparentColor),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(radius),
              side: BorderSide(color: buttonColor),
            ),
          ),
        ),
        child: SizedBox(
          width: double.infinity,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Text(
                text,
                style: TextStyle(color: buttonColor, fontWeight: fontWeight ?? FontWeight.bold),
              ),
              if (trailingIcon)
                Positioned(
                  right: 0,
                  child: Icon(Icons.arrow_forward_ios, size: arrowSize, color: buttonColor),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
