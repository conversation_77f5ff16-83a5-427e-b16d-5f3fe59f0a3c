import 'package:asset_force_mobile_v2/core/routes/auto_route.dart';
import 'package:asset_force_mobile_v2/features/app_tabs/presentations/controllers/tab_controller.dart';
import 'package:asset_force_mobile_v2/features/shared/domain/enums/shared_navbar_enum.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/controllers/workflow_application_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/controllers/workflow_approval_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/controllers/workflow_new_application_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// WorkflowTabsController
/// 用于管理 WorkflowTabsMainPage 的状态和逻辑
class WorkflowTabsController extends GetxController with GetSingleTickerProviderStateMixin {
  late TabController tabController;

  // 当前选中的tab索引
  final RxInt currentTabIndex = 0.obs;

  final RxBool isNewApplicationAscending = true.obs;
  final RxBool isApplicationAscending = true.obs;
  final RxBool isApprovalAscending = true.obs;

  void toggleSortOrder(int index) {
    switch (index) {
      case 0:
        isNewApplicationAscending.value = !isNewApplicationAscending.value;
        break;
      case 1:
        isApplicationAscending.value = !isApplicationAscending.value;
        break;
      case 2:
        isApprovalAscending.value = !isApprovalAscending.value;
        break;
    }
  }

  bool getSortOrder(int index) {
    switch (index) {
      case 0:
        return isNewApplicationAscending.value;
      case 1:
        return isApplicationAscending.value;
      case 2:
        return isApprovalAscending.value;
      default:
        return true;
    }
  }

  String getSortLabel(int index) {
    switch (index) {
      case 0:
        return 'AZ順';
      case 1:
      case 2:
        return '日付順';
      default:
        return '';
    }
  }

  void clickOnBack() {
    final params = Get.arguments;
    if (params is Map && (params['fromHomePage'] ?? false)) {
      final controller = Get.find<TabsController>();
      controller.handleTabTap(SharedNavBarEnum.home);
    }
    Get.offNamed(AutoRoutes.workflow, id: SharedNavBarEnum.workflow.navigatorId);
  }

  void refreshCurrentTab(int index) {
    switch (index) {
      case 0:
        Get.find<WorkflowNewApplicationController>().refreshData();
        break;
      case 1:
        Get.find<WorkflowApplicationController>().refreshData();
        break;
      case 2:
        Get.find<WorkflowApprovalController>().refreshData();
        break;
    }
  }
}
