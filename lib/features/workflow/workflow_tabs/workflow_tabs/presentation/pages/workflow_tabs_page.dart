import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/bindings/workflow_application_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/application/presentation/pages/workflow_application_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/bindings/workflow_approval_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/approval/presentation/pages/workflow_approval_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/bindings/workflow_new_application_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/new_application/presentation/pages/workflow_new_application_page.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_tabs/presentation/bindings/workflow_tabs_binding.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_tabs/presentation/controllers/workflow_tabs_controller.dart';
import 'package:asset_force_mobile_v2/features/workflow/workflow_tabs/workflow_tabs/presentation/widgets/sort_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_auto_router_annotation/annotations.dart';

/// WorkflowTabsMainPage
/// 主页面，包含三个 Tab (新規申請 / 申請 / 承認)。
@GetRoutePage(
  '/app_tab/workflow/tabs',
  bindings: [WorkflowTabsBinding, WorkflowNewApplicationBinding, WorkflowApplicationBinding, WorkflowApprovalBinding],
)
class WorkflowTabsPage extends StatefulWidget {
  const WorkflowTabsPage({super.key});

  @override
  State<WorkflowTabsPage> createState() => _WorkflowTabsPageState();
}

class _WorkflowTabsPageState extends State<WorkflowTabsPage> with SingleTickerProviderStateMixin {
  late TabController tabController;
  final WorkflowTabsController controller = Get.find<WorkflowTabsController>();

  @override
  void initState() {
    super.initState();
    final initialIndex = (Get.arguments as Map<dynamic, dynamic>?)?.cast<String, dynamic>()['initialIndex'] ?? 0;
    tabController = TabController(initialIndex: initialIndex, length: 3, vsync: this);
    tabController.addListener(_handleTabChange);

    // 避免 Obx during build 错误
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.currentTabIndex.value = tabController.index;
    });
  }

  @override
  void dispose() {
    tabController.removeListener(_handleTabChange);
    tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (controller.currentTabIndex.value != tabController.index) {
      controller.currentTabIndex.value = tabController.index;
      controller.isNewApplicationAscending.value = true;
      controller.isApplicationAscending.value = true;
      controller.isApprovalAscending.value = true;
      controller.refreshCurrentTab(tabController.index);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text('ワークフロー'),
        leading: IconButton(
          icon: const Icon(Icons.chevron_left, size: 37),
          onPressed: () {
            controller.clickOnBack();
          },
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Obx(() {
              final currentIndex = controller.currentTabIndex.value;
              return ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 150),
                child: SortButton(
                  label: controller.getSortLabel(currentIndex),
                  isAscending: controller.getSortOrder(currentIndex),
                  onTap: () => controller.toggleSortOrder(currentIndex),
                ),
              );
            }),
          ),
        ],
        bottom: TabBar(
          controller: tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white,
          labelStyle: const TextStyle(fontWeight: FontWeight.bold),
          unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal, fontSize: 16),
          indicatorColor: Colors.white,
          indicatorWeight: 2,
          indicatorSize: TabBarIndicatorSize.tab,
          tabs: const [
            Tab(text: '新規申請'),
            Tab(text: '申請'),
            Tab(text: '承認'),
          ],
        ),
      ),
      body: TabBarView(
        controller: tabController,
        children: const [WorkflowNewApplicationPage(), WorkflowApplicationPage(), WorkflowApprovalPage()],
      ),
    );
  }
}
