import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SortButton extends StatelessWidget {
  final String label;
  final bool isAscending;
  final VoidCallback onTap;

  const SortButton({super.key, required this.label, required this.isAscending, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: onTap,
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (label == 'AZ順')
              SvgPicture.asset(
                isAscending ? 'assets/icons/sort-alpha-down.svg' : 'assets/icons/sort-alpha-down-alt.svg',
                colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                width: 24,
                height: 24,
              )
            else ...[
              const Text('日付順', style: TextStyle(color: Colors.white, fontSize: 16)),
              const SizedBox(width: 4),
              Icon(isAscending ? Icons.arrow_upward : Icons.arrow_downward, color: Colors.white, size: 18),
            ],
          ],
        ),
      ),
    );
  }
}
